{"env": {"browser": true, "es6": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended", "plugin:import/recommended", "prettier", "./.wxt/eslintrc-auto-import.json"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "react-hooks", "import", "jsx-a11y", "prettier"], "settings": {"react": {"version": "detect"}}, "rules": {"react/react-in-jsx-scope": "off", "react/self-closing-comp": "error", "import/no-unresolved": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "no-empty": "off", "react/jsx-no-undef": "off", "react/no-unknown-property": ["error", {"ignore": ["css"]}]}, "globals": {"browser": "readonly"}, "ignorePatterns": [".output/**", ".wxt/**"]}