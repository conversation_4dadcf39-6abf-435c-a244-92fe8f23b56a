name: Build and Release Extension

on:
  push:
    tags:
      - 'v*.*.*'  # 仅在推送标签时触发

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '20'  # 或者你需要的 Node.js 版本

    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

    - name: Install pnpm
      run: npm install -g pnpm

    - name: Install dependencies
      run: pnpm install

    - name: Build extension
      run: pnpm run build

    - name: Create zip file
      run: |
        cd .output/chrome-mv3
        zip -r "../../EasyKol-${{ env.VERSION }}.zip" ./**
        cd ../../

    - name: Upload Release Asset
      uses: actions/upload-artifact@v4
      with:
        name: EasyKol-${{ env.VERSION }}
        path: "EasyKol-${{ env.VERSION }}.zip"

  release:
    needs: build
    runs-on: ubuntu-latest
    steps:
    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV
      
    - name: Install conventional-changelog-cli
      run: npm install -g conventional-changelog-cli

    # - name: Generate Release Notes
    #   id: generate_release_notes
    #   run: |
    #     # 使用 conventional-changelog 生成 release notes
    #     conventional-changelog -p angular -r 2 > RELEASE_NOTES.md
    #     # 读取生成的 release notes 并将其设置为输出
    #     echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
    #     cat RELEASE_NOTES.md >> $GITHUB_OUTPUT
    #     echo "EOF" >> $GITHUB_OUTPUT
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Download artifact
      uses: actions/download-artifact@v4
      with:
        name: EasyKol-${{ env.VERSION }}

    - name: Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        files: "EasyKol-${{ env.VERSION }}.zip"
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Notify Slack Channel
      run: |
        curl -X POST https://hooks.slack.com/triggers/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 \
        -H "Content-Type: application/json" \
        -d '{"version": "${{ env.VERSION }}", "link": "https://github.com/xhx-org/efluns-browser-extension/releases/tag/${{ env.VERSION}}"}'
