interface AiSearchParams{
  platform: string;
  projectId?: string;
  keywords: string[];
  url: string;
  description: string;
  viewCountGte: number;
  regions?: string[];
}

interface AISearchResponse{
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  projectId: string;
  type: string;
  status: string;
  reason: string;
  isTerminated: boolean;
  errors: any | null;
  meta: any | null;
  result: any | null;
  params: AiSearchParams;
}