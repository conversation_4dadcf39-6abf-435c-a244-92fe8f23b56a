import { TaskStatus } from "."
import { Platform } from "./platform"

export interface CardPosition{
	x:number
	y:number
}

// ... existing code ...

export type CardPositionState = Record<Platform, CardPosition>

export interface StatItemProps {
	label: string
	value: string | number
  key:keyof DisplaySettings
  }

  export  interface StatsCardProps {
	// topStats: StatItemProps[]
	// bottomStats: StatItemProps[]
  stats: StatItemProps[]
  }

  export interface AudienceAnalysis{
  userPortraitResult: {
    gender: {
      male: string
      female: string
    }
    age: {
      under18: string
      age18to30: string
      age30to45: string
      above45: string
    }
  }
  regionAnalysisResult: {
    total: number
    statistics: Array<{
      region: string
      count: number 
      percentage: string
      developmentLevel?: "T1" | "T2" | "T3"
    }>
    developmentStatistics: Array<{
      developmentLevel: "T1" | "T2" | "T3"
      count: number
      percentage: string
    }>
  }
  fromCache: boolean,
  needCreateTask?: boolean,
  fakeRadarData?:{
    videoCount: number
    totalUserCount: number
    suspectedFakeRate: string
    totalCommentCount: number
    suspectedFakeCount: number
    avgCommentUserCount: number
    userWithoutCountryRate: string
    userWithoutCountryCount: number
  }
  }

  export type ErrCardType =  'limit' | 'manual' | 'error'

  export interface DisplaySettings{
    estimatedPrice: boolean
    likes: boolean
    views: boolean
    er: boolean
    comments: boolean
  }

  export interface VideoCardSettings{ 
    er: boolean
    plays: boolean
    comments: boolean
    likes: boolean
    favorites: boolean
    publishTime: boolean
    viralMultiplier: boolean
  }

  export interface PricingConfig{
    [Platform.YOUTUBE]: {
      T1: number
      T2: number
      T3: number
    }
    [Platform.TIKTOK]: {
      T1: number
      T2: number
      T3: number
    }
    [Platform.INS]: {
      T1: number
      T2: number
      T3: number
    }
  }

export interface FakeDetectionResult {
  handler:string,
  platform:Platform,
  timestamp:string,
  // 统计信息对象
  statistics: {
    // 网红账户统计
    influencer: {
      sampleCount: number        // 样本中网红账户的数量
      estimatedCount: number     // 估算的网红账户总数
      samplePercentage: number   // 样本中网红账户的百分比
      estimatedPercentage: number // 估算的总数中网红账户的百分比
    }
    // 真实人物账户统计
    realPeople: {
      sampleCount: number        // 样本中真实人物账户的数量
      estimatedCount: number     // 估算的真实人物账户总数
      samplePercentage: number   // 样本中真实人物账户的百分比
      estimatedPercentage: number // 估算的总数中真实人物账户的百分比
    }
    // 虚假账户统计
    fakeAccounts: {
      sampleCount: number        // 样本中虚假账户的数量
      estimatedCount: number     // 估算的虚假账户总数
      samplePercentage: number   // 样本中虚假账户的百分比
      estimatedPercentage: number // 估算的总数中虚假账户的百分比
    }
    sampleTotal: number          // 用于分析的样本总数
    actualFollowerCount: number  // 实际粉丝总数
  }
}


export interface FakeDetection{
  result: FakeDetectionResult
  status:TaskStatus,
  params:any,
  id:string
  errors: string
  meta: string
  createdBy: string
  createdAt: string
  updatedAt: string
  isTerminated: boolean
  reason: string
  type: string
  strategyId: string
  candidate: string
}