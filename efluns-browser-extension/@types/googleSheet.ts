export interface GoogleSheetGetResponse {
  id: string;
  userId: string;
  spreadsheetId: string;
  sheetUrl: string;
  title: string;
  status: string;
  lastSyncAt: string;
  createdAt: string;
  updatedAt: string;
}


export interface GoogleSheetIds{
  youtube:{
    videos: string[]
    shorts: string[]
    urls:string[]
  }
  tiktok:{
    videos:string[],
    urls:string[]
  }
  instagram:{
    posts: string[]
    reels: string[]
    urls:string[]
  }
}

export interface GoogleSheetGetPostLinksResponse {
  data: {
    spreadsheetId: string;
    userId: string;
    links: string[];
  }
}
