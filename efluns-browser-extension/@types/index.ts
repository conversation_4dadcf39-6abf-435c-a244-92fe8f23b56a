export type { EmailTemplate } from './emailTemplate'

export interface Link {
	title: string
	url: string
	icon: string | undefined
}

export * from './youtube'
import type { EmailTemplate } from './emailTemplate'

export interface CreateEmailTemplatePayload extends Pick<EmailTemplate, 'name' | 'subject' | 'content'> {}
export interface UpdateEmailTemplatePayload extends CreateEmailTemplatePayload {
	id: string
}

export enum TaskStatus {
	PENDING = 'PENDING',
	PROCESSING = 'PROCESSING',
	COMPLETED = 'COMPLETED',
	FAILED = 'FAILED',
}

interface Notify{
	id: string
	content: string
	valid: boolean
	createdAt: string
	updatedAt: string
	position: 'SIDEPANEL' | 'INFOCARD'
}

export type Notifies = Notify[]

export interface ModalProps {
	id?: string
	type?: 'confirm' | 'alert' | 'limit' | 'warning'
	title?: string
	content: string | string[]
	onConfirm?: () => void
	onCancel?: () => void
	confirmText?: string
	cancelText?: string
	countdown?: number
	statusCode?: number
	onWarning?: () => void
	warningText?: string
}