import { Platform } from './platform'

// export interface KOL {
// 	id: string
// 	email?: string
// 	platform: Platform
// 	data: {
// 		id?: string
// 		handler?: string
// 	}
// }

export interface UNION_KOL extends Omit<KOL, 'data'> {
	title:string
	description:string
	platformAccount:string
	y2bId:string
	y2bName:string
	score:number
}

export interface SearchKOLParams {
	platform: Platform
	id?: string
	handler?: string
}



export interface KOL {
	id: string
	title: string
	description: string
	platform: Platform
	platformAccount: string
	avatar: string
	email: string | null
	historyEmails: string[]
	y2bId: string
	y2bName: string
	nanoObject: any | null
	createdAt: string
	updatedAt: string
	infoUpdatedAt: string
	y2bObject?:any
	commonFields?:string
}
 export interface SocialMediaIds{
	ins_id: string
	ins_url: string
	twitter_id: string
	twitter_url: string
	youtube_channel_id: string
	youtube_channel_title: string
	youtube_url: string
	reddit_url: string;
	reddit_title:string;
	discord_url: string;
	discord_title: string;
 }

 export interface SEARCH_KOL_RES{
	kolInfo:KOL
	socialMediaIds:SocialMediaIds[]
	region:string
	hasEmail:boolean
 }

 export interface InQueueKOL extends KOL {
	url: string
}


export type EmailSource = "reveal_button" | "front_bio_email" | "user_submit"

export interface ContactInfo {
	type: ContactType
	content: string
	url: string
	depth: number
	updatedAt?: number
	root: string
	linkType: LinkType
	emailSource?: string
  
  }

  export enum ContactType {
	EMAIL = 'email', // 邮箱地址
	PHONE = 'phone', // whatsapp 号码
	SOCIAL = 'social', // 社媒平台链接
  }
  
  export enum LinkType {
	SNAPCHAT = 'snapchat',
	LINKTREE = 'linktree',
	WHATSAPP = 'whatsapp',
	INSTAGRAM = 'instagram',
	YOUTUBE = 'youtube',
	TIKTOK = 'tiktok',
	HOMEPAGE = 'homepage',
	UNKNOWN = 'unknown',
  }


//   export interface KolRelation{
// 	title:"POSTED" | "LIKED" | "CONTACTED",
// 	properties:{
// 		key:string,
// 		value:string,
// 		index:number,
// 		isLink?:boolean
// 	}[]
//   }

  export interface KolRelation {
	title: KolRelationType
	properties: KolRelationProperty[]
	timestamp: number
  }[]
  
  export interface KolRelationProperty {
	key: string
	value: string
	type: 'text'|'link'
	href?: string
	index: number
  }
  
  export type KolRelationType = 'LIKED' | 'CONTACTED' | 'POSTED'