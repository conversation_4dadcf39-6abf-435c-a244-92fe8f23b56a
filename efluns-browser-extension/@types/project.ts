// tslint:disable
/**
 * talent-marketing
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { AiFilterParams } from "./similar"

/**
 *
 * @export
 * @interface Project
 */
export interface Project {
	/**
	 *
	 * @type {string}
	 * @memberof Project
	 */
	id: string
	/**
	 *
	 * @type {string}
	 * @memberof Project
	 */
	title: string
	/**
	 *
	 * @type {string}
	 * @memberof Project
	 */
	description: string
	/**
	 *
	 * @type {string}
	 * @memberof Project
	 */
	createdAt: string
	/**
	 *
	 * @type {string}
	 * @memberof Project
	 */
	updatedAt: string

	/**
	 *
	 * @type {string}
	 * @memberof Project
	 */
	config?: AiFilterParams
}

export interface CreateProjectPayload extends Pick<Project, 'title' | 'description'> {}
