import { KOL } from './kol'
import { Platform } from './platform'

// export interface SimilarTaskResultItem {
// 	aiRankResult: AiRankResult
// 	info: KOL
// }

export interface SimilarTaskResultItem extends KOL {}

export interface AiRankResult {
	similarRate: number
}

export type SimilarReason = "SEARCH" | "LIKE" | "SUPERLIKE" | "DISLIKE"

export interface AiFilterParams {
	allowList: string[]
	banList: string[]
	kolDescription: string
}

export interface SimilarFilters{
	minSubscribers?: number
	maxSubscribers?:number
	lastPublishedDays?: number
	videosAverageViews?: number
	maxVideosAverageViews?:number
	similarScore?:number
	minAverageLikeCount?:number
	maxAverageLikeCount?:number
	regions?:string[]
	taskRound?:number
	ttMode?:1|2
	ttModeReason?:string
	ytbMode?:1|2
	ytbVideoIds?:string[]
	twitterUserNames?:string[]
}

export interface SimilarCreateTaskParams extends SimilarFilters  {
	projectId: string
	source: string
	platform: Platform
	reason?:SimilarReason
	allowList?: string[]
	banList?: string[]
	kolDescription?: string
}

export interface SimilarCreatedResult  {
  id: string
  projectId: string
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  params:SimilarCreateTaskParams
  type:"SIMILAR" | "KEYWORD"
}

// export type SimilarTasksList = SimilarCreatedResult[]

// export type SimilarFilters = Omit<SimilarCreateTaskParams, 'projectId' | 'source' | 'platform'>

