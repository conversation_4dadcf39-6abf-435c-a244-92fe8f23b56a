export interface ttshopResponse {
    code: number;
    message: string;
    next_pagination: {
        has_more: boolean;
        next_page: number;
        search_key: string;
        next_item_cursor: number;
    };
    creator_profile_list?: CreatorProfile[];
    creator_connect_info_list: CreatorConnectInfo[];
}

export interface CreatorProfile {
    /** 创作者唯一ID */
    creator_oecuid: AuthorizedField<string>;
    /** 创作者账号名称 */
    handle: AuthorizedField<string>;
    /** 创作者昵称 */
    nickname: AuthorizedField<string>;
    /** 创作者头像信息 */
    avatar: AuthorizedField<{
        /** 缩略图列表 */
        thumb_url_list: string[];
        /** 原图列表 */
        url_list: string[];
    }>;
    /** 所在地区 */
    selection_region: AuthorizedField<string>;
    /** 粉丝数量 */
    follower_cnt: AuthorizedField<string>;
    /** 创作者分类标签 */
    category: AuthorizedField<{
        /** 分类唯一标识 */
        starling_key: string;
        /** 分类名称 */
        name: string;
    }[]>;
    /** 视频互动率 */
    video_engagement: AuthorizedField<number>;
    /** 视频平均观看数 */
    video_avg_view_cnt: AuthorizedField<string>;
    /** 视频播放中位数 */
    video_play_cnt_med: AuthorizedField<string>;
    /** 粉丝主要年龄段 */
    top_follower_ages: AuthorizedField<string[]>;
    /** 粉丝性别分布 */
    top_follower_gender: AuthorizedField<{
        /** 性别类型 */
        key: string;
        /** 数量 */
        value: string;
    }[]>;
    /** 总销售件数 */
    units_sold: AuthorizedField<string>;
    /** GMV收入范围中位数 */
    med_gmv_revenue_range: AuthorizedField<string>;
    /** GMV收入中位数 */
    med_gmv_revenue?: AuthorizedField<{
        value: string;
        symbol: string;
        format: string;
    }>;
    /** 创作者权限标签 */
    creator_permission_tag: AuthorizedField<number>;
    /** 是否有合作经历 */
    has_collaborated: AuthorizedField<boolean>;
    /** 热门视频数据 */
    top_video_data?: AuthorizedField<{
        /** 视频信息 */
        video: {
            id: string;
            duration: number;
            post_url: string;
            media_type: string;
            /** 视频文件信息 */
            video_infos: {
                main_url: string;
                backup_url: string;
                url_expire: number;
                width: number;
                height: number;
                file_hash: string;
                format: string;
                size: number;
                bitrate: number;
                video_quality: string;
            }[];
        };
        /** 视频标题 */
        name: string;
        /** 发布时间 */
        release_date: string;
        /** 视频ID */
        item_id: string;
        /** 点赞数 */
        like_cnt: number;
        /** 播放数 */
        play_cnt: number;
        /** 评论数 */
        comment_cnt: number;
        /** 视频关联商品 */
        video_products?: {
            /** 商品名称 */
            name: string;
            /** 商品图片 */
            image: string;
            /** 商品ID */
            product_id: string;
            /** 最低售价 */
            min_sale_price: {
                currency: string;
                price: string;
                formatted_price: string;
            };
            /** 最高售价 */
            max_sale_price: {
                currency: string;
                price: string;
                formatted_price: string;
            };
            /** 推广商品数量 */
            promoted_product_num: string;
            /** 商品状态 */
            status: number;
        }[];
    }[]>;

    /** 是否官方推荐 */
    is_official_recommend: AuthorizedField<boolean>;
    /** 是否显示推荐图标 */
    is_show_recom_icon: AuthorizedField<boolean>;
    /** 是否开放账号 */
    is_open_account: AuthorizedField<unknown>;
    /** 主要行业 */
    main_industry: AuthorizedField<unknown>;
    /** 直播平均观看人数 */
    ec_live_avg_uv: AuthorizedField<string>;
    /** 是否快速回复 */
    is_quickly_response: AuthorizedField<unknown>;
    /** 是否活跃创作者 */
    is_active_creator: AuthorizedField<unknown>;
    /** 是否高样本发货率 */
    is_high_sample_dispatch_rate: AuthorizedField<unknown>;
    /** 视频GPM参考值 */
    ec_video_gpm_reference: AuthorizedField<unknown>;
    /** 直播GPM参考值 */
    ec_live_gpm_reference: AuthorizedField<unknown>;
    /** 创作者绑定的MCN机构名称 */
    creator_bind_mcn_name: AuthorizedField<unknown>;
    /** 直播GPM数据 */
    ec_live_gpm: AuthorizedField<{
        minimal: string;
        maximum: string;
        symbol: string;
        minimal_format: string;
        maximum_format: string;
    }>;
    /** 电商视频平均观看数 */
    ec_video_avg_view_cnt: AuthorizedField<string>;
    /** 视频GPM数据 */
    ec_video_gpm: AuthorizedField<{
        minimal: string;
        maximum: string;
        symbol: string;
        minimal_format: string;
        maximum_format: string;
    }>;
    /** 历史最高排名信息 */
    occurred_top_rank: AuthorizedField<{
        /** 排名类型 */
        rank_type: number;
        /** 排名周期 */
        rank_period: number;
        /** 排名内容类型 */
        rank_content_type: number;
        /** 排名日期 */
        rank_date: string;
        /** 排名位置 */
        rank_position: number;
        /** 行业分类 */
        indus_cate: string;
    }>;
    /** 店铺收藏状态 */
    shop_collect_status: AuthorizedField<boolean>;
    /** 是否被店铺拉黑 */
    is_creator_blocked_by_shop: AuthorizedField<boolean>;
    /** 视频GMV */
    video_gmv: AuthorizedField<{
        value: string;
        symbol: string;
        format: string;
    }>;
    /** 直播GMV */
    live_gmv: AuthorizedField<unknown>;
    /** 电商视频互动率 */
    ec_video_engagement: AuthorizedField<number>;
    /** 电商视频播放中位数 */
    ec_video_play_cnt_med: AuthorizedField<string>;
    /** 销售件数范围 */
    units_sold_range: AuthorizedField<unknown>;
    /** 粉丝年龄分布 */
    top_follower_age: AuthorizedField<{
        key: string;
        value: null | string;
    }[]>;
    /** 直播观看中位数 */
    ec_live_med_view_cnt: AuthorizedField<unknown>;
    /** 视频观看中位数 */
    video_med_view_cnt: AuthorizedField<string>;
    /** 电商视频观看中位数 */
    ec_video_med_view_cnt: AuthorizedField<string>;
    /** 视频分享中位数 */
    video_med_share_cnt: AuthorizedField<unknown>;
    /** 推荐原因 */
    recommend_reason: AuthorizedField<unknown>;
    /** 样本履约率 */
    sample_fulfillment_rate: AuthorizedField<unknown>;
    /** 90天内是否被邀请过 */
    has_invited_before_90d: AuthorizedField<boolean>;
}

/** 授权字段包装器 */
interface AuthorizedField<T> {
    /** 字段值 */
    value: T;
    /** 是否已授权 */
    is_authorized: boolean;
    /** 状态码 */
    status: number;
}

/** 创作者联系信息 */
interface CreatorConnectInfo {
    /** 创作者ID */
    creator_id: string;
}

export interface profileDetailResponse {
    code: number;
    message: string;
    creator_profile: ProfileDetail;
}

export interface ProfileDetail {
    /** 创作者唯一ID */
    creator_oecuid: AuthorizedField<string>;
    /** 所在地区 */
    selection_region: AuthorizedField<string>;
    /** 商品数量 */
    product_cnt: AuthorizedField<unknown>;
    /** 视频GPM参考值 */
    ec_video_gpm_reference: AuthorizedField<boolean>;
    /** 直播GPM参考值 */
    ec_live_gpm_reference: AuthorizedField<boolean>;
    /** 直播平均评论数 */
    ec_live_avg_comment_cnt: AuthorizedField<string>;
    /** 直播GPM */
    ec_live_gpm: AuthorizedField<unknown>;
    /** 视频GPM */
    ec_video_gpm: AuthorizedField<unknown>;
    /** 销售件数 */
    units_sold: AuthorizedField<unknown>;
    /** GMV收入中位数 */
    med_gmv_revenue: AuthorizedField<unknown>;
    /** 行业分组 */
    industry_groups: AuthorizedField<{
        key: string;
        value: string;
        name?: string;
    }[]>;
    /** 内容分组 */
    content_groups: AuthorizedField<unknown>;
    /** 商品价格范围 */
    product_price_range: AuthorizedField<string>;
    /** 销售业绩截止时间 */
    sales_performance_end_time: AuthorizedField<string>;
    /** GPM */
    gpm: AuthorizedField<{
        value: string;
        symbol: string;
        format: string;
    }>;
    /** 视频互动率 */
    video_engagement: AuthorizedField<number>;
    /** 视频GMV */
    video_gmv: AuthorizedField<{
        value: string;
        symbol: string;
        format: string;
    }>;
    /** 直播GMV */
    live_gmv: AuthorizedField<unknown>;
    /** 电商视频互动率 */
    ec_video_engagement: AuthorizedField<number>;
    /** 视频平均观看数 */
    video_avg_view_cnt: AuthorizedField<unknown>;
    /** 视频播放中位数 */
    video_play_cnt_med: AuthorizedField<string>;
    /** 电商视频播放中位数 */
    ec_video_play_cnt_med: AuthorizedField<string>;
    /** 每买家平均收入 */
    avg_revenue_per_buyer: AuthorizedField<{
        value: string;
        symbol: string;
        format: string;
    }>;
    /** 合作品牌数量 */
    collaborated_brands_num: AuthorizedField<number>;
    /** GPM参考值 */
    gpm_reference: AuthorizedField<boolean>;

    /** 直播互动率 */
    ec_live_engagement: AuthorizedField<number>;
    /** 直播总互动率 */
    live_engagement: AuthorizedField<number>;
    /** GMV收入范围中位数 */
    med_gmv_revenue_range: AuthorizedField<string>;
    /** GPM范围 */
    gpm_range: AuthorizedField<string>;
    
    /** 视频GPM范围 */
    video_gpm_range: AuthorizedField<string>;
    /** 直播GPM范围 */
    live_gpm_range: AuthorizedField<string>;
    /** 销售件数范围 */
    units_sold_range: AuthorizedField<string>;
    /** 佣金率中位数 */
    med_commission_rate: AuthorizedField<number>;
    /** 合作品牌 */
    partnered_brand: AuthorizedField<{
        id: string;
        name: string;
    }[]> & {
        brand: {
            id: string;
            name: string;
        }[];
    };
    /** 佣金率范围中位数 */
    med_commission_rate_range: AuthorizedField<string>;
    /** 每买家平均收入范围 */
    avg_revenue_per_buyer_range: AuthorizedField<string>;
    /** 推广商品数量 */
    promoted_product_num: AuthorizedField<string>;
    /** 30天直播次数 */
    live_streaming_cnt_30d: AuthorizedField<string>;
    /** 直播观看中位数 */
    live_med_view_cnt: AuthorizedField<string>;
    /** 电商直播观看中位数 */
    ec_live_med_view_cnt: AuthorizedField<string>;
    /** 直播点赞中位数 */
    live_med_like_cnt: AuthorizedField<string>;
    /** 电商直播点赞中位数 */
    ec_live_med_like_cnt: AuthorizedField<string>;
    /** 直播评论中位数 */
    live_med_comment_cnt: AuthorizedField<string>;
    /** 电商直播评论中位数 */
    ec_live_med_comment_cnt: AuthorizedField<string>;
    /** 直播分享中位数 */
    live_med_share_cnt: AuthorizedField<string>;
    /** 电商直播分享中位数 */
    ec_live_med_share_cnt: AuthorizedField<string>;
    /** 30天视频发布数 */
    video_publish_cnt_30d: AuthorizedField<string>;
    /** 视频观看中位数 */
    video_med_view_cnt: AuthorizedField<string>;
    /** 电商视频观看中位数 */
    ec_video_med_view_cnt: AuthorizedField<string>;
    /** 视频点赞中位数 */
    video_med_like_cnt: AuthorizedField<string>;
    /** 电商视频点赞中位数 */
    ec_video_med_like_cnt: AuthorizedField<string>;
    /** 视频评论中位数 */
    video_med_comment_cnt: AuthorizedField<string>;
    /** 电商视频评论中位数 */
    ec_video_med_comment_cnt: AuthorizedField<string>;
    /** 视频分享中位数 */
    video_med_share_cnt: AuthorizedField<string>;
    /** 电商视频分享中位数 */
    ec_video_med_share_cnt: AuthorizedField<string>;
    /** 粉丝性别分布V2 */
    follower_genders_v2: AuthorizedField<unknown>;
    /** 粉丝年龄分布V2 */
    follower_ages_v2: AuthorizedField<unknown>;
    /** 30天电商视频发布数 */
    ec_video_publish_cnt_30d: AuthorizedField<string>;
    /** 30天电商直播次数 */
    ec_live_streaming_cnt_30d: AuthorizedField<string>;
    /** 创作者权限标签 */
    creator_permission_tag: AuthorizedField<number>;
    /** 推荐原因 */
    recommend_reason: AuthorizedField<unknown>;
    /** 样本履约率 */
    sample_fulfillment_rate: AuthorizedField<number>;
    /** 90天内是否被邀请过 */
    has_invited_before_90d: AuthorizedField<unknown>;
}


// 需要用到的数据
// med_gmv_revenue_range
// avg_revenue_per_buyer_range
// gpm_range
// ec_video_publish_cnt_30d