export interface TwitterProfilePage {
    /** Schema.org 上下文 */
    "@context": "http://schema.org";
    /** 页面类型 */
    "@type": "ProfilePage";
    /** 创建日期 */
    dateCreated: string;
    /** 主要内容实体 */
    mainEntity: {
      /** 实体类型 */
      "@type": "Person";
      /** 附加名称（用户名） */
      additionalName: string;
      /** 个人描述 */
      description: string;
      /** 真实姓名 */
      givenName: string;
      /** 居住地 */
      homeLocation: {
        "@type": "Place";
        name: string;
      };
      /** 用户ID */
      identifier: string;
      /** 头像图片 */
      image: {
        "@type": "ImageObject";
        /** 完整图片URL */
        contentUrl: string;
        /** 缩略图URL */
        thumbnailUrl: string;
      };
      /** 互动统计 */
      interactionStatistic: Array<{
        "@type": "InteractionCounter";
        /** 互动类型 */
        interactionType: string;
        /** 统计名称 */
        name: string;
        /** 用户互动数量 */
        userInteractionCount: number;
      }>;
      /** 个人主页URL */
      url: string;
    };
    /** 内容评级 */
    contentRating: string;
    /** 相关链接 */
    relatedLink: string[];
  };