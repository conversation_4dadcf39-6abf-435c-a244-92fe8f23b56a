export interface RequestConfig {
  method: any;
  url: string;
  query?: Record<string, string>;
  headers: Record<string, string>;
  body: any;
  withCredentials?: boolean;
}
//拦截器返回的reponse
export interface ResponseConfig {
  status: number;
  headers: Record<string, string>;
  response: any;
  requestConfig: {
    method: any;
    url: string;
    query?: Record<string, string>;
    headers: Record<string, string>;
    body: any;
    withCredentials?: boolean;
  };
}
