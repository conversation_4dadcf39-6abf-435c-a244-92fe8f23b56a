import { Link } from '..'

export interface YoutubeVideo {
	id: string
	title: string
	description: string
	views: string
	url: string | undefined
	created_time: number
	is_paid_partnership?:boolean
}

export interface YoutubeChannelData {
	id: string
	url: string
	links: Link[]
	emailInDescription: string | null
	videos: YoutubeVideo[]
	region: string | null
}


export interface TiktokChannelData {
	videos: TiktokCardData[]
	isFirst: boolean
	median_number?:number
}


export interface InstagramPost {
	like_count: number
	id:string
	description:string
	comment_count:number
	created_time:number
	publishedAt:number
}
export interface InstagramChannelData {
	posts: InsCardData[],
	isFirst: boolean,
	median_number?:number
}

export interface TiktokCardData {
    collectCount: number
    commentCount: number 
    diggCount: number
    playCount: number
    shareCount: number
    id:string
    created_time:number
	duration:string
	isPinned?: boolean
	is_paid_partnership?:boolean
	is_shop?:boolean
	userList:string[]
	description?:string
}

export interface InsCardData {
    like_count:number
    comment_count:number
    play_count?:number
    code:string,
    created_time:number,
	duration?:string
    isPinned?: boolean
	is_paid_partnership?:boolean
	username?:string
}