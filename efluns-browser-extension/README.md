# 达人营销插件 


[![Build and Release Extension](https://github.com/xhx-org/efluns-browser-extension/actions/workflows/release.yml/badge.svg?branch=main)](https://github.com/xhx-org/efluns-browser-extension/actions/workflows/release.yml)


## 目录

1. [项目概述](#项目概述)
2. [技术栈](#技术栈)
3. [核心模块](#核心模块)
4. [Entrypoints 说明](#entrypoints-说明)
5. [找相似业务流程](#找相似业务流程)



## 项目概述

EasyKOL 是一个达人营销插件项目，用于在 YouTube、TikTok 和 Instagram 等平台上进行 KOL 营销。

- 对内：Efluns(Project Code)
- 对外：EasyKOL(Project Name) 

## 技术栈

- 前端框架: React
- 状态管理: Jotai
- 路由: TanStack Router
- 样式: Emotion, Tailwind CSS
- 构建工具: WXT (Web Extension Tools)
- 查询管理: TanStack Query
- 动画: Framer Motion


## 核心模块

### 入口点 (Entrypoints)
- Entrypoint.content: 内容脚本入口
- KOLInfoCard.content: KOL 信息卡片
- Login: 登录页面
- Main.sidepanel: 主侧边栏面板
- TikTokService.content: TikTok 特定服务

### 服务 (Services)
- AuthService: 认证相关操作
- CommonService: 通用功能
- EmailTemplateService: 邮件模板管理
- KOLService: KOL 相关操作
- PlatformService: 平台特定功能
- ProjectService: 项目管理

## 数据流及状态管理
- 使用 Jotai 进行状态管理
- 主要状态定义: utils/storages.ts
  - 用户会话
  - 项目列表
  - 邮件模板
  - 其他应用状态

## 构建和部署
- 工具: WXT (Web Extension Tools)
- 支持环境:
  - 开发环境
  - 构建环境
  - 打包环境

## 扩展性
- 模块化设计
- 易于添加新平台支持
- 可扩展新功能模块

## 安全性
- 认证机制: Supabase
- 敏感操作需登录

## Entrypoints 说明

### Entrypoint.content

- 概述: 主要负责 EasyKOL Side Panel（侧边栏）的唤起工作。在用户未登录的情况下还作为用户登录入口的功能。
- 位置: entrypoints/Entrypoint.content
- 功能:
  - 在YouTube和TikTok页面注入内容脚本
  - 创建Shadow DOM以隔离样式
  - 渲染主应用程序UI
  - 监听页面导航事件，在URL变化时重新挂载UI
- 关键组件:
  - App.tsx: 主要UI组件，包含登录和"Find Similars"功能
  - utils.tsx: 处理Shadow DOM创建和应用程序渲染
- 技术细节:
  - 使用React和Emotion进行UI渲染
  - 利用浏览器扩展API与后台脚本通信
  - 根据不同平台选择适当的DOM插入位置


## KOLInfoCard.content

- 位置: entrypoints/KOLInfoCard.content
- 功能:
  - 在各平台页面（目前YouTube和TikTok页面）中显示KOL信息卡片
  - 展示KOL信息，并提发送邮件快捷入口
- 关键组件:
  - KOLInfoCard.tsx: 显示KOL信息和操作按钮
  - utils.tsx: 处理KOL信息收集和邮件发送

### Login

- 位置: entrypoints/Login
- 功能:
  - 提供登录页面
  - 处理登录和注册
- 关键组件:
  - Login.tsx: 登录页面UI
  - services/AuthService: 处理登录逻辑

### Main.sidepanel

- 概述: Side Panel 侧边栏是主要承载 EasyKOL 主要功能的区域，业务上的主要流程都在这个模块中进行，例如找相似、管理邮件模板、管理项目等。
- 位置: entrypoints/Main.sidepanel
- 功能:
  - 提供主侧边栏面板
  - 展示KOL信息和操作
- 关键组件:
  - Main.tsx: 主侧边栏面板UI

### TikTokService.content

- 概述: 类似于这种平台 Service 模块，是作为和 EasyKOL 业务模块转化的适配器设计，其主要负责的功能是与当前平台紧密相关的一些数据处理工作（例如抽取数据等），属于辅助模块，TikTokService 是负责从TikTok平台抽取数据。不同的平台按需设计平台服务适配器。
- 位置: entrypoints/TikTokService.content
- 功能:
  - 处理TikTok平台的特定功能
- 关键组件:
  - TikTokService.tsx: 处理TikTok平台的特定功能

### Background

- 概述: 后台脚本是 EasyKOL 业务运行的主要场景和入口。所有的 Services 都会在这里被注册，然后在 Services 中去实现具体的功能。
- 位置: background
- 功能:
  - 处理后台脚本逻辑
  - 与前端页面通信
- 关键组件:
  - background.tsx: 后台脚本逻辑

## 找相似业务流程

找相似的业务在 Main.sidepanel 中实现。在 Main 中核心功能都在 models 中实现，再通过 UI 层中组合，以解耦 UI 与逻辑层。

找相似功能目的是为了收集需要的 KOL，所以大体流程为：
1. 根据上下文信息创建找相似任务
2. 获取任务结果
3. 将任务结果交给 KOLCollector 处理，KOLCollector 会在内部维护一个 KOLQueue 队列，并结合自身功能来满足筛选 KOL 的需求。KOLQueue 会负责维护队列的实时状态，并暴露接口给外部使用，通常由 KOLCollector 来调用。


