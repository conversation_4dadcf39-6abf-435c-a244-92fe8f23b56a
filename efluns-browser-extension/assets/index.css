@import url('./tailwind.css');

[hidden] {
	display: none;
}

@keyframes platformHint {
	0% {
	  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.2);
	}
	70% {
	  box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.2);
	}
	100% {
	  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.2);
	}
  }
  
  .animate-platform-hint {
	animation: platformHint 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}


/* 隐藏Webkit浏览器（Chrome、Safari等）的上下箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 隐藏Firefox浏览器的上下箭头 */
input[type="number"] {
  -moz-appearance: textfield;
}

.btn-primary{
	background-color: #000;
	border: none;
	color: #fff;
}
.btn-primary:hover{
	background-color:  rgb(0 0 0 / 0.8);
}