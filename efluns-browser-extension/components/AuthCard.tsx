import { makeModuleLog } from '@/utils/log'
import clsx from 'clsx'
import { ExternalLink } from 'lucide-react'
import { HTMLAttributes, useState } from 'react'
import { browser } from 'wxt/browser'

const log = makeModuleLog('AuthCard')

export interface AuthCardProps extends HTMLAttributes<HTMLDivElement> {
	onClose?: () => void
}

export default function AuthCard({ children, onClose, className, ...props }: AuthCardProps) {
	const [isLoading, setIsLoading] = useState(false)

	const handleLogin = async () => {
		try {
			setIsLoading(true)
			// 打开 easykol.com/login 页面
			await browser.tabs.create({
				url: 'https://easykol.com/login',
				active: true
			})

			// 关闭当前的登录卡片（如果有关闭回调）
			if (onClose) {
				onClose()
			}
		} catch (error) {
			log('打开登录页面失败：', error)
		} finally {
			setIsLoading(false)
		}
	}

	return (
		<>
			<div className={clsx('w-full h-full flex items-center justify-center', className)} {...props}>
				<div className="card w-full max-w-md shadow-lg bg-base-100">
					<div className="card-body items-center text-center">
						<h1 className="text-3xl font-bold text-nowrap mb-2">
							Login to{' '}
							<span className="bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-violet-500">
								EasyKOL
							</span>
						</h1>
						<button className="btn mt-2 btn-primary btn-wide gap-2" onClick={handleLogin} disabled={isLoading}>
							{isLoading ? (
								<span className="loading loading-spinner" />
							) : (
								<>
									<ExternalLink size={16} />
									Go to Login Page
								</>
							)}
						</button>
					</div>
				</div>
			</div>
		</>
	)
}
