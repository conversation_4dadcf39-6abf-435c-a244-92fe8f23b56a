import { getEmailTemplateService } from '@/services/EmailTemplateService'
import clsx from 'clsx'
import { HTMLAttributes } from 'react'

const log = makeModuleLog('EmailTemplateDropdown')
export interface EmailTemplateDropdownProps extends HTMLAttributes<HTMLSelectElement> {
	onNew?: () => void
	menuButtonClassName?: string
	menuClassName?: string
}

export default function EmailTemplateDropdown({
	className,
	menuButtonClassName,
	menuClassName,
	children,
	onNew,
	...props
}: EmailTemplateDropdownProps) {
	const [data] = useStorageState(emailTemplatesState)
	const [value, setValue] = useStorageState(currentEmailTemplateIdState)
	const currentEmailTemplate = data.find((item) => item.id === value)

	// log({
	// 	data
	// })
	useEffect(() => {
		getEmailTemplateService().refreshEmailTemplates()
	}, [])

	return (
		<div className="dropdown dropdown-end">
			<div tabIndex={0} role="button" className={clsx('btn flex-nowrap text-nowrap ', menuButtonClassName)}>
				{children && <span className="overflow-hidden overflow-ellipsis flex-1 max-w-[27vw]">{children}</span>}
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 0 448 512"
					fill="currentColor"
					css={css`
						width: 16px;
						height: 16px;
					`}
				>
					<path d="M201.4 374.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 306.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z" />
				</svg>
			</div>
			<ul
				key={currentEmailTemplate?.id ?? '--'}
				tabIndex={0}
				className={clsx(' dropdown-content menu bg-base-100 rounded-box z-[1] p-2 shadow ', menuClassName)}
				css={css`
					width: 200px;
				`}
				onClick={(e) => {}}
			>
				<li onClick={() => void setValue(null)} className="">
					<a
						className={clsx({
							active: value === null
						})}
					>
						--
					</a>
				</li>
				{data.map((item) => (
					<li className="w-full mt-1" key={item.id} onClick={() => void setValue(item.id)}>
						<a
							className={clsx('overflow-hidden', {
								active: currentEmailTemplate?.id === item.id
							})}
							css={css`
								white-space: nowrap;
								text-overflow: ellipsis;
								max-width: 180px;
								display: block;
							`}
						>
							{item.name}
						</a>
					</li>
				))}
				<div className="divider my-0" css={css``} />
				<li>
					<a
						className=" flex items-center flex-nowrap text-nowrap"
						onClick={async (e) => {
							e.preventDefault()
							e.stopPropagation()
							onNew?.()
						}}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							className="h-4 w-4 rotate-45"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
						New Template
					</a>
				</li>
			</ul>
		</div>
	)
}
