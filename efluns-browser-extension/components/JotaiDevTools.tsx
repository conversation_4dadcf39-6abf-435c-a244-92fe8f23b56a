import { Global } from '@emotion/react'
import { DevTools } from 'jotai-devtools'
import styles from 'jotai-devtools/styles.css?inline'
import { HTMLAttributes } from 'react'

export interface JotaiDevToolsProps extends HTMLAttributes<HTMLDivElement> {}

export default function JotaiDevTools({ ...props }: JotaiDevToolsProps) {
	return (
		<>
			<Global
				styles={css`
					${styles}
					.internal-jotai-devtools-shell {
						background-color: white;
					}
				`}
			/>
			<DevTools css={css``} />
		</>
	)
}
