import { getEasykolCookieService } from '@/services/EasykolCookieService'
import React from 'react'

interface LimitCardProps {
	hasOverlay?: boolean
	isOpen: boolean
	toggleOpen: (bool: boolean) => void
	classname?: string
	description?: string
	statusCode?: number
}

const LimitCard: React.FC<LimitCardProps> = ({
	hasOverlay = false,
	isOpen = false,
	toggleOpen,
	classname,
	description,
	statusCode
}) => {
	const handleUnlock = async (isFree: boolean) => {
		if (isFree) {
			window.open('https://airy-update.notion.site/EasyKOL-14ae8694012c804595fffc6afe85eefa', '_blank')
		} else {
			// sync session to web
			await getEasykolCookieService().requestCookiePermission()
			window.open('https://easykol.com/settings/quotaQuery', '_blank')
		}
		toggleOpen(false)
	}

	const content = (
		<div
			className={`flex text-[1rem] flex-col items-center justify-center bg-[#d9d9d9] rounded-lg p-4 pt-8 relative w-[90%] max-w-[400px] ${classname || ''}`}
		>
			{description ? (
				<div className="font-bold text-center">{description}</div>
			) : (
				<>
					<div className="font-bold text-center">Today‘s Free Plan</div>
					<div className="font-bold text-center">Has Reached the Limit</div>
				</>
			)}

			{statusCode && statusCode > 1911 ? (
				<button
					onClick={() => {
						handleUnlock(false)
					}}
					className="bg-white text-black px-6 py-1 rounded-lg !mt-6 font-semibold hover:bg-gray-50 transition-colors"
				>
					Check balance
				</button>
			) : (
				<button
					onClick={() => {
						handleUnlock(true)
					}}
					className="bg-white text-black px-6 py-1 rounded-lg !mt-6 font-semibold hover:bg-gray-50 transition-colors"
				>
					Unlock
				</button>
			)}
		</div>
	)

	if (!isOpen) return null

	if (hasOverlay) {
		return (
			<div className="fixed inset-0 z-9999 flex items-center justify-center z-[9999]">
				<div className="fixed inset-0 bg-black bg-opacity-50" />
				{content}
			</div>
		)
	}

	return content
}

export default LimitCard
