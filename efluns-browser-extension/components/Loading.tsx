import clsx from 'clsx'
import { HTMLAttributes } from 'react'

export interface LoadingProps extends HTMLAttributes<HTMLDivElement> {}

export default function Loading({ className, children, ...props }: LoadingProps) {
	return (
		<div className={clsx(className, 'h-full w-full flex flex-col items-center justify-center bg-base-100')} {...props}>
			<div className="loading loading-spinner loading-lg mx-auto"/>
			{children && <div className="mt-4 text-[14px]">{children}</div>}
		</div>
	)
}
