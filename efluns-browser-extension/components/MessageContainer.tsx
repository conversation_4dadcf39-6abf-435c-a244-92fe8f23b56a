import { Message } from "@/@types/easyDm";
import { messagesState } from "@/entrypoints/KOLInfoCard.content/store";
import { motion, AnimatePresence } from "framer-motion";
import { useAtom } from "jotai";
import { useEffect } from "react";

const MessageItem = ({ message }: { message: Message }) => {
  const [, setMessages] = useAtom(messagesState);

  useEffect(() => {
    if (message.duration && message.duration > 0) {
      const timer = setTimeout(() => {
        setMessages((prev) => prev.filter((m) => m.id !== message.id));
      }, message.duration);

      return () => clearTimeout(timer);
    }
  }, [message.id]);

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      className={`
        px-4 py-2 rounded shadow-lg mb-2 flex items-center
        ${message.type === "success" ? "bg-green-500 text-white" : ""}
        ${message.type === "error" ? "bg-red-500 text-white" : ""}
        ${message.type === "warning" ? "bg-yellow-500 text-white" : ""}
        ${message.type === "info" ? "bg-blue-500 text-white" : ""}
        ${message.type === "loading" ? "bg-gray-500 text-white" : ""}
      `}
    >
      {message.type === "loading" && (
        <svg
          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {message.content}
    </motion.div>
  );
};

export default function MessageContainer() {
  const [messages] = useAtom(messagesState);

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[99999] pointer-events-none">
      <AnimatePresence>
        {messages.map((message) => (
          <MessageItem key={message.id} message={message} />
        ))}
      </AnimatePresence>
    </div>
  );
}
