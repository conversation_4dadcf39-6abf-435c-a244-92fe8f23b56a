import { ModalProps } from '@/@types'
import { kolCollector } from '@/entrypoints/Main.sidepanel/models/KOLCollector'
import { getAuthService } from '@/services/AuthService'
import { getEventBusService } from '@/services/EventBusService'
import { useCallback, useEffect, useState } from 'react'
import LimitCard from './LimitCard'

export default function Modal() {
	const [modal, setModal] = useState<ModalProps | null>(null)
	const [visible, setVisible] = useState(false)
	const [countdown, setCountdown] = useState(0)

	const handleModalShow = useCallback((options: ModalProps) => {
		setModal(options)
		setVisible(true)
	}, [])

	const handleModalClose = useCallback((modalId?: string) => {
		if (!modal?.id || modal.id === modalId) {
			setVisible(false)
			setModal(null)
		}
	}, [modal])

	useEffect(() => {
		const eventBus = getEventBusService()
		const unsubscribe = eventBus.subscribe('showModal', handleModalShow)
		const unsubscribeClose = eventBus.subscribe('closeModal', handleModalClose)

		const unsubscribeMessage = onMessage('showEmailAuthModal', async () => {
			const authService = getAuthService()
			const session = await authSessionState.getValue()

			if (!session || !session.user) {
				// No session, need full login
				handleModalShow({
					type: 'confirm',
					content: 'Please login to send emails.',
					confirmText: 'Login',
					async onConfirm() {
						await authService.openLoginPage()
					}
				})
			} else {
				const provider = session.user.app_metadata?.provider
				// Non-Google user or other auth issue - full re-auth
				handleModalShow({
					type: 'confirm',
					content:
						'To send emails, please re-login to grant email permissions. This will open a new tab for authentication.',
					confirmText: 'Re-login',
					async onConfirm() {
						await supabase.auth.signOut()
						await getAuthService().logout(true)
						// Open login page with Gmail scope
						browser.tabs.create({ url: 'https://easykol.com/login?scope=gmail' })
					}
				})
			}
		})

		return () => {
			unsubscribe()
			unsubscribeClose()
			unsubscribeMessage()
		}
	}, [handleModalShow, handleModalClose])

	useEffect(() => {
		if (visible && modal?.type === 'alert' && modal.countdown) {
			setCountdown(modal.countdown)
			const timer = setInterval(() => {
				setCountdown((prev) => {
					if (prev <= 1) {
						clearInterval(timer)
						return 0
					}
					return prev - 1
				})
			}, 1000)

			return () => clearInterval(timer)
		}
	}, [visible, modal])

	const handleConfirm = () => {
		if (modal?.onConfirm) {
			modal.onConfirm()
		}
		setVisible(false)
		setModal(null)
	}

	const handleCancel = () => {
		if (modal?.onCancel) {
			modal.onCancel()
		}
		setVisible(false)
		setModal(null)
	}

	const handleWarning = () => {
		if (modal?.onWarning) {
			modal.onWarning()
		}
		setVisible(false)
		setModal(null)
	}

	if (!visible || !modal) return null

	if (modal.type === 'limit') {
		return (
			<LimitCard
				hasOverlay={true}
				isOpen={visible}
				statusCode={modal.statusCode}
				description={modal.content as string}
				toggleOpen={(bool: boolean) => {
					if (!bool) {
						kolCollector.setLimitModal({
							type: 'limit',
							title: '',
							content: modal.content as string,
							statusCode: modal.statusCode
						})
						setModal(null)
						setVisible(false)
					}
				}}
			/>
		)
	}

	return (
		<dialog className="modal bg-black/50 flex justify-center items-end pb-[20px]" open={visible}>
			<div className="modal-box w-80 px-[1.4rem]">
				{modal.title && <h3 className="font-bold text-lg">{modal.title}</h3>}
				{Array.isArray(modal.content) ? (
					<ul className="list-disc pl-4 py-4">
						{modal.content.map((item, index) => (
							<li key={index}>{item}</li>
						))}
					</ul>
				) : (
					<p className="py-4 whitespace-pre-line">{modal.content}</p>
				)}
				<div className="modal-action">
					<form method="dialog" className="flex gap-2">
						{modal.type === 'warning' && (
							<button
								className="btn btn-sm rounded-full !bg-[#ea3a2e] !text-white hover:!opacity-90"
								onClick={handleWarning}
							>
								{modal.warningText || 'Warning'}
							</button>
						)}
						{modal.type !== 'alert' && (
							<button className="btn btn-ghost btn-sm rounded-full !border-[#c1c0c8]" onClick={handleCancel}>
								{modal.cancelText || 'Cancel'}
							</button>
						)}
						{modal.type !== 'warning' && (
							<button className="btn btn-primary btn-sm rounded-full" onClick={handleConfirm} disabled={countdown > 0}>
								{modal.confirmText || 'Confirm'}
								{countdown > 0 && ` · ${countdown}s`}
							</button>
						)}
					</form>
				</div>
			</div>
		</dialog>
	)
}
