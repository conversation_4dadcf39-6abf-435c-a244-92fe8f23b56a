import { ImgHTMLAttributes, useState } from 'react'
import clsx from 'clsx'

export interface ImageProps extends Omit<ImgHTMLAttributes<HTMLImageElement>, 'src'> {
  /**
   * 图片的源地址
   */
  src?: string
  /**
   * 默认显示的图片地址
   */
  fallback?: string
  /**
   * 包装器的类名
   */
  wrapperClassName?: string
  /**
   * 图片加载时的类名
   */
  loadingClassName?: string
  /**
   * 图片加载完成的类名
   */
  loadedClassName?: string
  /**
   * 图片加载失败的类名
   */
  errorClassName?: string
}

export const MyImage = ({
  src,
  fallback = '/icon/defaultAvatar.png',
  className,
  wrapperClassName,
  loadingClassName,
  loadedClassName,
  errorClassName,
  alt,
  ...props
}: ImageProps) => {
  const [status, setStatus] = useState<'loading' | 'loaded' | 'error'>(src ? 'loading' : 'error')
  const [currentSrc, setCurrentSrc] = useState(src || fallback)

  const handleLoad = () => {
    setStatus('loaded')
  }

  const handleError = () => {
    setStatus('error')
    setCurrentSrc(fallback)
  }

  return (
    <div className={clsx('relative inline-block', wrapperClassName)}>
      {/* 默认图片层 */}
      <img
        {...props}
        className={clsx(
          className,
          status === 'loading' && loadingClassName,
          status === 'error' && errorClassName,
          {'opacity-0':status === 'loaded'}
        )}
        src={fallback}
        alt={alt || 'fallback image'}
      />
      {/* 实际图片层 */}
      {src && (
        <img
          {...props}
          className={clsx(
            'absolute top-0 left-0',
            className,
            status === 'loading' && 'opacity-0',
            status === 'loaded' && [loadedClassName, 'opacity-100'],
            'transition-opacity duration-200'
          )}
          src={currentSrc}
          onLoad={handleLoad}
          onError={handleError}
          alt={alt || 'image'}
        />
      )}
    </div>
  )
}