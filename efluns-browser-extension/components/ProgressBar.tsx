interface ProgressBarProps {
    value: number
    max: number
    foregroundColor?: string
    backgroundColor?: string
    height?: number
    className?: string
  }
  
  export default function ProgressBar({
    value,
    max,
    foregroundColor = "#00b5ff",
    backgroundColor = "#E5E7EB",
    height = 8,
    className = "",
  }: ProgressBarProps) {
    // 确保值在有效范围内
    const safeValue = Math.max(0, Math.min(value, max))
    const percentage = max === 0 ? 0 : (safeValue / max) * 100
  
    return (
      <div
        className={`w-full overflow-hidden rounded-full ${className}`}
        style={{
          backgroundColor,
          height: `${height}px`,
        }}
      >
        <div
          className="h-full"
          style={{
            width: `${percentage}%`,
            backgroundColor: foregroundColor,
          }}
        />
      </div>
    )
  }
  
  