// projects related
export const PROJECTS = '/projects'
export const PRODUCT = '/projects/:id'
export const PROJECT_TERMINATE = '/projects/terminate'
export const PROJECT_TERMINATE_NEW = '/projects/terminateByTaskType'
export const PROJECT_DETAIL = '/projects/:projectId'
export const PROJECT_RESET = '/projects/reset'

// email related
export const EMAIL_TEMPLATES = '/emails/templates'
export const GET_EMAIL_TEMPLATE = '/emails/templates/:id'
export const SEND_EMAIL = '/emails/sendWithPolish'
export const EMAILS = '/emails'
export const EMAILS_OFFICIAL = '/emails/official'

export const CREATE_SIMILARS_TASK = '/similars/create'
export const GET_SIMILAR_TASK = '/similars/:id'
export const LIKE_SIMILAR = '/similars/like'
export const DISLIKE_SIMILAR = '/similars/dislike'
export const RATE_SIMILAR = '/similars/rate'
export const SEARCH_TASKS = 'similars/tasks/:projectId'
export const SEARCH_LASTEST_TASKS = 'similars/latestTask'
export const UNION_SEARCH_SIMILARS = '/similars/singleSearch'
export const UNION_SEARCH_SIMILARS_BY_TIKTOK = '/similars/unionSearch'
export const QUERY_PROJECT_LIKES = '/similars/projectKol/likes'

export const USER_VERTICAL = '/similars/analysis/userVertical'

export const AI_SEARCH_CREATE = '/search/keywordSearch'

export const SEARCH_KOL = '/kols/search'
export const KOL = '/kols/:id'
export const EXPORT_KOLS = '/kols/export'
export const FETCH_KOL_EMAIL = '/kols/:id/fetchEmail'
export const SEARCH_KOLINFO = '/kols/info/search'
export const SEARCH_KOL_EMAIL = '/kols/info/email'
export const SEARCH_KOL_PLATFORM = '/kols/info/platform'
export const KOL_RELATION = '/kols/info/relation'
export const KOL_RELATION2 = '/kols/info/relation-v2'

export const GET_KOL_REIGON = '/kols/info/region'

export const CREDENTIALS = '/credentials'
export const CREDENTIALS_SIGNATURE = '/credentials/signature'

export const TIKTOK_COMMENTS = '/ttInfo/author/comments/:uniqueId'

//三个平台粉丝数量分析
export const TT_AUDIENCE_ANALYSIS = '/ttInfo/audience-analysis'
export const INSTA_AUDIENCE_ANALYSIS = '/ins/audience-analysis'
export const YOUTUBE_AUDIENCE_ANALYSIS = '/ytb/audience-analysis'

export const YTB_PAGE_TYPE = '/ytb/pageType'

export const AUDIENCE_ANALYSIS = '/search/audienceAnalysis'

// 根据单个视频的audience
export const SINGLE_AUDIENCE_ANALYSIS = '/publicationStatistics/plugin/audience'
export const QUERY_SINGLE_AUDIENCE_ANALYSIS = '/publicationStatistics/plugin/audience/:taskId'

export const AUDIENCE_ANALYSIS_EXPORT = '/search/audienceAnalysis/export'

export const NOTIFIES = '/common/notifies'

export const USER_TIME_ZONE = '/userMember/timezone'

// Google Sheet related
export const GOOGLE_SHEET_GET = '/googleSheet/get'
export const GOOGLE_SHEET_SCHEMA_DATA_RENDER = '/googleSheet/schema-data-render/:spreadsheetId' //刷新表格
export const GOOGLE_SHEET_UPDATE_READONLY = '/googleSheet/update-readonly/:spreadsheetId' // 更新只读
export const GOOGLE_SHEET_SYNC_PUBLICATION = '/googleSheet/sync-publication-data/:spreadsheetId' // 同步数据
export const PUBLICATION_UPDATE_VIDEO_DATA = '/publicationStatistics/update-video-data' // 更新视频数据
export const GOOGLE_MONTHLY_REFRESH = '/googleSheet/update-monthly-report-sheet-data/:spreadsheetId' //只更新monthly 的数据
export const GOOGLE_GET_POSTS = '/publicationStatistics/get-post-urls'

export const POST_LINKS_CONTACT = '/common/contact' //批量从链接中获取联系方式
export const PATCH_LINKS = '/kols/links' //给kol patchlinks

export const UPDATE_JUMP_URL = '/common/welcome-page' // 更新跳转链接

// notes
export const GET_TAGS_LIST = '/tagAndNote/list' // 获取标签列表，GET
export const CREATE_TAG = '/tagAndNote/tag' // 创建标签，POST
export const UPDATE_TAG = '/tagAndNote/tag' // 更新标签，PUT
export const DELETE_TAG = '/tagAndNote/tag' // 删除标签，DELETE
export const GET_NOTES_LIST = '/tagAndNote/kol' // 获取 kol 的笔记和标签 GET
export const UPDATE_KOL_TAGS = '/tagAndNote/kol/tag' // 更新 kol 标签 POST
export const UPDATE_KOL_NOTES = '/tagAndNote/kol/note' // 更新 kol 笔记 POST
// export const GET_KOL_TAGS = '/tagAndNote/kol/list' // 查询 kol 标签 GET

// user info
export const GET_USER_INFO = '/userMember/userInfo' // 获取用户信息 GET

// 获取博主系统中的唯一 id ，通过博主的平台 id
export const GET_BLOGGER_UNIQUE_ID = '/kols/id'

export const CREATE_FAKE_DETECTION_TASK = '/ins/audience-fake'
export const QUERY_FAKE_DETECTION_TASK = '/tasks/:taskId'
export const EXPORT_FAKE_DETECTION_TASK = '/ins/audience-fake/export'

// 创建长任务
export const CREATE_LONG_TASK = '/search/longCrawler'
export const UNTERMINATED_TASKS = '/tasks/user/unterminatedTasks'

//判断是否是企业用户
export const INFO_CARD_ACCESS_STATUS = '/userMember/info-card-access-status'
