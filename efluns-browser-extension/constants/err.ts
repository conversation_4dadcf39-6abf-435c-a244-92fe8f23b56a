export const LIMIT = "Today quota is used up, please try again tomorrow."

// 定义自定义状态码
export const StatusCodes = {
    // 成功类状态码
    SUCCESS: 1000, // 成功
    CREATED: 1001, // 资源已创建
    ACCEPTED: 1002, // 请求已接受但未处理完成
    NO_CONTENT: 1003, // 成功但无返回内容
  
    // 客户端错误类状态码
    BAD_REQUEST: 1400, // 错误请求
    UNAUTHORIZED: 1401, // 未授权
    FORBIDDEN: 1403, // 无权限访问
    NOT_FOUND: 1404, // 资源未找到
    CONFLICT: 1409, // 请求冲突（如重复提交）
    UNPROCESSABLE_ENTITY: 1422, // 请求格式正确，但无法处理（如参数验证失败）
    TOKEN_OUTDATED: 1411, // 该账号已在其他设备登录
    VERSION_TOO_LOW: 1426, // 版本过低
    TOO_MANY_REQUESTS: 1429, // 请求频率过高
  
    // 服务器错误类状态码
    SERVER_ERROR: 1500, // 服务器内部错误
    SERVICE_UNAVAILABLE: 1503, // 服务不可用
    GATEWAY_TIMEOUT: 1504, // 网关超时
  
    // 自定义网络状态码
    NETWORK_ERROR: 1700, // 网络连接错误
    TIMEOUT: 1701, // 请求超时
    DNS_RESOLUTION_FAILED: 1702, // DNS 解析失败
  
    // 文件处理相关状态码
    FILE_TOO_LARGE: 1800, // 上传文件过大
    UNSUPPORTED_FILE_TYPE: 1801, // 不支持的文件类型
    FILE_UPLOAD_FAILED: 1802, // 文件上传失败
  
    // 配额相关状态码
    QUOTA_EXCEEDED: 1900, // 通用配额耗尽
    FREE_QUOTA_EXCEEDED: 1910, // 免费用户配额耗尽
    FREE_INSUFFICIENT_QUOTA: 1911, // 免费用户配额不足
    PAID_QUOTA_EXCEEDED: 1912, // 付费会员配额耗尽
    PAID_INSUFFICIENT_QUOTA: 1913, // 付费会员配额不足
    ENTERPRISE_NOT_FOUND: 1919, // 企业账户不存在
    ENTERPRISE_EXPIRED: 1920, // 企业账户过期
    ENTERPRISE_QUOTA_EXCEEDED: 1921, // 企业账户余额耗尽
    ENTERPRISE_INSUFFICIENT_QUOTA: 1922, // 企业账户余额不足
    ENTERPRISE_NOT_EFFECTIVE: 1923, // 企业账户未生效
    ENTERPRISE_SUSPENDED: 1924, // 企业账户被暂停
    MEMBERSHIP_NOT_EFFECTIVE: 1925, // 会员未生效
    MEMBERSHIP_SUSPENDED: 1926, // 会员被暂停
    ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED: 1927, // 企业成员每日使用额度超限
    ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED: 1928, // 企业成员个人每日使用额度超限
  
    // 会员相关状态码
    MEMBERSHIP_EXPIRED: 1901, // 会员已过期
    MEMBERSHIP_NOT_FOUND: 1902, // 会员信息未找到
    TOO_MANY_SUPERLIKES: 1949, // superlike请求频率过高
  }
  
  // 定义状态码到消息的映射
  export const StatusMessages = new Map<number, string>([
    [StatusCodes.SUCCESS, 'Request was successful'],
    [StatusCodes.CREATED, 'Resource created successfully'],
    [StatusCodes.ACCEPTED, 'Request accepted and is being processed'],
    [StatusCodes.NO_CONTENT, 'No content to return'],
    [StatusCodes.BAD_REQUEST, 'Bad request'],
    [StatusCodes.UNAUTHORIZED, 'Unauthorized access'],
    [StatusCodes.FORBIDDEN, 'Access forbidden'],
    [StatusCodes.NOT_FOUND, 'Resource not found'],
    [StatusCodes.CONFLICT, 'Request conflict detected'],
    [StatusCodes.UNPROCESSABLE_ENTITY, 'Unprocessable entity'],
    [StatusCodes.SERVER_ERROR, 'Internal server error'],
    [StatusCodes.SERVICE_UNAVAILABLE, 'Service unavailable'],
    [StatusCodes.GATEWAY_TIMEOUT, 'Gateway timeout'],
    [StatusCodes.NETWORK_ERROR, 'Network error occurred'],
    [StatusCodes.TIMEOUT, 'Request timed out'],
    [StatusCodes.DNS_RESOLUTION_FAILED, 'DNS resolution failed'],
    [StatusCodes.FILE_TOO_LARGE, 'File too large'],
    [StatusCodes.UNSUPPORTED_FILE_TYPE, 'Unsupported file type'],
    [StatusCodes.FILE_UPLOAD_FAILED, 'File upload failed'],
    [StatusCodes.QUOTA_EXCEEDED, 'Today quota is used up, please try again tomorrow .'],
    [StatusCodes.MEMBERSHIP_EXPIRED, 'Membership expired'],
    [StatusCodes.MEMBERSHIP_NOT_FOUND, 'Membership not found'],
    [StatusCodes.TOKEN_OUTDATED, 'The account is logged in on other devices'],
    [StatusCodes.TOO_MANY_REQUESTS, 'Request frequency too high. Please wait.'],
    [StatusCodes.TOO_MANY_SUPERLIKES, 'Superlike limit reached. Please wait.'],
    [StatusCodes.VERSION_TOO_LOW, 'The version is too low, please update to the latest version'],
    [
      StatusCodes.FREE_QUOTA_EXCEEDED,
      'Daily free quota exhausted. Upgrade to premium or try again tomorrow.',
    ],
    [StatusCodes.PAID_QUOTA_EXCEEDED, 'Member quota exhausted. Please purchase additional quota.'],
    [StatusCodes.ENTERPRISE_EXPIRED, 'Enterprise account has expired. Please contact administrator.'],
    [StatusCodes.ENTERPRISE_QUOTA_EXCEEDED, 'Enterprise quota insufficient for this operation.'],
    [
      StatusCodes.FREE_INSUFFICIENT_QUOTA,
      'Insufficient free quota for this operation. Please upgrade to premium.',
    ],
    [
      StatusCodes.PAID_INSUFFICIENT_QUOTA,
      'Insufficient member quota for this operation. Please purchase additional quota.',
    ],
    [StatusCodes.ENTERPRISE_NOT_FOUND, 'Enterprise account not found'],
    [StatusCodes.ENTERPRISE_INSUFFICIENT_QUOTA, 'Insufficient enterprise quota for this operation'],
    [StatusCodes.ENTERPRISE_NOT_EFFECTIVE, 'Enterprise membership not effective'],
    [StatusCodes.MEMBERSHIP_NOT_EFFECTIVE, 'Membership not effective'],
    [StatusCodes.MEMBERSHIP_SUSPENDED, 'Membership suspended'],
    [StatusCodes.ENTERPRISE_SUSPENDED, 'Enterprise suspended'],
    [StatusCodes.ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED, 'Enterprise daily usage limit exceeded'],
    [
      StatusCodes.ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED,
      'Enterprise personal daily usage limit exceeded',
    ],
  ])