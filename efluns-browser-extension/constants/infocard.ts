import { AudienceAnalysis, DisplaySettings } from "@/@types/card"
const isIns = globalThis.location.href.includes("instagram")

//第二项如果是ins就是 8 否则是 10
const twoNum = isIns ? 8 : 10
export const videoLastDateList = [
  {
    id: 'last-5-posts',
    label: 'Last 5 posts',
    value: 5,
    type: 'count'
  },
  {
    id: `last-${twoNum}-posts`,
    label: `Last ${twoNum} posts`,
    value: twoNum,
    type: 'count'
  },
  {
    id: 'last-14-days',
    label: 'Last 14 days',
    value: 14,
    type: 'days'
  },
  {
    id: 'last-30-days',
    label: 'Last 30 days',
    value: 30,
    type: 'days'
  }
] as const


export const audienceCardMockData:AudienceAnalysis = {
  "userPortraitResult": {
      "gender": {
          "male": "52%",
          "female": "48%"
      },
      "age": {
          "under18": "5%",
          "age18to30": "39%",
          "age30to45": "34%",
          "above45": "22%"
      }
  },
  "regionAnalysisResult": {
      "total": 1412,
      "statistics": [
          {
              "region": "US",
              "count": 341,
              "percentage": "24.15%",
              "developmentLevel": "T1"
          },
          {
              "region": "KZ",
              "count": 147,
              "percentage": "10.41%",
              "developmentLevel": "T2"
          },
          {
              "region": "UA",
              "count": 78,
              "percentage": "5.52%",
              "developmentLevel": "T3"
          },
          {
              "region": "BR",
              "count": 71,
              "percentage": "5.03%",
              "developmentLevel": "T3"
          },
          {
              "region": "BY",
              "count": 71,
              "percentage": "5.03%",
              "developmentLevel": "T3"
          },
          {
              "region": "ID",
              "count": 56,
              "percentage": "3.97%",
              "developmentLevel": "T3"
          },
          {
              "region": "DE",
              "count": 48,
              "percentage": "3.40%",
              "developmentLevel": "T3"
          },
          {
              "region": "JP",
              "count": 36,
              "percentage": "2.55%",
              "developmentLevel": "T3"
          },
          {
              "region": "PL",
              "count": 28,
              "percentage": "1.98%",
              "developmentLevel": "T3"
          },
          {
              "region": "FR",
              "count": 26,
              "percentage": "1.84%",
              "developmentLevel": "T3"
          },
          {
              "region": "IT",
              "count": 23,
              "percentage": "1.63%",
              "developmentLevel": "T3"
          },
          {
              "region": "CA",
              "count": 21,
              "percentage": "1.49%",
              "developmentLevel": "T3"
          },
          {
              "region": "GB",
              "count": 21,
              "percentage": "1.49%",
              "developmentLevel": "T3"
          },
          {
              "region": "MX",
              "count": 18,
              "percentage": "1.27%",
              "developmentLevel": "T3"
          },
          {
              "region": "SA",
              "count": 16,
              "percentage": "1.13%",
              "developmentLevel": "T3"
          },
          {
              "region": "VN",
              "count": 16,
              "percentage": "1.13%",
              "developmentLevel": "T3"
          },
          {
              "region": "TR",
              "count": 15,
              "percentage": "1.06%",
              "developmentLevel": "T3"
          },
          {
              "region": "DZ",
              "count": 14,
              "percentage": "0.99%",
              "developmentLevel": "T3"
          },
          {
              "region": "AZ",
              "count": 14,
              "percentage": "0.99%",
              "developmentLevel": "T3"
          },
          {
              "region": "KG",
              "count": 14,
              "percentage": "0.99%",
              "developmentLevel": "T3"
          },
          {
              "region": "HU",
              "count": 13,
              "percentage": "0.92%",
              "developmentLevel": "T3"
          },
          {
              "region": "RO",
              "count": 12,
              "percentage": "0.85%",
              "developmentLevel": "T3"
          },
          {
              "region": "NL",
              "count": 12,
              "percentage": "0.85%",
              "developmentLevel": "T3"
          },
          {
              "region": "PH",
              "count": 11,
              "percentage": "0.78%",
              "developmentLevel": "T3"
          },
          {
              "region": "KE",
              "count": 10,
              "percentage": "0.71%",
              "developmentLevel": "T3"
          },
          {
              "region": "AU",
              "count": 10,
              "percentage": "0.71%",
              "developmentLevel": "T3"
          },
          {
              "region": "CZ",
              "count": 10,
              "percentage": "0.71%",
              "developmentLevel": "T3"
          },
          {
              "region": "GR",
              "count": 9,
              "percentage": "0.64%",
              "developmentLevel": "T3"
          },
          {
              "region": "TH",
              "count": 9,
              "percentage": "0.64%",
              "developmentLevel": "T3"
          },
          {
              "region": "MA",
              "count": 9,
              "percentage": "0.64%",
              "developmentLevel": "T3"
          },
          {
              "region": "SE",
              "count": 8,
              "percentage": "0.57%",
              "developmentLevel": "T3"
          },
          {
              "region": "RU",
              "count": 8,
              "percentage": "0.57%",
              "developmentLevel": "T3"
          },
          {
              "region": "EG",
              "count": 8,
              "percentage": "0.57%",
              "developmentLevel": "T3"
          },
          {
              "region": "MY",
              "count": 7,
              "percentage": "0.50%",
              "developmentLevel": "T3"
          },
          {
              "region": "ZA",
              "count": 7,
              "percentage": "0.50%",
              "developmentLevel": "T3"
          }
      ],
      "developmentStatistics": [
        {
          "developmentLevel": "T1",
          "count": 1412,
          "percentage": "20%"
        },
        {
          "developmentLevel": "T2",
          "count": 1412,
          "percentage": "20%"
        },
        {
          "developmentLevel": "T3",
          "count": 1412,
          "percentage": "20%"
        }
      ]
  },
  "fromCache": false,
}


export const DefaultDisplaySettings:DisplaySettings = {
  estimatedPrice: true,
  views: true,
  likes: true,
  comments: false,
  er: true,
}