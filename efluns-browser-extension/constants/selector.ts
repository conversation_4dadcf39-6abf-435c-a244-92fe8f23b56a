export const YOUTUBE_AVATAR_SELECTOR = 'yt-avatar-shape img'
export const TIKTOK_AVATAR_SELECTOR = '[data-e2e="user-avatar"] img'
export const INS_AVATAR_SELECTOR = 'header img'

export function getAvatarSelector(url:string=location.href):HTMLElement | null{
  if(url.includes('youtube.com')){
    return document.querySelector(YOUTUBE_AVATAR_SELECTOR)
  }
  if(url.includes('tiktok.com')){
    return document.querySelector(TIKTOK_AVATAR_SELECTOR)
  }
  if(url.includes('instagram.com')){
    return document.querySelector(INS_AVATAR_SELECTOR)
  }
  return null
}
