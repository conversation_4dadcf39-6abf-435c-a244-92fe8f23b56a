'use client'

import {  faClose } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import clsx from "clsx"
import { useState, useRef, useEffect, useCallback, useMemo } from "react"
import leftIcon from '~/assets/img/AIFilter/icon_left.png'
import { AiFilterParams } from "@/@types/similar"
import { getProjectService } from "@/services/ProjectService"
import { VisionModal } from "./vision-modal"
import { getSelectedVideoIds, removeSimilarFromMode } from "@/utils/youtube/similarFromVideos"


const log = makeModuleLog('AIFilterApp')

// 添加推荐示例数据
// const recommendedExamples = [
//   {
//     title: "Tech Reviewer",
//     desc: "Unboxing and reviewing latest gadgets",
//     value: "Tech Reviewer: Unboxing and reviewing latest gadgets"
//   },
//   {
//     title: "Food Blogger",
//     desc: "Restaurant reviews and cooking tips",
//     value: "Food Blogger: Restaurant reviews and cooking tips"
//   },
//   {
//     title: "Lifestyle Creator",
//     desc: "Home decor and organization",
//     value: "Lifestyle Creator: Home decor and organization"
//   },
//   {
//     title: "Travel Photographer",
//     desc: "Scenic destinations and travel guides",
//     value: "Travel Photographer: Scenic destinations and travel guides"
//   },
//   {
//     title: "Fitness Coach",
//     desc: "Workout routines and fitness tips",
//     value: "Fitness Coach: Workout routines and fitness tips"
//   },
//   {
//     title: "美妆博主",
//     desc: "化妆品试用与妆容教程",
//     value: "美妆博主：化妆品试用与妆容教程"
//   }
// ]

type ShowType = false | "vision" | "normal"

export default function AIFilterApp() {
  // const [likedInput, setLikedInput] = useState('')
  // const [dislikedInput, setDislikedInput] = useState('')
  const [show, setShow] = useState<ShowType>(false)

  const [aiFilterParams, setAiFilterParams] = useState<AiFilterParams>({
    allowList: [],
    banList: [],
    kolDescription: '',    
  }) //storage存储
  
  // const likedContainerRef = useRef<HTMLDivElement>(null)
  // const dislikedContainerRef = useRef<HTMLDivElement>(null)
  // const [isWorking] = useStorageState(isWorkingState)

  useEffect(()=>{
    const listener = (message: any,sender:any,sendResponse:any) => {
      //在用户点击开始查询的时候，打开弹窗
      if(typeof message === 'object' && message && 'type' in message && message.type === 'showAIFilter'){
        showApp(message.isVision ? "vision" : "normal")
        sendResponse(true)
        return true
      }
      return;
    }
    browser.runtime.onMessage.addListener(listener)
    return () => browser.runtime.onMessage.removeListener(listener)
  },[])

  const close = useCallback(() => {
    setShow(false)
    sendMessage('submitAIFilter',{cancel:true,ytbIds:getSelectedVideoIds()})
  },[])

  const showApp = useCallback((type:ShowType)=>{
    setShow(type)
    getProjectService().getAiFilterParams().then(setAiFilterParams)
  },[])


  // const scrollToBottom = (containerRef: React.RefObject<HTMLDivElement>) => {
  //   if (containerRef.current) {
  //     containerRef.current.scrollTo({
  //       top: containerRef.current.scrollHeight,
  //       behavior: 'smooth'
  //     })
  //   }
  // }

  // const addKeyword = (type: 'liked' | 'disliked') => {
  //   const input = type === 'liked' ? likedInput : dislikedInput
  //   if (!input.trim()) return

  //   const newKeyword = input.trim()

  //   if (type === 'liked') {
  //     if(aiFilterParams.allowList.includes(newKeyword)) return
  //     setAiFilterParams({
  //       ...aiFilterParams,
  //       allowList: [...aiFilterParams.allowList, newKeyword]
  //     })
  //     setLikedInput('')
  //     setTimeout(() => scrollToBottom(likedContainerRef), 100)
  //   } else {
  //     if(aiFilterParams.banList.includes(newKeyword)) return
  //     setAiFilterParams({
  //       ...aiFilterParams,
  //       banList: [...aiFilterParams.banList, newKeyword]
  //     })
  //     setDislikedInput('')
  //     setTimeout(() => scrollToBottom(dislikedContainerRef), 100)
  //   }
  // }

  // const removeKeyword = (keyword: string, type: 'liked' | 'disliked') => {
  //   if (type === 'liked') {
  //     setAiFilterParams({
  //       ...aiFilterParams,
  //       allowList: aiFilterParams.allowList.filter(k => k !== keyword)
  //     })
  //   } else {
  //     setAiFilterParams({
  //       ...aiFilterParams,
  //       banList: aiFilterParams.banList.filter(k => k !== keyword)
  //     })
  //   }
  // }

  // const hasKeyWords = useMemo(() => aiFilterParams.allowList?.length > 0 || aiFilterParams.banList?.length > 0, [aiFilterParams.allowList, aiFilterParams.banList])

  // const CollapsedButtons = () => {
  //   return (isWorking &&
  //     <div onClick={() => showApp("normal")} className="fixed bottom-[60px] right-0 flex items-center gap-2 px-[12px] py-[8px] bg-white rounded-tl-[12px] rounded-bl-[12px] border border-black hover:bg-gray-100 transition-all cursor-pointer font-[500]">
  //         <img src={leftIcon} alt="left" className="w-[16px] h-[16px]" />
  //         <span>AI Filter</span>
  //     </div>
  //   )
  // }

  // const handleContinue = useCallback(async() => {    
  //   log(aiFilterParams)
  //   await getProjectService().setAiFilterParams(aiFilterParams)
  //   setShow(false)
  //   sendMessage('submitAIFilter',{ytbIds:getSelectedVideoIds()})
  //   removeSimilarFromMode()
  // }, [aiFilterParams])

  // // 添加示例点击处理函数
  // const handleExampleClick = useCallback((value: string) => {
  //   setAiFilterParams(prev => ({
  //     ...prev,
  //     kolDescription: value
  //   }))
  // }, [])

  if(!show) return null

  if(show) return <VisionModal showModal={true} superVisionEnabled={true} onClose={close} />

//   return (
//     <>
//     {<div className="fixed top-0 left-0 w-full h-full bg-black/50 flex items-center justify-center z-[9999] text-[14px]" style={{ isolation: 'isolate' }}>
//       <div onKeyDown={(e)=>{
//             if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
//               e.stopPropagation();
//               e.nativeEvent.stopImmediatePropagation();
//             }          }}
//  className="flex w-full max-w-[1200px] gap-[20px] px-[20px] justify-center items-center">
//         {/* 主面板 */}
//         <div className="flex-1 max-w-[600px]">
//           <div className="w-auto mx-auto bg-white rounded-[24px] p-[24px] border border-gray-200 shadow-lg relative">
//             <div className="text-center text-[20px] font-semibold mb-[16px]">
//               AI Filter
//             </div>
//             <button onClick={close} className="absolute top-[16px] right-[16px] w-[24px] h-[24px]">
//               <FontAwesomeIcon className="w-[16px] h-[16px]" icon={faClose} />
//             </button>
//             <div className="flex justify-between items-center mb-[16px]">
//               <div className="flex items-center gap-[8px]">
//                 {/* <img src={campaignIcon} alt="campaign" className="w-[24px] h-[24px]" /> */}
//                 <h2 className="text-[18px] font-semibold"><span className="text-[24px] mr-[10px]">💼</span>Ideal Creator Description</h2>
//               </div>
//             </div>

//             <textarea
//               placeholder="Describe your ideal creator type and their main content focus."
//               className={clsx(
//                 "textarea w-full h-[112px] mb-[16px] border border-gray-300 border-dashed rounded-[12px] resize-none text-[14px] leading-[1.2]",
//               )}
//               value={aiFilterParams.kolDescription}
//               onChange={(e)=>{
//                 setAiFilterParams({
//                   ...aiFilterParams,
//                   kolDescription: e.target.value
//                 })
//               }}
//               onKeyDown={(e)=>{
//                 if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
//                   e.stopPropagation();
//                   e.nativeEvent.stopImmediatePropagation();
//                 }          }}
    
//             />

//             <div className="grid md:grid-cols-2 gap-[16px]">
//               <div>
//                 <div className="flex items-center gap-[8px] mb-[8px]">
//                   {/* <img src={likeIcon} alt="like" className="w-[24px] h-[24px]" /> */}
//                   <h3 className="text-[18px] font-semibold"><span className="text-[24px] mr-[10px]">👍</span>Liked Keywords</h3>
//                 </div>
//                 <div className="flex gap-[8px] mb-[8px] border border-[#9291a1] rounded-full px-[1px] overflow-hidden">
//                   <input
//                     type="text"
//                     value={likedInput}
//                     onChange={(e) => setLikedInput(e.target.value)}
//                     onKeyPress={(e) => {
//                       e.stopPropagation();
//                       if (e.key === 'Enter') {
//                         e.preventDefault();
//                         addKeyword('liked');
//                       }
//                     }}
//                     onKeyDown={(e)=>{
//                       if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
//                         e.stopPropagation();
//                         e.nativeEvent.stopImmediatePropagation();
//                       }          }}
          
//                     placeholder="Add Keyword"
//                     className="input input-bordered border-gray-300 flex-1 border-none !outline-none  min-h-[30px] h-[30px]"
//                   />
//                   <button
//                     onClick={() => addKeyword('liked')}
//                     className="btn btn-neutral rounded-full px-[24px]  min-h-[30px] h-[30px]"
//                   >
//                     Add
//                   </button>
//                 </div>
//                 <div 
//                   ref={likedContainerRef}
//                   className={clsx("flex flex-col gap-[8px] h-[180px] overflow-y-auto pr-[8px] scroll-smooth border border-gray-200 rounded-[12px] p-[8px]",{
//                     "hidden": !hasKeyWords,
//                   })}
//                 >
//                   {aiFilterParams.allowList && aiFilterParams.allowList.length > 0 && aiFilterParams.allowList.map((keyword) => (
//                     <div
//                       key={keyword}
//                       className="flex items-center justify-between w-full px-[16px] bg-base-100 rounded-full transition-all border border-gray-200"
//                     >
//                       <span>{keyword}</span>
//                       <button
//                         onClick={() => removeKeyword(keyword, 'liked')}
//                         className="btn btn-ghost btn-circle btn-sm"
//                       >
//                         <svg
//                           className="w-[16px] h-[16px]"
//                           fill="none"
//                           stroke="currentColor"
//                           viewBox="0 0 24 24"
//                         >
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth="2"
//                             d="M6 18L18 6M6 6l12 12"
//                           />
//                         </svg>
//                       </button>
//                     </div>
//                   ))}
//                 </div>
//               </div>

//               <div>
//                 <div className="flex items-center gap-[8px] mb-[8px]">
//                   {/* <img src={dislikeIcon} alt="dislike" className="w-[24px] h-[24px]" /> */}
//                   <h3 className="text-[18px] font-semibold"><span className="text-[24px] mr-[10px]">👎</span>Disliked Keywords</h3>
//                 </div>
//                 <div className="flex gap-[8px] mb-[8px] border border-[#9291a1] rounded-full px-[1px] overflow-hidden">
//                   <input
//                     type="text"
//                     value={dislikedInput}
//                     onChange={(e) => setDislikedInput(e.target.value)}
//                     onKeyPress={(e) => {
//                       e.stopPropagation();
//                       if (e.key === 'Enter') {
//                         e.preventDefault();
//                         addKeyword('disliked');
//                       }
//                     }}
//                     onKeyDown={(e)=>{
//                       if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
//                         e.stopPropagation();
//                         e.nativeEvent.stopImmediatePropagation();
//                       }          }}
          
//                     placeholder="Add Keyword"
//                     className="input input-bordered border-gray-300 flex-1 border-none !outline-none  min-h-[30px] h-[30px]"
//                   />
//                   <button
//                     onClick={() => addKeyword('disliked')}
//                     className="btn btn-neutral rounded-full px-[24px] min-h-[30px] h-[30px]"
//                   >
//                     Add
//                   </button>
//                 </div>
//                 <div 
//                   ref={dislikedContainerRef}
//                   className={clsx("flex flex-col gap-[8px] h-[180px] overflow-y-auto pr-[8px] scroll-smooth border border-gray-200 rounded-[12px] p-[8px]",{
//                     "hidden": !hasKeyWords,
//                   })}
//                 >
//                   {aiFilterParams.banList && aiFilterParams.banList.length > 0 && aiFilterParams.banList.map((keyword) => (
//                     <div
//                       key={keyword}
//                       className="flex items-center justify-between w-full px-[16px] bg-base-100 rounded-full transition-all border border-gray-200"
//                     >
//                       <span>{keyword}</span>
//                       <button
//                         onClick={() => removeKeyword(keyword, 'disliked')}
//                         className="btn btn-ghost btn-circle btn-sm"
//                       >
//                         <svg
//                           className="w-[16px] h-[16px]"
//                           fill="none"
//                           stroke="currentColor"
//                           viewBox="0 0 24 24"
//                         >
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth="2"
//                             d="M6 18L18 6M6 6l12 12"
//                           />
//                         </svg>
//                       </button>
//                     </div>
//                   ))}
//                 </div>
//               </div>
//             </div>

//             {!hasKeyWords && <p className="text-gray-500 mt-[6px] mb-[16px] text-center">
//               Enter words or phrases(optional)
//             </p>}

//             <button onClick={handleContinue} className="btn btn-neutral w-full rounded-full flex items-center justify-center gap-[8px] border border-gray-700 mt-[40px] h-[40px] min-h-[40px]">
//               {isWorking ? 'Update & Restart' : 'Continue'}
//             </button>
//           </div>
//         </div>

//         {/* 推荐示例面板 */}
//         <div className="w-[300px] bg-white p-4 rounded-[12px] shadow-lg self-start">
//           <h3 className="text-center m-0 mb-[15px] text-[16px] font-semibold text-[#2c3e50]">
//             Recommended Examples
//           </h3>
//           <ul className="list-none p-0 m-0">
//             {recommendedExamples.map((example, index) => (
//               <li 
//                 key={index}
//                 onClick={() => handleExampleClick(example.value)}
//                 className="bg-white border border-[#e0e0e0] rounded-[8px] p-[10px_15px] mb-[8px] cursor-pointer transition-all hover:translate-y-[-2px] hover:border-primary hover:shadow-md"
//               >
//                 <h4 className="font-semibold text-[14px] mt-0 mb-[3px] text-[#2c3e50]">
//                   {example.title}
//                 </h4>
//                 <p className="text-[12px] text-[#666] m-0">
//                   {example.desc}
//                 </p>
//               </li>
//             ))}
//           </ul>
//         </div>
//       </div>
//     </div>}
//     </>
//   )
}

