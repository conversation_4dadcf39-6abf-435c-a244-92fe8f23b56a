import createCache from '@emotion/cache'
import { CacheProvider } from '@emotion/react'
import { QueryClientProvider } from '@tanstack/react-query'
import { createRoot, Root } from 'react-dom/client'
import { ContentScriptContext, ShadowRootContentScriptUi } from 'wxt/client'
import <PERSON><PERSON>ilterApp from './AIFilterApp'  // 你的根组件
import { getPlatform } from '../Entrypoint.content/utils'
import { Platform } from '@/@types/platform'

const TAG_NAME = 'efluns-request-root'

function renderApp(body: HTMLElement) {
  const emotionStyleElement = document.createElement('style')
  const head = body.previousSibling
  head?.appendChild(emotionStyleElement)
  const styleCache = createCache({
    key: TAG_NAME,
    container: emotionStyleElement
  })
  styleCache.compat = true


  const style = document.createElement('style')
  style.textContent = `
    html, body {
      height: 0px;
    }
  `
  head?.appendChild(style)

  const appContainerElement = document.createElement('div')
  const root = createRoot(appContainerElement)

  const render = () => {
    root.render(
      <CacheProvider value={styleCache}>
        <QueryClientProvider client={queryClient}>
            <AIFilterApp />
        </QueryClientProvider>
      </CacheProvider>
    )
  }
  render()

  body.appendChild(appContainerElement)
  return root
}

export async function mountRootShadowRoot(
  context: ContentScriptContext
): Promise<ShadowRootContentScriptUi<Root> | null> {
  try {
    // 直接使用document.documentElement作为注入点
    const ui = await createShadowRootUi(context, {
      name: TAG_NAME,
      position: 'inline',
      anchor: document.documentElement, // 注入到HTML根元素
      append: 'last',
      onMount: (body) => renderApp(body),
      onRemove: (root) => {
        root?.unmount()
      }
    })

    ui.mount()
    return ui
  } catch (err) {
    console.error('Failed to mount root shadow DOM:', err)
    return null
  }
} 

export function getNickName() {
	const platform = getPlatform(window.location.href)
	switch (platform) {
		case Platform.TIKTOK:
			return document.querySelector('[data-e2e="user-title"]')?.textContent
		case Platform.YOUTUBE:
			return document.querySelector('#page-header h1')?.textContent
		case Platform.INS:
			return document.querySelector('header h2')?.textContent
    case Platform.TWITTER:
      return location.pathname.split("/")[1]
		default:
			return 'The audience of nickname'
	}
}