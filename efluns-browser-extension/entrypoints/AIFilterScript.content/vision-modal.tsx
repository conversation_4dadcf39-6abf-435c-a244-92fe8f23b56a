"use client"
import { useState, useEffect } from "react"
import { X, <PERSON><PERSON><PERSON>, ThumbsUp, ThumbsDown, Rss } from "lucide-react"
import { useQuery } from "@tanstack/react-query"
import { getPlatformService } from "@/services/PlatformService"
import { SearchKOLParams } from "@/@types/kol"
import { Platform } from "@/@types/platform"
import { getPlatform } from "../Entrypoint.content/utils"
import { getYoutubeChannelId } from "@/utils/youtube/getYoutubeChannelId"
import { getKOLService } from "@/services/KOLService"
import { getProjectService } from "@/services/ProjectService"
import { getNickName } from "./utils"
import { getSelectedVideoIds, removeSimilarFromMode } from "@/utils/youtube/similarFromVideos"
import { getCommonService } from "@/services/CommonService"

interface VisionModalProps {
  showModal: boolean
  // isLoading: boolean
  superVisionEnabled: boolean
  onClose: () => void
}

export function VisionModal({ showModal, superVisionEnabled, onClose }: VisionModalProps) {
  // Modal-specific state
  const [detectedDescription, setDetectedDescription] = useState("")
  const [likedKeywords, setLikedKeywords] = useState<string[]>([])
  const [dislikedKeywords, setDislikedKeywords] = useState<string[]>([])
  const [newLikedKeyword, setNewLikedKeyword] = useState("")
  const [newDislikedKeyword, setNewDislikedKeyword] = useState("")
  const [isScanning, setIsScanning] = useState(false)


  // // Initialize data when scanning completes
  // useEffect(() => {
  //   if (!isLoading && showModal && superVisionEnabled) {
  //     // Pre-populate AI filter content based on profile
  //     setDetectedDescription(
  //       "Fashion and lifestyle creator with a focus on minimalist aesthetics. Creates high-quality visual content with consistent color schemes and professional photography.",
  //     )
  //     setLikedKeywords(["fashion", "minimalist", "lifestyle", "aesthetic"])
  //     setDislikedKeywords(["overly promotional", "low quality"])


  //   }
  // }, [isLoading, showModal, superVisionEnabled])

  const {error} = useQuery({
    queryKey:["visionModeInfo",window.location.href],
    queryFn:async ()=>{
      setIsScanning(true)
      const currentPlatform = getPlatform(window.location.href)
      let id:string | null = null
      if(currentPlatform === Platform.YOUTUBE){
        if(checkIsPostPlatform(window.location.href)){
          id = await getPostHandlerOrId(window.location.href)
        }else{
          id = await getYoutubeChannelId()
        }
      }
      const params = await getSearchKOLParams(window.location.href, id)
      const res = await getKOLService().getVisionModeInfo(params)
      setDetectedDescription(res.kolDescription)
      setLikedKeywords(res?.allowList || [])
      setDislikedKeywords(res?.banList || [])
      setIsScanning(false)
      getCommonService().activeTabUrl(window.location.href)
      return res
    }
  })

  const getSearchKOLParams = useCallback(async (url: string, youtuberId?: string | null) => {
		const platform = await getPlatformService().getPlatform(url)
		const params: SearchKOLParams = {
			platform
		}

    if(checkIsPostPlatform(url)){
      params.handler = guard(getPostHandlerOrId(url), 'Post Handler is null')
    }else{
      switch (platform) {
        case Platform.YOUTUBE:
          params.id = guard(youtuberId, 'YOUTUBE ID is null')
          break
        case Platform.TIKTOK:
          params.handler = guard(extractKOLHandler(url), 'TIKTOK Handler is null')
          break
        case Platform.INS:
          params.handler = guard(extractInsKOLHandler(url), 'INSTAGRAM Handler is null')
          break
        case Platform.TWITTER:
          params.handler = guard(extractTwitterKOLHandler(url), 'TWITTER Handler is null')
          break
      }
    }



		return params
	}, [])

  // Keyword management functions
  const addLikedKeyword = () => {
    if (newLikedKeyword.trim() !== "" && !likedKeywords.includes(newLikedKeyword.trim())) {
      setLikedKeywords([...likedKeywords, newLikedKeyword.trim()])
      setNewLikedKeyword("")
    }
  }

  const addDislikedKeyword = () => {
    if (newDislikedKeyword.trim() !== "" && !dislikedKeywords.includes(newDislikedKeyword.trim())) {
      setDislikedKeywords([...dislikedKeywords, newDislikedKeyword.trim()])
      setNewDislikedKeyword("")
    }
  }

  const removeLikedKeyword = (keyword: string) => {
    setLikedKeywords(likedKeywords.filter((k) => k !== keyword))
  }

  const removeDislikedKeyword = (keyword: string) => {
    setDislikedKeywords(dislikedKeywords.filter((k) => k !== keyword))
  }

  const handleStartSearch = useCallback(async () => {
      await getProjectService().setAiFilterParams({
        kolDescription: detectedDescription,
        allowList: likedKeywords,
        banList: dislikedKeywords,
      })
      sendMessage('submitAIFilter',{ytbIds:getSelectedVideoIds()})
      removeSimilarFromMode()
    onClose()
  }, [detectedDescription, likedKeywords, dislikedKeywords, onClose])

  // Add progress bar animation styles
  useEffect(() => {
    const style = document.createElement("style")
    style.textContent = `
      @keyframes progress {
        0% { width: 0%; }
        100% { width: 100%; }
      }
    `
    document.head.appendChild(style)

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style)
      }
    }
  }, [])

  if (!showModal) return null

  if(error) return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold text-red-600">Error</h3>
          <button className="text-gray-500 hover:text-gray-700" onClick={() => onClose()}>
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="p-4">
          <p className="text-gray-700">{error.message}</p>
        </div>
        <div className="flex justify-end p-4 border-t">
          <button 
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            onClick={() => onClose()}
          >
            OK
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Scanning animation overlay */}
      {isScanning && (
        <div className="fixed top-0 left-0 w-full h-full inset-0 bg-white/70 backdrop-blur-sm flex items-center justify-center z-[9999]">
          <div className="max-w-md w-full">
            <div className="relative p-8 flex flex-col items-center">
              {/* Minimalist scanning animation */}
              <div className="relative w-16 h-16 mb-6">
                {/* Outer ring */}
                <div className="absolute inset-0 rounded-full border-2 border-black/10"/>
                {/* Scanning line - horizontal moving line */}
                <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-black animate-pulse"/>
                {/* Rotating scanner */}
                <div
                  className={`absolute inset-0 rounded-full border-2 border-t-transparent animate-spin ${
                    superVisionEnabled ? "border-sky-500" : "border-black"
                  }`}
                />
              </div>

              {/* Text */}
              <h2 className="text-base font-medium text-black mb-2 flex items-center">
                {superVisionEnabled && <Sparkles className="h-4 w-4 mr-1.5 text-sky-500 animate-pulse" />}
                AI is analyzing @{getNickName()}
              </h2>
              <p className=" text-gray-500 text-center mb-6">
                {superVisionEnabled
                  ? "Analyzing content, audience characteristics, and visual elements"
                  : "Analyzing content and audience characteristics"}
              </p>

              {/* Minimalist progress bar */}
              <div className="w-full h-0.5 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className={`h-full animate-[progress_2s_linear_forwards] ${
                    superVisionEnabled ? "bg-gradient-to-r from-sky-400 to-blue-500" : "bg-black"
                  }`}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI Filter */}
      {!isScanning && (
        <div className="fixed top-0 left-0 w-full h-full flex items-center justify-center inset-0 bg-white/95 overflow-auto  z-[9999] text-[12px]"  style={{ isolation: 'isolate' }}>
          <div className="p-6 w-[80%] max-w-[1000px] mx-auto">
            <div
              className={`rounded-lg shadow-xl border overflow-hidden ${
                superVisionEnabled
                  ? "bg-gradient-to-br from-sky-50 to-white border-sky-200"
                  : "bg-white border-gray-200"
              }`}
            >
              <div className="relative">
                <button className="absolute top-[16px] right-[16px] text-gray-500 hover:text-gray-700" onClick={onClose}>
                  <X className="h-[18px] w-[18px]" />
                </button>

                <div className="p-[18px]">
                  <div className="flex items-center justify-center mb-[18px]">
                    {superVisionEnabled && (
                      <div className="mr-[8px] bg-gradient-to-r from-sky-300 via-blue-200 to-sky-300 p-[6px] rounded-full animate-pulse">
                        <Sparkles className="h-[18px] w-[18px] text-blue-600" />
                      </div>
                    )}
                    <h2 className="text-[18px] font-bold text-center">
                      {superVisionEnabled ? "Vision AI Filter" : "AI Filter"}
                    </h2>
                  </div>

                  {superVisionEnabled && (
                    <div className="mb-[18px] bg-gradient-to-r from-sky-100 to-blue-50 p-[12px] rounded-lg text-[14px] text-blue-800 border border-sky-200">
                      <div className="flex items-center mb-[8px]">
                        <Sparkles className="h-[16px] w-[16px] mr-[8px] text-sky-500" />
                        <span className="font-medium">Vision mode enabled</span>
                      </div>
                      <p>
                        Vision mode = AI gets eyes. What you see, it sees. <br />
                        Want a white person who loves selfies? Now you can tell it that.
                      </p>
                    </div>
                  )}

                  <div className="mb-[18px]">
                    <div className="flex items-center mb-[8px]">
                      <div
                        className={`p-[4px] rounded mr-[8px] ${
                          superVisionEnabled ? "bg-sky-100 text-blue-800" : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        <span className="text-[18px]">💼</span>
                      </div>
                      <h3 className="text-[18px] font-semibold">Ideal Creator Description</h3>
                    </div>
                    <textarea
                      className={`w-full border rounded-lg p-[12px] h-[100px] text-[14px] focus:outline-none focus:ring-2 resize-none ${
                        superVisionEnabled
                          ? "border-sky-200 focus:ring-sky-300 bg-white"
                          : "border-gray-200 focus:ring-gray-400"
                      }`}
                      placeholder="Describe your ideal creator type and their main content focus."
                      value={detectedDescription}
                      onKeyDown={(e)=>{
                        if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
                          e.stopPropagation();
                          e.nativeEvent.stopImmediatePropagation();
                        }          }}
                      onChange={(e) => setDetectedDescription(e.target.value)}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-[18px] mb-[18px]">
                    <div>
                      <div className="flex items-center mb-[8px]">
                        <ThumbsUp className={`h-[18px] w-[18px] mr-[8px] ${superVisionEnabled ? "text-sky-500" : "text-gray-500"}`} />
                        <h3 className="font-semibold text-[18px]">Liked Keywords</h3>
                      </div>
                      <div className="flex mb-[8px]">
                        <input
                          className={`flex-1 rounded-l-full border border-r-0 px-[12px] py-[8px] text-[14px] focus:outline-none focus:ring-2 ${
                            superVisionEnabled
                              ? "border-sky-200 focus:ring-sky-300"
                              : "border-gray-200 focus:ring-gray-400"
                          }`}
                          placeholder="Add Keyword"
                          value={newLikedKeyword}
                          onChange={(e) => setNewLikedKeyword(e.target.value)}
                          onKeyDown={(e) => {e.key === "Enter" && addLikedKeyword()
                            if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
                              e.stopPropagation();
                              e.nativeEvent.stopImmediatePropagation();
                            }
                          }}
                        />
                        <button
                          className={`px-[16px] rounded-r-full text-[14px] text-white transition-colors ${
                            superVisionEnabled
                              ? "bg-gradient-to-r from-sky-500 to-blue-400 hover:from-sky-600 hover:to-blue-500"
                              : "bg-black hover:bg-gray-800"
                          }`}
                          onClick={addLikedKeyword}
                        >
                          Add
                        </button>
                      </div>
                      <div className="flex flex-wrap gap-[8px] mt-[8px] h-[100px] overflow-auto">
                        {likedKeywords.map((keyword, index) => (
                          <div
                            key={index}
                            className={`max-h-[30px] px-[12px] py-[4px] text-[12px] rounded-full  flex items-center ${
                              superVisionEnabled ? "bg-sky-100 text-blue-800" : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {keyword}
                            <button
                              className={`ml-[8px] transition-colors ${superVisionEnabled ? "text-sky-600 hover:text-sky-800" : "text-gray-500 hover:text-gray-700"}`}
                              onClick={() => removeLikedKeyword(keyword)}
                            >
                              <X className="h-[12px] w-[12px]" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center mb-[8px]">
                        <ThumbsDown
                          className={`h-[18px] w-[18px] mr-[8px] ${superVisionEnabled ? "text-sky-500" : "text-gray-500"}`}
                        />
                        <h3 className="font-semibold text-[18px]">Disliked Keywords</h3>
                      </div>
                      <div className="flex mb-[8px]">
                        <input
                          className={`flex-1 rounded-l-full border border-r-0 px-[12px] py-[8px] text-[14px] focus:outline-none focus:ring-2 ${
                            superVisionEnabled
                              ? "border-sky-200 focus:ring-sky-300"
                              : "border-gray-200 focus:ring-gray-400"
                          }`}
                          placeholder="Add Keyword"
                          value={newDislikedKeyword}
                          onChange={(e) => setNewDislikedKeyword(e.target.value)}
                          onKeyDown={(e) => {e.key === "Enter" && addDislikedKeyword()
                            if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
                              e.stopPropagation();
                              e.nativeEvent.stopImmediatePropagation();
                            }
                          }}
                        />
                        <button
                          className={`px-[16px] rounded-r-full text-[14px] text-white transition-colors ${
                            superVisionEnabled
                              ? "bg-gradient-to-r from-sky-500 to-blue-400 hover:from-sky-600 hover:to-blue-500"
                              : "bg-black hover:bg-gray-800"
                          }`}
                          onClick={addDislikedKeyword}
                        >
                          Add
                        </button>
                      </div>
                      <div className="flex flex-wrap gap-[8px] mt-[8px] h-[100px] overflow-auto">
                        {dislikedKeywords.map((keyword, index) => (
                          <div
                            key={index}
                            className={`max-h-[30px] px-[12px] py-[4px] text-[12px] rounded-full  flex items-center ${
                              superVisionEnabled ? "bg-sky-100 text-blue-800" : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {keyword}
                            <button
                              className={`ml-[8px] transition-colors ${superVisionEnabled ? "text-sky-600 hover:text-sky-800" : "text-gray-500 hover:text-gray-700"}`}
                              onClick={() => removeDislikedKeyword(keyword)}
                            >
                              <X className="h-[12px] w-[12px]" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="text-center text-[14px] text-gray-500 mb-[18px]">Enter words or phrases(optional)</div>

                  <button
                    className={`w-full py-[12px] rounded-full text-white transition-colors flex items-center justify-center ${
                      superVisionEnabled
                        ? "bg-gradient-to-r from-sky-500 to-blue-400 hover:from-sky-600 hover:to-blue-500"
                        : "bg-black hover:bg-gray-800"
                    }`}
                    onClick={handleStartSearch}
                  >
                    {superVisionEnabled && <Sparkles className="h-[16px] w-[16px] mr-[8px]" />}
                    Start Search
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
