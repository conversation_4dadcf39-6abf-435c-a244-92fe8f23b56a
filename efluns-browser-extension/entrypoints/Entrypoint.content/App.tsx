import { getAuthService } from '@/services/AuthService'
import { motion } from 'framer-motion'
import { isNil } from 'lodash-es'
import { HTMLAttributes } from 'react'
import './App.css'
import clsx from 'clsx'
import similarIcon from '../../assets/img/similar.png'

const log = makeModuleLog('EntrypointApp')
export interface AppProps extends HTMLAttributes<HTMLDivElement> {}

export default function App({ ...props }: AppProps) {
	const [session] = useStorageState(authSessionState)
	const isLogin = !isNil(session)
	const { isInTikTokKOLPage,isInInsKOLPage } = useSupportPageInfo()

	const showLoginModal = useCallback(() => {
		getAuthService().openLoginPage()
	}, [])

	const handleFindSimilars = useCallback(async () => {
		await sendMessage('openSidePanel', undefined)
	}, [])

	return (
		<div className={clsx('w-full h-full')}>

		<motion.button
			initial={{
				opacity: 0,
				scale: 0.9
			}}
			animate={{
				opacity: 1,
				scale: 1,
				transition: {
					delay: 0.3,
					type: 'spring',
					duration: 0.6
					// damping: 20
				}
			}}
			exit={{
				opacity: 0
			}}
			className={clsx("rounded-full text-[#65adf9] font-bold text-nowrap min-h-fit h-full border-[2px]  border-[#65adf9] px-[8px] py-[4px] bg-white text-[14px] flex gap-[4px] justify-center items-center",{
				'!h-[40px] !rounded-lg': isInTikTokKOLPage,
				'!h-[32px] !rounded-lg':isInInsKOLPage
			})}
			onClick={isLogin ? handleFindSimilars : showLoginModal}
		>
			{/* {isLoading && <span className="loading loading-spinner"></span>} */}
			<img src={similarIcon} alt="similar" className="w-[16px] h-[16px]" />
			{isLogin ? 'Similar' : 'Login to EasyKOL'}
			</motion.button>
		</div>
	)
}
