import '~/assets/index.css'
import { mountShadowRoot } from './utils'

export default defineContentScript({
	matches: ['https://www.youtube.com/*', 'https://www.tiktok.com/*','https://www.instagram.com/*'],
	runAt: 'document_end',
	cssInjectionMode: 'ui',
	async main(ctx) {
		let ui = await mountShadowRoot(ctx, window.location.href)
		window.navigation?.addEventListener('navigate', () => {
			ui?.remove()
		})
		window.navigation?.addEventListener('navigatesuccess', (event) => {
			const url = window.location.href
			sleep(1000).then(async () => {
				ui = await mountShadowRoot(ctx, url)
			})
		})
	}
})
