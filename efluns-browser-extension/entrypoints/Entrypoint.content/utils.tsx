import getSupportPage, { SupportPage } from '@/utils/getSupportPage'
import createCache from '@emotion/cache'
import { CacheProvider } from '@emotion/react'
import { QueryClientProvider } from '@tanstack/react-query'
import { createRoot, Root } from 'react-dom/client'
import { ContentScriptContext, ShadowRootContentScriptUi } from 'wxt/client'
import App from './App'
import { Platform } from '@/@types/platform'

const YOUTUBE_KOL_CONTAINER_SELECTOR =
	'yt-flexible-actions-view-model.page-header-view-model-wiz__page-header-flexible-actions'
const TIKTOK_KOL_CONTAINER_SELECTOR = "div[class*=DivShareTitleContainer-CreatorPageHeaderShareContainer]:has(a) div[class*=DivButtonPanelWrapper]"
const INSTAGRAM_KOL_CONTAINER_SELECTOR =
'header > section:nth-child(2) > div:nth-child(1) > div:nth-child(1)'

const TAG_NAME = 'efluns-entrypoint'

async function getContainerSelector(url: string) {
	const supportPage = await getSupportPage(url)

	switch (supportPage) {
		case SupportPage.YOUTUBE_KOL:
			return YOUTUBE_KOL_CONTAINER_SELECTOR
		case SupportPage.TIKTOK_KOL:
			return TIKTOK_KOL_CONTAINER_SELECTOR
		case SupportPage.INS_KOL:
			return INSTAGRAM_KOL_CONTAINER_SELECTOR
		default:
			throw new Error(`Unsupported page: ${url}`)
	}
}

function renderApp(body: HTMLElement) {
	// 准备样式和缓存
	const emotionStyleElement = document.createElement('style')
	const head = body.previousSibling
	head?.appendChild(emotionStyleElement)
	const styleCache = createCache({
		key: TAG_NAME,
		container: emotionStyleElement
	})
	styleCache.compat = true

	// 渲染应用
	const appContainerElement = document.createElement('div')
	appContainerElement.style.height = '100%'
	const root = createRoot(appContainerElement)

	const render = () => {
		root.render(
			<CacheProvider value={styleCache}>
				<QueryClientProvider client={queryClient}>
					<App />
				</QueryClientProvider>
			</CacheProvider>
		)
	}
	render()

	// 将应用挂载到body
	body.appendChild(appContainerElement)

	return root
}

export async function mountShadowRoot(
	context: ContentScriptContext,
	url: string
): Promise<ShadowRootContentScriptUi<Root> | null> {
	try {
		const containerSelector = await getContainerSelector(url)
		console.log("containerSelector",containerSelector);
		const entrypointContainer = await waitFor(containerSelector)
		console.log("entrypointContainer",entrypointContainer);
		

		const ui = await createShadowRootUi(context, {
			name: TAG_NAME,
			position: 'inline',
			anchor: entrypointContainer,
			append: 'last',
			onMount: (body) => renderApp(body),
			onRemove: (root) => {
				root?.unmount()
			}
		})

		ui.mount()
		return ui
	} catch (err) {
		return null
	}
}

export function getPlatform(url: string): Platform {
	if (!url) {
		throw new Error('No valid URL provided')
	}

	switch (true) {
		case /youtube.com/.test(url):
			return Platform.YOUTUBE
		case /tiktok.com/.test(url):
			return Platform.TIKTOK
		case /instagram.com/.test(url):
			return Platform.INS
		case /x.com/.test(url):
			return Platform.TWITTER
		default:
			throw new Error('Unsupported platform')
	}
}