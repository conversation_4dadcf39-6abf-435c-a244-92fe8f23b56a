import { useState, useCallback, useMemo } from 'react'
import clsx from 'clsx'
import { GoogleSheetIds } from '@/@types/googleSheet'
import { getGoogleSheetService } from '@/services/GoogleSheetService'
import { useActiveTab } from './hooks/useActiveTab'
import { useMutation } from '@tanstack/react-query'
import useToast from '../KOLInfoCard.content/hooks/useToast'

export interface AppProps {
}

export default function App(props: AppProps) {


	const [text, setText] = useState('')
    const [isSubmitting, setIsSubmitting] = useState(false)
	const [parsedLinks, setParsedLinks] = useState<GoogleSheetIds>({
		youtube: { videos: [], shorts: [], urls: [] },
			tiktok: { videos: [], urls: [] },
			instagram: { posts: [], reels: [], urls: [] }
	})

    const { isShow: isToastShow, toast, type: toastType, content: toastContent } = useToast()


    const [isCollapsed, setIsCollapsed] = useState(false)
    const [updateTime] = useStorageState(googleSheetMonthlyUpdateTimeState)
    const [googleSheetInfo] = useStorageState(googleSheetInfoState)
    const activeTab = useActiveTab()
    const [nonTrackableLinks, setNonTrackableLinks] = useState<string[]>([])

	// 提取解析逻辑到独立函数
	const parseLinks = (text: string) => {
		const lines = text.split('\n').filter(line => line.trim());
		const newParsedLinks: GoogleSheetIds = {
			youtube: { videos: [], shorts: [], urls: [] },
			tiktok: { videos: [], urls: [] },
			instagram: { posts: [], reels: [], urls: [] }
		};
		let trackable = 0;
		let nonTrackable = 0;
		const untrackableLinks: string[] = [];

		lines.forEach(line => {
			const url = line.trim();
			if (url.includes('youtube.com/watch?v=')) {
				const videoId = url.split('v=')[1]?.split('&')[0];
				if (videoId) {
					newParsedLinks.youtube.videos.push(videoId);
					newParsedLinks.youtube.urls.push(url);
					trackable++;
				}
			} else if (url.includes('youtube.com/shorts/')) {
				const shortId = url.split('shorts/')[1]?.split('?')[0];
				if (shortId) {
					newParsedLinks.youtube.shorts.push(shortId);
					newParsedLinks.youtube.urls.push(url);
					trackable++;
				}
			} else if (url.includes('tiktok.com')) {
				let videoId = null;
				
				// 处理标准格式 video/XXXXXX
				if (url.includes('/video/')) {
					videoId = url.split('video/')[1]?.split('?')[0];
				} 
				// 处理短链接格式 t/XXXXXX
				else if (url.includes('/t/')) {
					videoId = url.split('/t/')[1]?.split('/')[0];
				}
				// 处理 vt.tiktok.com/XXXXXX
				else if (url.includes('vt.tiktok.com')) {
					videoId = url.split('vt.tiktok.com/')[1]?.split('/')[0];
				}

				if (videoId) {
					newParsedLinks.tiktok.videos.push(videoId);
					newParsedLinks.tiktok.urls.push(url);
					trackable++;
				} else {
					untrackableLinks.push(url);
				}
			} else if (url.includes('instagram.com/p/')) {
				const postId = url.split('/p/')[1]?.split('/')[0];
				if (postId) {
					newParsedLinks.instagram.posts.push(postId);
					newParsedLinks.instagram.urls.push(url);
					trackable++;
				}
			} else if (url.includes('instagram.com/reel/')) {
				const reelId = url.split('/reel/')[1]?.split('/')[0];
				if (reelId) {
					newParsedLinks.instagram.reels.push(reelId);
					newParsedLinks.instagram.urls.push(url);
					trackable++;
				}
			} else {
				nonTrackable++;
				untrackableLinks.push(url);
			}
		});

		return { newParsedLinks, trackable, nonTrackable, untrackableLinks };
	};

	// 修改 useMemo
	const stats = useMemo(() => {
		const { newParsedLinks, trackable, nonTrackable, untrackableLinks } = parseLinks(text);
		setParsedLinks(newParsedLinks);
		setNonTrackableLinks(untrackableLinks);
		return { trackable, nonTrackable };
	}, [text]);

    const submit = useCallback(async () => {
        // console.log(parsedLinks)
        setIsSubmitting(true)
        getGoogleSheetService().submitSheetData(parsedLinks).then(()=>{
            setText('')
        }).finally(() => {
            setIsSubmitting(false)
        })
    }, [parsedLinks])

    const {isPending:isUpdating, mutate:updateMonthlyReport} = useMutation({
        mutationKey:['UPDATE_MONTHLY_REPORT'],
        mutationFn: async ()=>{
            const res = await getGoogleSheetService().refreshMonthlyReportSheetData()
            return res
        }
    })

    const options = [
        {
            name:'All',
            value:'all'
        },
        {
            name:'Latest 10',
            value:'10'
        }
    ]

    const [selectedOption, setSelectedOption] = useState<'all'|'10'>('10')

    // 修改 updateOldLinks
    const {isPending:isUpdatingOldLinks, mutate:updateOldLinks} = useMutation({
        mutationKey:['UPDATE_OLD_LINKS'],
        mutationFn: async ()=>{
            const res = await getGoogleSheetService().getPostLinks(selectedOption);
            setText(res.links.join('\n'));
            const { newParsedLinks } = parseLinks(res.links.join('\n'));
            // 这里可以使用 newParsedLinks 调用接口
            // console.log("处理好的链接", newParsedLinks);
            await getGoogleSheetService().submitSheetData(newParsedLinks)
            return;
        }
    })

    const copyNonTrackableLinks = useCallback(()=>{
        navigator.clipboard.writeText(nonTrackableLinks.join('\n'))
        toast('success', 'Copied to clipboard!')
    },[nonTrackableLinks, toast])


	return (
        <div className={clsx(
            'fixed bottom-0 right-0 z-[99999999]',
            {
                'hidden': !activeTab
            }
        )}>
            {isToastShow && (
					<div className="absolute top-2 z-10 toast toast-top toast-center">
						<div className={` text-center alert alert-${toastType} flex`}>
							<span>{toastContent}</span>
						</div>
					</div>
				)}
            <div className={clsx(
                'flex items-end',
                'transform transition-transform duration-300',
                isCollapsed ? 'translate-x-[calc(100%-32px)]' : 'translate-x-0'
            )}>
                <button
                    onClick={() => setIsCollapsed(!isCollapsed)}
                    className="h-14 w-8 bg-white shadow-[-2px_0_4px_rgba(0,0,0,0.1)] border-l border-t border-b border-gray-200 rounded-l-xl flex items-center justify-center cursor-pointer hover:bg-gray-50 z-10"
                >
                    <span className={`transform transition-transform duration-300 ${isCollapsed ? 'rotate-180' : ''}`}>
                        ▶
                    </span>
                </button>
                
                {activeTab === 'Post Data' ? 
                <div className="bg-white border rounded shadow-lg border-[#3d3d3d] box-border p-2 flex flex-col gap-2 h-[290px] w-[495px]">
                    <div className='py-2'>
                    <div className="flex gap-4 justify-between items-center px-4">
                       <div className='flex gap-4 items-center'>
                        {options.map(option=>{
                            return <label key={option.value} className="label cursor-pointer flex items-center gap-1">
                            <input type="radio" name="radio-10" className="radio radio-sm" value={option.value} defaultChecked={option.value === selectedOption} onChange={()=>{setSelectedOption(option.value as 'all'|'10')}} />
                            <span className="label-text font-bold text-sm">{option.name}</span>
                        </label>
                        })}
                   </div>
                    <button onClick={()=>{updateOldLinks()}} className='bg-black text-white px-8 py-2 text-[16px] rounded-lg font-medium hover:bg-gray-800 cursor-pointer flex items-center justify-center'>
                        {isUpdatingOldLinks ? <><span className="loading" />  Updating...</> : 'Update Old Links'}
                    </button>
                    </div>
                    </div>
                    <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 mb-2 flex-1 bg-[#fafafa]">
                        <textarea 
                            className="w-full h-full bg-transparent text-gray-500 outline-none resize-none overflow-auto text-[12px]"
                            placeholder="• 在这里粘贴要追踪数据的帖子链接
• Paste the post links you want to track data for here.
• Do not submit more than 100 links at a time.
• Support TikTok&Youtube&Instagram"
                            value={text}
                            onChange={(e) => setText(e.target.value)}
                            onSelect={(e) => e.stopPropagation()}
                            onMouseDown={(e) => e.stopPropagation()}
                            onKeyDown={(e) => {
                                if ((e.metaKey || e.ctrlKey) && e.key === 'a') {
                                    e.stopPropagation();
                                    // 选中 textarea 中的所有文本
                                    e.currentTarget.select();
                                    e.preventDefault();
                                }
                            }}
                        />
                    </div>
                    <div className="flex justify-between items-center px-4">
                        <div className="space-y-1 text-sm">
                            <p>
                                Trackable links: <span className="font-medium">{stats.trackable}</span>
                            </p>
                            <p className='flex items-center gap-1'>
                                Non-trackable links: <span className="font-medium">{stats.nonTrackable}</span>
                                {stats.nonTrackable > 0 && <button onClick={copyNonTrackableLinks} className='flex items-center border border-[black] rounded-[5px] px-2 ml-1'>copy</button>}
                            </p>
                        </div>
                        {!isSubmitting ? <button 
                            onClick={submit}
                            className="bg-black text-white px-8 py-2 text-[16px] rounded-lg font-medium hover:bg-gray-800 cursor-pointer"
                            disabled={stats.trackable === 0}
                        >
                            Track New Links
                        </button> : <div className="bg-gray-200 text-gray-500 px-16 py-3 rounded-lg text-lg font-medium cursor-not-allowed flex items-center justify-center"><span className="loading" />Tracking...</div>}
                    </div>
                </div> : <div className='bg-white border rounded shadow-lg border-[#3d3d3d] box-border p-2 flex justify-between items-center h-[110px] w-[495px]'>
                        <span>{updateTime?`Last updated: ${updateTime}`:googleSheetInfo?.createdAt}</span>
                        <div className="flex justify-between items-center px-4">
                        {!isUpdating ? <button 
                            onClick={()=>{updateMonthlyReport()}}
                            className="bg-black text-white px-12 py-3 rounded-lg text-lg font-medium hover:bg-gray-800 cursor-pointer"
                        >
                            Update Report
                        </button> : <div className="bg-gray-200 text-gray-500 px-12 py-3 rounded-lg text-lg font-medium cursor-not-allowed flex items-center justify-center"><span className="loading" />Updating...</div>}
                    </div>
                </div>}
            </div>
        </div>
	)
}
