import { useState, useEffect } from 'react'

type TabName = "Post Data" | "Monthly Report" | null

// 检查当前激活的 tab
const checkActiveTab = (): TabName => {
  const activeTab = document.querySelector('.docs-sheet-active-tab .docs-sheet-tab-name')
  const tabName = activeTab?.textContent || null
  return tabName === "Post Data" || tabName === "Monthly Report" ? tabName : null
}

export const useActiveTab = () => {
  const [activeTab, setActiveTab] = useState<TabName>(checkActiveTab())

  useEffect(() => {
    // 创建 MutationObserver 监听 tab 变化
    const observer = new MutationObserver(() => {
      setActiveTab(checkActiveTab())
    })

    // 监听整个 sheet tabs 容器
    const tabsContainer = document.querySelector('.docs-sheet-container')
    if (tabsContainer) {
      observer.observe(tabsContainer, {
        subtree: true,
        childList: true,
        attributes: true
      })
    }

    // 清理函数
    return () => observer.disconnect()
  }, [])

  return activeTab
}