import React from 'react';
import { createRoot, Root } from 'react-dom/client';
import App from './App';
import globalStyles from './global.css?inline'
import createCache, { type EmotionCache } from '@emotion/cache'
import { CacheProvider } from '@emotion/react'
import { QueryClientProvider } from '@tanstack/react-query';

const ANCHOR_ID = 'google-sheet-anchor'
const APP_ROOT_ID = 'google-sheet-app-root'

// 创建或获取锚点元素
async function getAnchor(): Promise<HTMLElement> {
    let anchor = document.querySelector(`#${ANCHOR_ID}`) as HTMLElement
    
    if (!anchor) {
        anchor = document.createElement('div')
        anchor.id = ANCHOR_ID
        document.body.appendChild(anchor)
    }
    
    return anchor
}

// 准备应用环境(Shadow DOM + 样式注入)
function prepareAppEnv(anchor: HTMLElement): Promise<{
    appRoot: HTMLElement
    styleCache: EmotionCache
}> {
    return new Promise((resolve, reject) => {
        // 创建 Shadow DOM 结构
        const createShadowDOM = () => {
            const appRoot = document.createElement('div')
            appRoot.id = APP_ROOT_ID
            
            // 注入全局样式
            const basicStyleElement = document.createElement('style')
            basicStyleElement.innerHTML = globalStyles
            
            // 创建 emotion 样式容器
            const emotionStyleElement = document.createElement('style')
            const styleCache = createCache({
                key: ANCHOR_ID,
                container: emotionStyleElement
            })
            styleCache.compat = true
            
            // 构建完整的 DOM 结构
            const html = document.createElement('html')
            const head = document.createElement('head')
            const body = document.createElement('body')
            
            head.appendChild(basicStyleElement)
            head.appendChild(emotionStyleElement)
            body.appendChild(appRoot)
            html.appendChild(head)
            html.appendChild(body)
            
            return { html, styleCache }
        }
        
        // 创建或获取 Shadow Root
        if (anchor.shadowRoot === null) {
            anchor.attachShadow({ mode: 'open' })
        }
        
        // 初始化 Shadow DOM
        let html: HTMLElement | null = null
        let styleCache = null
        
        if (anchor.shadowRoot?.childElementCount === 0) {
            const { html: newHtml, styleCache: newCache } = createShadowDOM()
            html = newHtml
            styleCache = newCache
            anchor.shadowRoot?.appendChild(html)
        } else {
            html = anchor.shadowRoot?.querySelector('html') ?? null
        }
        
        const appRoot = html?.querySelector(`#${APP_ROOT_ID}`) as HTMLElement
        
        if (!appRoot || !styleCache) {
            reject(new Error('Failed to initialize app environment'))
            return
        }
        
        resolve({
            appRoot,
            styleCache
        })
    })
}

// 渲染应用
let root: Root | null = null
async function renderApp() {
    try {
        const anchor = await getAnchor()
        const { appRoot, styleCache } = await prepareAppEnv(anchor)
        
        if (root) {
            root.unmount()
        }
        
        root = createRoot(appRoot)
        root.render(
            <CacheProvider value={styleCache}>
                <QueryClientProvider client={queryClient}>
                    <App/>
                </QueryClientProvider>
            </CacheProvider>
        )
    } catch (err) {
        console.error('Failed to render app:', err)
    }
}

export default defineContentScript({
    runAt: 'document_idle',
    registration: 'runtime',
    world: 'ISOLATED',
    main() {
        googleSheetInfoState.getValue().then(data=>{
            if(location.href.includes(data.spreadsheetId)){
                renderApp()
            }
        })
        
        // 处理页面导航
        // window.navigation?.addEventListener('navigate', () => {
        //     root?.unmount()
        // })
        // window.navigation?.addEventListener('navigatesuccess', () => {
        //     console.log('navigatesuccess')
        //     setTimeout(renderApp, 500)
        // })
    }
});
