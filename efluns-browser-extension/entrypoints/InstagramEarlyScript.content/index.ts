import { InsCardData, InstagramChannelData, InstagramPost } from "@/@types";
import { parseInstagramVideoDuration, sendInstagramPostData } from "./utils/index";
import { observeInsCards } from "./utils/changeInsPosterData";
import "./ins_insert.css"
import { testAdDescription } from "@/utils/regular";

export default defineContentScript({
    matches: ['https://www.instagram.com/*'],
    runAt: 'document_start',
    world: 'MAIN',
    main() {
        console.log('Instagram Early Script is loading');
        let cleanup: (() => void) | undefined;

        // 等待 document.body 可用
        const startObserver = () => {
            if (document.body) {
                cleanup = observeInsCards();
            } else {
                document.addEventListener('DOMContentLoaded', () => {
                    cleanup = observeInsCards();
                }, { once: true });
            }
        };
        
        startObserver();
        
        // 拦截原始的XMLHttpRequest
        const originalXHR = window.XMLHttpRequest;
        // @ts-expect-error 类型忽略
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            
            // @ts-expect-error 类型忽略
            xhr.open = function(...args) {                
                // @ts-expect-error 这里的类型可能有问题
                if (args[0]==='POST' && args[1]?.includes('/graphql/query')) {
                    const originalSend = xhr.send;
                    xhr.send = function(body) {
                        let isFirst = true;
                        if(typeof body === 'string') {
                            try {
                                const params = new URLSearchParams(body);
                                const variablesStr = params.get('variables');
                                if(variablesStr) {
                                    const variables = JSON.parse(decodeURIComponent(variablesStr));
                                    isFirst = !variables.after;
                                }
                            } catch(e) {
                                console.error('解析请求体失败:', e);
                            }
                        }
                        
                        xhr.addEventListener('load', function() {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response?.data?.xdt_api__v1__feed__user_timeline_graphql_connection && !response?.data?.user) {
                                    const edges = response.data.xdt_api__v1__feed__user_timeline_graphql_connection.edges;
                                    const datas = edges.map((e:any)=>({
                                        code:e.node.code,
                                        like_count:e.node.like_count,
                                        comment_count:e.node.comment_count,
                                        created_time:e.node.taken_at,
                                        duration:parseInstagramVideoDuration(e.node.video_dash_manifest),
                                        isPinned:e.node.timeline_pinned_user_ids?.length,
                                        is_paid_partnership:e.node.is_paid_partnership || testAdDescription(e.node?.caption?.text)
                                    }))

                                    const instagramData: InstagramChannelData = {
                                        posts: datas,
                                        isFirst,
                                    };

                                    sendInstagramPostData(instagramData);
                                    // console.log('Instagram posts:', instagramData);
                                }

                                if(response?.data?.xdt_api__v1__clips__user__connection_v2){
                                    const reels = response.data.xdt_api__v1__clips__user__connection_v2.edges;
                                    const reelsData = reels.map((e:any)=>({
                                        code:e?.node?.media?.code,
                                        like_count:e?.node?.media?.like_count,
                                        comment_count:e?.node?.media?.comment_count,
                                        play_count:e?.node?.media?.play_count,
                                        is_paid_partnership:e?.node?.media?.is_paid_partnership || testAdDescription(e?.node?.media?.caption?.text),
                                        isPinned:!!e?.node?.media?.clips_tab_pinned_user_ids?.length
                                    }))
                                    // collectInsReelsData(reelsData)
                                    const instagramReelsData: InstagramChannelData = {
                                        posts: reelsData,
                                        isFirst
                                    }
                                    sendInstagramPostData(instagramReelsData);
                                    // console.log('Instagram reels:', instagramReelsData);
                                }

                                //tagged内容  把共创博主展示出来
                                if(response?.data?.xdt_api__v1__usertags__user_id__feed_connection){
                                    const tagged = response.data.xdt_api__v1__usertags__user_id__feed_connection.edges;
                                    const taggedData = tagged.map((e:any)=>({
                                        code:e.node.code,
                                        like_count:e.node.like_count,
                                        comment_count:e.node.comment_count,
                                        created_time:e.node.taken_at,
                                        duration:parseInstagramVideoDuration(e.node.video_dash_manifest),
                                        isPinned:e.node.timeline_pinned_user_ids?.length,
                                        is_paid_partnership:e.node.is_paid_partnership || testAdDescription(e.node?.caption?.text),
                                        username:e.node?.user?.username
                                    }))
                                    // collectInsReelsData(reelsData)
                                    const instagramTaggedData: InstagramChannelData = {
                                        posts: taggedData,
                                        isFirst
                                    }
                                    sendInstagramPostData(instagramTaggedData);
                                    // console.log('Instagram tagged:', instagramTaggedData);
                                }

                                if(response?.data?.user?.bio_links){
                                    const bioLinks = response.data.user.bio_links.map((e:any)=>e.url)
                                    const INSTAGRAM_LINK_SELECTOR = 'header section:nth-child(4) > div > *:has(span>svg):nth-last-of-type(1)'
                                    waitFor(INSTAGRAM_LINK_SELECTOR).then((dom)=>{
                                        if(dom instanceof HTMLButtonElement || dom.querySelector("a")){
                                            dom.setAttribute('bioLinks',JSON.stringify(bioLinks))
                                        }else{
                                            return
                                        }
                                    })
                                }

                            } catch (error) {
                                console.error('解析Instagram响应时出错:', error);
                            }
                        });
                        // @ts-expect-error 类型忽略
                        // eslint-disable-next-line prefer-rest-params
                        return originalSend.apply(this, arguments);
                    }
                }
                // @ts-expect-error 类型忽略
                return originalOpen.apply(this, args);
            };


            return xhr;
        };

        const originalFetch = window.fetch;
        window.fetch = async function(...args) {
            const [resource] = args;
            // 调用原始fetch
            const response = await originalFetch.apply(this, args);
           
            //  @ts-expect-error 类型忽略
            if(resource?.includes('/async/wbloks/fetch/')){
                const clonedResponse = response.clone();

                // 1. 移除开头的 "for (;;);"
                const rawResponse = await clonedResponse.text();
                const jsonStr = rawResponse.replace(/^for \(;;\);/, '');

                try {
                // 2. 解析JSON
                const data = JSON.parse(jsonStr);
                
                // 3. 提取bloks_payload
                const bloksPayload = data?.payload?.layout?.bloks_payload;
                
                if (bloksPayload) {
                    if(bloksPayload?.data?.[0]?.data?.initial){
                        sendEvent("INS_AREA",bloksPayload?.data?.[0]?.data?.initial)
                    }
                    
                    // 你可以进一步提取其中的具体信息
                    // const accountInfo = {
                    // username: extractUsername(bloksPayload),
                    // joinDate: extractJoinDate(bloksPayload),
                    // location: extractLocation(bloksPayload),
                    // verificationDate: extractVerificationDate(bloksPayload),
                    // avatarUrl: extractAvatarUrl(bloksPayload)
                    // };
                    
                    // console.log('提取的账户信息:', accountInfo);
                } else {
                    console.error('未找到 bloks_payload');
                }
                } catch (error) {
                console.error('解析JSON时出错:', error);
                }
            }
            
            return response;
        };

        return () => {
            cleanup?.();
        };
    }
});