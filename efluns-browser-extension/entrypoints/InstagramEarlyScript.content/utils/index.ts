import { InstagramChannelData } from "@/@types";

export const INSTAGRAM_DATA = "INSTAGRAM_DATA";
const FROM_INSTAGRAM_EARLY_SCRIPT = 'FROM_INSTAGRAM_EARLY_SCRIPT';

export const STORAGE_KEY_PREFIX = 'instagram_data_';

export const sendToContentScript = (data: InstagramChannelData) => {
    window.postMessage({
        type: FROM_INSTAGRAM_EARLY_SCRIPT,
        url: window.location.href,
        data: data
    }, window.location.origin);
}

export const sendInstagramPostData = (data: InstagramChannelData) => {
    const url = window.location.href;
    const storageKey = STORAGE_KEY_PREFIX + url;
    const oldData = JSON.parse(sessionStorage.getItem(storageKey) || '{}') as InstagramChannelData
    if(data.isFirst){
        data.median_number = getMedian(data.posts.map(item=>item.like_count))
        // 发送消息通知
        sessionStorage.setItem(storageKey, JSON.stringify(data));

        sendToContentScript(data);

        // 清理旧数据
        const keys = Object.keys(sessionStorage);
        const dataKeys = keys.filter(key => key.startsWith(STORAGE_KEY_PREFIX));
        if (dataKeys.length > 10) {
            sessionStorage.removeItem(dataKeys[0]);
        } 
    }else{
        data.posts = [...oldData.posts,...data.posts]
        data.median_number = getMedian(data.posts.map(item=>item.like_count))
        sessionStorage.setItem(storageKey, JSON.stringify(data));
        sendToContentScript(data);
    }
    
    if (!sessionStorage.getItem(storageKey)) {
        // 存储数据
        sessionStorage.setItem(storageKey, JSON.stringify(data));
        
        // 发送消息通知
        sendToContentScript(data);
        
        // 清理旧数据
        const keys = Object.keys(sessionStorage);
        const dataKeys = keys.filter(key => key.startsWith(STORAGE_KEY_PREFIX));
        if (dataKeys.length > 10) {
            sessionStorage.removeItem(dataKeys[0]);
        }
    }
}

export const getStoredInstagramData = (url: string): InstagramChannelData | null => {
    // 标准化 URL,移除可能存在的 "reels/" 部分
    const storageKey = STORAGE_KEY_PREFIX + url;
    const data = sessionStorage.getItem(storageKey);
    return data ? JSON.parse(data) : null;
}

export const listenFromInstagramEarlyScript = (callback: (data: InstagramChannelData) => void) => {
    const handler = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;
        
        if (event.data?.type === 'FROM_INSTAGRAM_EARLY_SCRIPT') {
            callback(event.data.data);
        }
    };

    window.addEventListener('message', handler);
    
    // 返回清理函数
    return () => {
        window.removeEventListener('message', handler);
    };
}


/**
 * 从DASH manifest解析视频时长
 * @param videoDashManifest DASH manifest XML字符串
 * @returns 格式化的时长字符串 "分:秒"
 */
export function parseInstagramVideoDuration(videoDashManifest: string): string {
    if(!videoDashManifest){
        return "";
    }
    try {
        // 使用正则表达式直接匹配mediaPresentationDuration属性
        const durationMatch = videoDashManifest.match(/mediaPresentationDuration="PT(\d+\.?\d*)S"/);
        
        if (!durationMatch) {
            return "0:00";
        }
        
        // 提取秒数并转换为数字
        const totalSeconds = parseFloat(durationMatch[1]);
        
        // 转换为分:秒格式
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);
        
        // 返回格式化的时间字符串,秒数补零保持两位
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
    } catch (error) {
        console.error('解析视频时长出错:', error);
        return "";
    }
}

