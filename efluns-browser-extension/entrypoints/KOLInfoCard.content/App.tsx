import clsx from 'clsx'
import { motion, useAnimationControls, useDragControls } from 'framer-motion'
import { uniqueId } from 'lodash-es'
import { HTMLAttributes, Suspense, startTransition, useRef } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import FallbackCard from './components/FallbackCard'
import InfoCard from './components/InfoCard'
import {  CardPositionState } from '@/@types/card'
import { getPlatform } from '../Entrypoint.content/utils'
import { Toaster } from 'react-hot-toast'
import useToast from './hooks/useToast'

let needRerender = false

const log = makeModuleLog('KOLInfoCardApp')

async function getCommonFields(url:string){
	const commonFields = await sendMessage("getTikTokKOLOriginalFrom",url)
	return commonFields
}

export interface AppProps extends HTMLAttributes<HTMLDivElement> {
	position:CardPositionState
}

export default function App({ position, ...props }: AppProps) {
	const controls = useDragControls()
	const { isInYoutubeKOLPage, isInTikTokKOLPage, isInInsKOLPage } = useSupportPageInfo()
	const [showCommonFields,setShowCommonFields] = useStorageState(showCommonFieldsState)
	const [renderKey, setRenderKey] = useState<string>(uniqueId())

	const platform = getPlatform(window.location.href)

	const containerAnimateControls = useAnimationControls()

	const [commonFields,setCommonFields]= useState<string>("")
	const { isShow: isToastShow, toast, type: toastType, content: toastContent } = useToast()


	useEffect(() => {
		startTransition(() => {
			containerAnimateControls.start({
				x: 0,
				y: 0,
				opacity: 1,
				transition: {
					delay: 0.8
				}
			})
		})
	}, [containerAnimateControls])

	useEffect(() => {
		const handleVisibilityChange = () => {
			if (!needRerender || document.visibilityState === 'hidden') {
				return
			}

			setRenderKey(uniqueId())
			needRerender = false
		}

		handleVisibilityChange()

		getCommonFields(location.href).then(from=>{
			if(from){
				setCommonFields(from)
			}
		})

		document.addEventListener('visibilitychange', handleVisibilityChange)
		return () => {
			document.removeEventListener('visibilitychange', handleVisibilityChange)
		}
	}, [])

	// 添加这两个 ref 来追踪点击
	const clickCountRef = useRef(0);
	const lastClickTimeRef = useRef(0);

	return (
		<>
		<Toaster />
		<motion.div
			drag
			dragControls={controls}
			dragMomentum={false}
			className={clsx('', {
				'absolute right-0 top-0 scale-90 z-10': isInYoutubeKOLPage,
				'absolute right-0 top-[60px] z-10': isInTikTokKOLPage,
				'absolute right-0 top-[70px] z-[2147483647]': isInInsKOLPage
			})}
			initial={{
				// translateX: position?.[platform]?.x || 0,
				// translateY: position?.[platform]?.y || 0,
				opacity: 0
			}}
			transition={{
				type: 'spring',
				damping: 20
			}}
			onDragEnd={ function(event, info){	
							
				cardPositionState.setValue({
					...position,
					[platform]:{
						x: info.offset.x,
						y:info.offset.y
					}
				})
			}}
			animate={containerAnimateControls}
		>
			<div 
				className="absolute top-3 left-1/2 z-10 -translate-x-1/2 bg-base-300 rounded-full h-2 w-10 cursor-move"
				onClick={() => {
					const now = Date.now();
					if (now - lastClickTimeRef.current < 500) {
						clickCountRef.current++;
						if (clickCountRef.current === 3) {
							setShowCommonFields(!showCommonFields);
							const message = (showCommonFields ? 'Debug info disabled' : 'Debug info enabled');
							toast('success',message)
							clickCountRef.current = 0;
						}
					} else {
						clickCountRef.current = 1;
					}
					lastClickTimeRef.current = now;
				}} 
			/>
			<ErrorBoundary
				fallbackRender={({ error, resetErrorBoundary }) => {
					needRerender = true
					return (
						<FallbackCard
							className=""
							css={css`
								min-height: inherit;
							`}
						>
							<div>An error occurred: {error.message}</div>
							<button
								className="btn btn-primary "
								onClick={() => {
									setRenderKey(uniqueId())
								}}
							>
								Try again
							</button>
						</FallbackCard>
					)
				}}
			>
				<Suspense
					fallback={
						<FallbackCard
							className=""
							css={css`
								min-height: inherit;
							`}
						>
							<Loading>加载中</Loading>
						</FallbackCard>
					}
				>
					<div className='relative'>
					{isToastShow && (
					<div className="absolute top-2 z-10 toast toast-top toast-center">
						<div className={` text-center alert alert-${toastType} flex`}>
							<span>{toastContent}</span>
						</div>
					</div>
				)}
						{commonFields && showCommonFields && <div className='bg-white text-black text-center px-2 py-1 rounded-lg absolute top-0 left-1/2 -translate-x-1/2 translate-y-[-100%]'>{commonFields}</div>}
						<InfoCard key={renderKey} /></div>
				</Suspense>
			</ErrorBoundary>
		</motion.div>
		</>
	)
}
