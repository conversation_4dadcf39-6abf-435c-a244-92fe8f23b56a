import { AudienceAnalysis } from '@/@types/card'
import { Platform } from '@/@types/platform'
import insIcon from '@/assets/img/ins.png'
import tiktokIcon from '@/assets/img/tiktok.png'
import youtubeIcon from '@/assets/img/youtube.png'
import { audienceCardMockData } from '@/constants/infocard'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import { getKOLService } from '@/services/KOLService'
import { getPlatformService } from '@/services/PlatformService'
import { getCountryIcon } from '@/utils/tiktok/getCountryIcon'
import { useQuery } from '@tanstack/react-query'
import clsx from 'clsx'
import domtoimage from 'dom-to-image'
import React from 'react'
import ProgressBar from '@/components/ProgressBar'
import ardarIcon from '@/assets/img/radar.png'
import AudienceLoading from './AudienceLoading'
import { createSendEvent } from '@/utils/analysisPost'

interface Props {
	isOpen: boolean
	onToggle: () => void
}

const TIER_COLORS = {
	T1: "#00b5ff",
	T2: "#58c1a1",
	T3: "#f59e0b"
}

const sendEvent = createSendEvent('audience')
export default function AudienceCard({ isOpen, onToggle }: Props) {
	const handler = extractKOLHandler(window.location.href)
	const platform = getPlatform(window.location.href)

	const {
		data: commentsData,
		isLoading,
		error
	} = useQuery({
		queryKey: ['COMMENTS', handler],
		queryFn: async () => {
			const platform = await getPlatformService().getPlatform(window.location.href)
			const hander = await getKOLService().getKOLHandler(window.location.href)
			const data = await getKOLService().getAudienceAnalysis(hander!, platform)
			sendEvent('open')
			return data
		},
		//展开的时候发请求，并且缓存时间无限，这样能确保只有第一次展开的时候会请求
		enabled: isOpen,
		staleTime: Infinity
	})

	return (
		<>
			<button
				className={clsx('btn btn-outline relative infocardBtn', isOpen ? 'btn-active hover:opacity-80' : '')}
				onClick={onToggle}
			>
				<svg width="18" height="18" viewBox="0 0 31 30" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path
						d="M6.75039 25.0004C6.06289 25.0004 5.50039 24.4379 5.50039 23.7504V18.7504C5.50039 18.0629 6.06289 17.5004 6.75039 17.5004C7.43789 17.5004 8.00039 18.0629 8.00039 18.7504V23.7504C8.00039 24.4379 7.43789 25.0004 6.75039 25.0004ZM12.5629 25.0004C11.8754 25.0004 11.3129 24.4379 11.3129 23.7504V14.3754C11.3129 13.6879 11.8754 13.1254 12.5629 13.1254C13.2504 13.1254 13.8129 13.6879 13.8129 14.3754V23.7504C13.8129 24.4379 13.2504 25.0004 12.5629 25.0004ZM18.4379 25.0004C17.7504 25.0004 17.1879 24.4379 17.1879 23.7504V16.8754C17.1879 16.1879 17.7504 15.6254 18.4379 15.6254C19.1254 15.6254 19.6879 16.1879 19.6879 16.8754V23.7504C19.6879 24.4379 19.1254 25.0004 18.4379 25.0004ZM24.2504 25.0004C23.5629 25.0004 23.0004 24.4379 23.0004 23.7504V15.0004C23.0004 14.3129 23.5629 13.7504 24.2504 13.7504C24.9379 13.7504 25.5004 14.3129 25.5004 15.0004V23.7504C25.5004 24.4379 24.9379 25.0004 24.2504 25.0004ZM6.75039 15.0004C6.43789 15.0004 6.06289 14.8754 5.81289 14.5629C5.37539 14.0629 5.37539 13.2504 5.87539 12.8129L12.7504 6.56289C13.2504 6.12539 14.0629 6.12539 14.5004 6.62539C14.9379 7.12539 14.9379 7.93789 14.4379 8.37539L7.56289 14.6254C7.37539 14.8754 7.06289 15.0004 6.75039 15.0004Z"
						fill="black"
					/>
					<path
						d="M18.6246 13.1285C18.3121 13.1285 18.0621 13.0035 17.8121 12.816L12.8121 8.44101C12.3121 8.00351 12.2496 7.19101 12.6871 6.69101C13.1246 6.19101 13.9371 6.12851 14.4371 6.56601L19.4371 10.941C19.9371 11.3785 19.9996 12.191 19.5621 12.691C19.3121 13.0035 18.9996 13.1285 18.6246 13.1285Z"
						fill="black"
					/>
					<path
						d="M18.625 13.125C18.3125 13.125 18 13 17.75 12.75C17.25 12.25 17.25 11.5 17.75 11L23.375 5.375C23.875 4.875 24.625 4.875 25.125 5.375C25.625 5.875 25.625 6.625 25.125 7.125L19.5 12.75C19.25 13 18.9375 13.125 18.625 13.125Z"
						fill="black"
					/>
					<path
						d="M24.25 7.5H20.5C19.8125 7.5 19.25 6.9375 19.25 6.25C19.25 5.5625 19.8125 5 20.5 5H24.25C24.9375 5 25.5 5.5625 25.5 6.25C25.5 6.9375 24.9375 7.5 24.25 7.5Z"
						fill="black"
					/>
					<path
						d="M24.25 11.25C23.5625 11.25 23 10.6875 23 10V6.25C23 5.5625 23.5625 5 24.25 5C24.9375 5 25.5 5.5625 25.5 6.25V10C25.5 10.6875 24.9375 11.25 24.25 11.25Z"
						fill="black"
					/>
				</svg>
				<span>Audience</span>
			</button>
			{isOpen && (
				<div
					className={clsx(
						'absolute max-h-[900px] -bottom-3 right-0 w-full translate-y-[100%] bg-[#f6f6f8] rounded-md pb-[20px] px-[4%] pt-0',
						{'min-w-[370px]': platform === Platform.YOUTUBE,
						 'min-w-[580px]': platform !== Platform.YOUTUBE,
						}
					)}
				>
					<div className={clsx(" rounded-md py-2  overflow-y-auto max-h-[830px]",{
						"!max-h-[600px]":platform === Platform.YOUTUBE,
					})}>
						{error ? (
							//  @ts-expect-error 添加一个code
							!error.statusCode ? (
								<div className='py-6 text-center w-full'>
									<span>Error: {error.message}</span>
								</div>
							) : (
								<AudienceCardContent data={audienceCardMockData} isFreeUser={true} />
							)
						) : isLoading ? <AudienceLoading/> : (
							<AudienceCardContent data={commentsData!}/>
						)}
					</div>
					{/* {!commentsData && <div className=" absolute  w-full text-center top-[20px] left-0 px-[5%] whitespace-normal bg-[#f6f6f8] border-none">
						Audience data analysis is based on the most recent 1,000 interacting users. Each analysis report will
						consume task quota.
					</div>} */}
					{/* <button 
                    className='absolute top-[3px] right-[8px] border-none bg-transparent' 
                    onClick={onToggle}
                >
                    ⬆️hide
                </button> */}
				</div>
			)}
		</>
	)
}

function AudienceCardContent({ data, isFreeUser }: { data: AudienceAnalysis; isFreeUser?: boolean }) {
	const { userPortraitResult, regionAnalysisResult } = data
	const contentRef = React.useRef<HTMLDivElement>(null)
	const [extend, setExtend] = useState(false)

	const handleDownload = useCallback(async () => {
		if (!contentRef.current) return
		await downloadImage(contentRef.current)
	}, [])

	const allCountries = useMemo(() => {
		return regionAnalysisResult.statistics.filter((item, index) => {
			return index < 10 || (index >= 10 && parseFloat(item.percentage) >= 2)
		})
	}, [regionAnalysisResult.statistics])

	const countries = useMemo(() => {
		return extend ? allCountries : allCountries.slice(0, 7)
	}, [allCountries, extend])

	return (
		<div ref={contentRef} className="max-w-4xl mx-auto p-4 relative" id="dashboard">
			{isFreeUser && (
				<div className="absolute inset-0 pointer-events-none select-none overflow-hidden z-[9] backdrop-blur-sm bg-white/30" style={{top: '60px'}}>
					{Array.from({ length: 3 }).map((_, i) => (
						<div
							key={i}
							className="absolute text-[#e5e5e5] text-4xl font-bold whitespace-nowrap"
							style={{
								top: `${i * 35}%`,
								left: '50%',
								transform: 'translateX(-50%) translateY(80px) rotate(-30deg)',
								letterSpacing: '0.5rem',
								color:'#f09837',
								fontWeight:'900',
								width: '100%',
								textAlign: 'center'
							}}
						>
							⚠️ NOT REAL DATA
						</div>
					))}
				</div>
			)}
			<div className="flex justify-between items-center mb-4">
				{isFreeUser ? (
					<h1 className="text-3xl font-bold text-center w-full flex items-center gap-[20px] justify-center">Upgrade for Real Data <button onClick={()=>{
						window.open('https://airy-update.notion.site/EasyKOL-14ae8694012c804595fffc6afe85eefa', '_blank')
					}} className='btn btn-outline btn-sm flex items-center gap-2 bg-black text-white hover:bg-gray-800'>Upgrade</button></h1>
				) : (
					<>
						<h1 className="text-2xl font-bold flex items-center gap-2">
							<img src={getPlatformIcon()} alt="" className="w-6 h-6" />
							{getNickName()}
						</h1>
						<button
							id="downloadBtn"
							className={clsx('btn btn-outline btn-sm flex items-center gap-2')}
							onClick={handleDownload}
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
								className="h-4 w-4"
							>
								<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
								<polyline points="7 10 12 15 17 10" />
								<line x1="12" y1="15" x2="12" y2="3" />
							</svg>
							Download
						</button>
					</>
				)}
			</div>

			<div className="audience-content">
				{/* <!-- Gender Distribution --> */}
				<div className="card bg-base-100 shadow-sm mb-4">
					<div className="card-body p-6">
						<div className="flex items-center pb-2">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="20"
								height="20"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
								className="mr-2 h-5 w-5 text-gray-500"
							>
								<path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
								<circle cx="9" cy="7" r="4" />
								<path d="M22 21v-2a4 4 0 0 0-3-3.87" />
								<path d="M16 3.13a4 4 0 0 1 0 7.75" />
							</svg>
							<h2 className="card-title text-lg">Gender</h2>
						</div>
						<div className="space-y-4">
							<div className="space-y-1">
								<div className="flex items-center justify-between">
									<span className="font-medium">Male</span>
									<span className="text-sm text-gray-500">{userPortraitResult.gender.male}</span>
								</div>
								<ProgressBar
									value={parseInt(userPortraitResult.gender.male)}
									max={100}
								/>
							</div>
							<div className="space-y-1">
								<div className="flex items-center justify-between">
									<span className="font-medium">Female</span>
									<span className="text-sm text-gray-500">{userPortraitResult.gender.female}</span>
								</div>
								<ProgressBar
									value={parseInt(userPortraitResult.gender.female)}
									max={100}
								/>
							</div>
						</div>
					</div>
				</div>

				{/* <!-- Age --> */}
				<div className="card bg-base-100 shadow-sm mb-4">
					<div className="card-body p-6">
						<div className="flex items-center pb-2">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="20"
								height="20"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
								className="mr-2 h-5 w-5 text-gray-500"
							>
								<rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
								<line x1="16" y1="2" x2="16" y2="6" />
								<line x1="8" y1="2" x2="8" y2="6" />
								<line x1="3" y1="10" x2="21" y2="10" />
							</svg>
							<h2 className="card-title text-lg">Age</h2>
						</div>
						<div className="space-y-4">
							{Object.entries(userPortraitResult.age).sort((a,b)=>Number(getFirstTwoNumbers(a[0]))-Number(getFirstTwoNumbers(b[0]))).map(([key,value])=>{
								return <div key={key} className="space-y-1">
								<div className="flex items-center justify-between">
									<span className="font-medium">{formatAgeKey(key)}</span>
									<span className="text-sm text-gray-500">{value}</span>
								</div>
								<ProgressBar
									value={parseInt(value)}
									max={100}
								/>
							</div>
							})}
						</div>
					</div>
				</div>

				{/* <!-- Country Distribution --> */}
				<div className="card bg-base-100 shadow-sm mb-4">
					<div className="card-body p-6">
						<div className="flex items-center pb-2">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="20"
								height="20"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
								className="mr-2 h-5 w-5 text-gray-500"
							>
								<circle cx="12" cy="12" r="10" />
								<line x1="2" y1="12" x2="22" y2="12" />
								<path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
							</svg>
							<h2 className="card-title text-lg">Region</h2>
						</div>
						<DevelopmentStatusOverview data={data} />
						<div className="space-y-4">
							{countries.map((country, index) => (
								<div className="space-y-1" key={index}>
									<div className="flex items-center justify-between">
										<div className="flex items-center">
											<img src={getCountryIcon(country.region.slice(0, 2))!} alt="" className="w-[1.5rem] h-[1.5rem] mr-2 object-cover rounded-full" />
											<span className="font-medium">{country.region}</span>
											<span className={`ml-2 text-xs px-1.5 py-0.5 rounded-full text-white levelBtn ${country.developmentLevel || ""}`} >{country.developmentLevel}</span>
										</div>
										<span className="text-sm text-gray-500">{country.percentage}</span>
									</div>
									<ProgressBar
										value={parseFloat(country.percentage)}
										max={100}
										foregroundColor={getDevelopmentLevelColor(country.developmentLevel)}
									/>
								</div>
							))}
							{allCountries.length > 7 && (
								<button
									className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-[#f4f4f5] hover:text-accent-foreground h-10 px-4 py-2 w-full text-gray-500"
									onClick={() => setExtend(!extend)}
								>
									{extend ? 'Show Less' : `View All ${allCountries.length} Countries`}
								</button>
							)}
						</div>
					</div>
				</div>

				{/* {ardar} */}
				{data.fakeRadarData && <div className="card bg-base-100 shadow-sm">
					<div className="card-body p-6">
						<div className="flex items-center pb-2">
							<img src={ardarIcon} alt="" className='w-[20px] h-[20px] mr-2' />
							<h2 className="card-title text-lg">Audience Radar</h2>
						</div>
						<div className='text-sm font-bold'>Analysis based on the most recent 1,000 interacting users.</div>
						<DashboardMetrics data={data.fakeRadarData} />
						<div className="space-y-4">
							<div className="space-y-1">
									<div className="flex items-center justify-between">
										<span className="font-medium">Users Without Country</span>
										<span className="text-sm text-gray-500">{data.fakeRadarData.userWithoutCountryRate}</span>
									</div>
									<ProgressBar
										value={parseInt(data.fakeRadarData.userWithoutCountryRate)}
										max={100}
										foregroundColor='red'
									/>
							</div>
							<div className="space-y-1">
									<div className="flex items-center justify-between">
										<span className="font-medium">Suspected Fake Rate <span className='font-normal'>(Registered {'<'}1 Year)</span></span>
										<span className="text-sm text-gray-500">{data.fakeRadarData.suspectedFakeRate}</span>
									</div>
									<ProgressBar
										value={parseInt(data.fakeRadarData.suspectedFakeRate)}
										max={100}
										foregroundColor='red'
									/>
							</div>
						</div>
					</div>
				</div>
				}
			</div>
		</div>
	)
}

function DevelopmentStatusOverview({data}:{data:AudienceAnalysis}) {
	return (
		<div className="mx-auto mb-3 w-full">
			<div className="grid grid-cols-3 gap-4 w-full" >
				{data.regionAnalysisResult.developmentStatistics?.sort((a,b)=>Number(a.developmentLevel[1])-Number(b.developmentLevel[1])).map((item,index)=>(
					<div key={index} className={clsx("rounded-lg border p-4 flex flex-col items-center justify-center text-center",{
						"bg-[#f3f7fe]":item.developmentLevel === "T1",
						"bg-[#f5fbf9]":item.developmentLevel === "T2",
						"bg-[#fefbf4]":item.developmentLevel === "T3",
					})} style={{
						borderColor:getDevelopmentLevelColor(item.developmentLevel)
					}}>
						<h2 className=" font-bold text-xl mb-2" style={{
							color:getDevelopmentLevelColor(item.developmentLevel)
						}}>{item.percentage}</h2>
						<div className="text-white rounded-full px-2" style={{
							backgroundColor:getDevelopmentLevelColor(item.developmentLevel)
						}}>{item.developmentLevel}</div>
					</div>
				))}
			</div>

			<div className="flex justify-center gap-12 my-4">
              {Object.entries(TIER_COLORS).map(([tier, color]) => (
                <div key={tier} className="flex items-center">
                  <div className="w-6 h-6 rounded-full mr-1" style={{ backgroundColor: color }} />
                  <span className="text-lg text-muted-foreground text-[#71717a]">
                    {tier === "T1" ? "Developed" : tier === "T2" ? "Developing" : "Underdeveloped"}
                  </span>
                </div>
              ))}
            </div>
		</div>
	)
}

 function DashboardMetrics({data}:{data:AudienceAnalysis["fakeRadarData"]}) {
	if(!data) return null
  
	return (
	  <div className="container mx-auto p-4 my-2">
		<div className="grid grid-cols-2 gap-4">
		  {/* Total Comments Card */}
		  <div className="bg-white shadow-sm p-4 flex flex-col items-center border border-[#e5e7eb] rounded-lg">
			<h3 className="text-gray-500 text-lg font-medium mb-2">Total Comments</h3>
			<p className="text-2xl font-bold">{formatNumberWithCommas(data.totalCommentCount)}</p>
		  </div>
  
		  {/* Total Users Card */}
		  <div className="bg-white shadow-sm p-4 flex flex-col items-center border border-[#e5e7eb] rounded-lg">
			<h3 className="text-gray-500 text-lg font-medium mb-2">Total Users</h3>
			<p className="text-2xl font-bold">{formatNumberWithCommas(data.totalUserCount)}</p>
		  </div>
  
		  {/* Comments/User Card */}
		  <div className="bg-white shadow-sm p-4 flex flex-col items-center border border-[#e5e7eb] rounded-lg">
			<h3 className="text-gray-500 text-lg font-medium mb-2">Comments/User</h3>
			<p className="text-2xl font-bold">{(data.totalCommentCount/data.totalUserCount).toFixed(1)}</p>
		  </div>
  
		  {/* Videos Analyzed Card */}
		  <div className="bg-white shadow-sm p-4 flex flex-col items-center border border-[#e5e7eb] rounded-lg">
			<h3 className="text-gray-500 text-lg font-medium mb-2">Videos Analyzed</h3>
			<p className="text-2xl font-bold">{data.videoCount}</p>
		  </div>
  
		</div>
	  </div>
	)
  }
  

function downloadImage(dom: HTMLElement) {
	getKOLService().exportAudienceAnalysis(window.location.href).catch((error:Error)=>{
	})
	const btn = dom.querySelector('#downloadBtn') as HTMLButtonElement
	btn.style.display = 'none'
	return new Promise((resolve, reject) => {
		domtoimage
			.toPng(dom, {
				bgcolor: '#f6f6f8',
				width: dom.offsetWidth, // 明确设置宽度
				height: dom.offsetHeight // 明确设置高度
			})
			.then((dataUrl: string) => {

				// 创建一个隐藏的 iframe 来处理下载，避免触发导航事件
				const iframe = document.createElement('iframe')
				iframe.style.display = 'none'
				document.body.appendChild(iframe)

				// 在 iframe 中创建并点击链接
				setTimeout(() => {
					const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
					if (iframeDoc) {
						const link = iframeDoc.createElement('a')
						link.download = `audience-analysis-${new Date().toISOString().split('T')[0]}.png`
						link.href = dataUrl
						iframeDoc.body.appendChild(link)
						link.click()

						// 清理 iframe
						setTimeout(() => {
							document.body.removeChild(iframe)
						}, 100)
					}
				}, 0)
			})
			.catch((error: Error) => {
				console.error('截图生成失败:', error)
				alert('截图生成失败，请尝试其他浏览器或联系支持团队')
				reject(error)
			})
			.finally(() => {
				btn.style.display = 'flex'
				resolve(true)
			})
	})
}

function getNickName() {
	const platform = getPlatform(window.location.href)
	switch (platform) {
		case Platform.TIKTOK:
			return document.querySelector('[data-e2e="user-title"]')?.textContent
		case Platform.YOUTUBE:
			return document.querySelector('#page-header h1')?.textContent
		case Platform.INS:
			return document.querySelector('header h2')?.textContent
		default:
			return 'The audience of nickname'
	}
}

function getPlatformIcon() {
	const platform = getPlatform(window.location.href)
	switch (platform) {
		case Platform.TIKTOK:
			return tiktokIcon
		case Platform.YOUTUBE:
			return youtubeIcon
		case Platform.INS:
			return insIcon
		default:
			throw new Error('Invalid platform')
	}
}



function getDevelopmentLevelColor(t:"T1"|"T2"|"T3"|undefined){
	return TIER_COLORS[t as keyof typeof TIER_COLORS] || TIER_COLORS.T1
}

function formatAgeKey(key:string){
if (key.startsWith('under')) {
    const age = key.replace('under', '')
    return `0-${age}`
} else if (key.startsWith('age') && key.includes('to')) {
    const [start, end] = key.replace('age', '').split('to')
    return `${start}-${end}`
} else if (key.startsWith('above')) {
    const age = key.replace('above', '')
    return `${age}+`
}
return key
}

function getFirstTwoNumbers(str:string) {
	if(str.includes('under')){
		return -1
	}
	if(str.includes('above')){
		return 100
	}
	const matches = str.match(/\d+/g); // 匹配所有连续数字
	const result = matches ? matches.slice(0, 2) : [];
	return result?.[0] || 0;
  }


  