"use client"

import { useEffect, useState } from "react"
import { Loader2 } from "lucide-react"
import clsx from "clsx"

export default function AudienceLoading({title="Audience Radar"}:{title?:string}) {
  const [progress, setProgress] = useState(0)
  const [timeRemaining, setTimeRemaining] = useState(60) // 1 minute in seconds
  const [isPending, setIsPending] = useState(false)
  const [statusMessage, setStatusMessage] = useState("Initializing data retrieval...")

  // Status messages that will be displayed during loading
  const statusMessages = [
    "Initializing data retrieval...",
    "Connecting to real-time data sources...",
    "Fetching latest information...",
    "Processing real-time data...",
    "Preparing visualization components...",
  ]

  // Pending state messages
  const pendingMessages = [
    "Still processing data...",
    "Retrieving additional information...",
    "Finalizing data analysis...",
    "Almost there...",
    "Completing final calculations...",
  ]

  useEffect(() => {
    // Progress bar animation - initially set to complete in 1 minute (60 seconds)
    const progressInterval = setInterval(() => {
      setProgress((prevProgress) => {
        // If we're in pending state, slow down progress
        if (isPending) {
          const increment = (100 - prevProgress) * 0.05
          return Math.min(prevProgress + increment, 99.5) // Never quite reach 100%
        }

        // Normal progress during the first minute
        if (prevProgress >= 100) {
          clearInterval(progressInterval)
          return 100
        }
        return prevProgress + 100 / 60 // Increment to reach 100% in 60 seconds
      })
    }, 1000)

    // Countdown timer
    const timerInterval = setInterval(() => {
      setTimeRemaining((prevTime) => {
        if (prevTime <= 0) {
          // When timer reaches zero, switch to pending state
          setIsPending(true)
          clearInterval(timerInterval)
          return 0
        }
        return prevTime - 1
      })
    }, 1000)

    // Rotate through status messages
    const messageInterval = setInterval(() => {
      if (isPending) {
        // Use pending messages when in pending state
        setStatusMessage((prevMessage) => {
          const currentIndex = pendingMessages.indexOf(prevMessage)
          const nextIndex = currentIndex === -1 ? 0 : (currentIndex + 1) % pendingMessages.length
          return pendingMessages[nextIndex]
        })
      } else {
        // Use normal status messages during countdown
        setStatusMessage((prevMessage) => {
          const currentIndex = statusMessages.indexOf(prevMessage)
          const nextIndex = (currentIndex + 1) % statusMessages.length
          return statusMessages[nextIndex]
        })
      }
    }, 5000) // Change message every 5 seconds

    return () => {
      clearInterval(progressInterval)
      clearInterval(timerInterval)
      clearInterval(messageInterval)
    }
  }, [isPending])

  // Format remaining time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`
  }

  return (
    <div className="w-full mx-auto p-6 rounded-lg bg-white shadow-lg">
      <div className="space-y-6">
        <div className="text-center space-y-2">
          <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
          <div className="text-sm text-gray-600 whitespace-normal">
            {isPending
              ? "We're still processing your data. This is taking longer than expected."
              : "We're fetching the latest real-time data for your analysis. This should take about 1 minute."}
          </div>
        </div>

        <div className="relative">
          <ProgressBar value={progress} className={clsx("h-2", isPending ? "bg-gray-100" : "bg-gray-100")} max={100} />
          <span className="absolute right-0 top-3 text-xs text-gray-500 font-medium">{Math.round(progress)}%</span>
        </div>

        <div className="flex items-center justify-center space-x-3 py-2">
          <Loader2 className={clsx("h-5 w-5 animate-spin", isPending ? "text-amber-500" : "text-primary")} />
          <span className={clsx("text-sm font-medium", isPending ? "text-amber-600" : "text-gray-700")}>
            {statusMessage}
          </span>
        </div>

        <div className="text-center">
          {!isPending && timeRemaining > 0 ? (
            <div className="text-sm text-gray-600">
              Estimated time: <span className="font-semibold">{formatTime(timeRemaining)}</span>
            </div>
          ) : (
            <div className="text-sm text-amber-600 font-medium">Processing is taking longer than expected</div>
          )}
          <div className="mt-1 text-xs text-gray-500">
            {isPending
              ? "Please wait while we complete the data processing"
              : "Your data will be available for viewing shortly"}
          </div>
        </div>
      </div>
    </div>
  )
}
