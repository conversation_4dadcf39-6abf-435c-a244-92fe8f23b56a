import { KolRelation, SearchKOLParams } from '@/@types/kol'
import { Platform } from '@/@types/platform'
import { getKOLService } from '@/services/KOLService'
import { getPlatformService } from '@/services/PlatformService'
import { getYoutubeData } from '@/utils/youtube'
import { getYoutubeChannelId } from '@/utils/youtube/getYoutubeChannelId'
import { useQuery } from '@tanstack/react-query'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import extendPng from '~/assets/img/extend.png'

//在各平台头像下方的卡片
const log = makeModuleLog('AvatarCard')
export function AvatarCard() {

  const {data:kolRelationData,isLoading:isKolRelationLoading,isError:isKolRelationError} = useQuery({
    queryKey:['FETCH_KOL_RELATION'],
    queryFn:async ()=>{
      const url = window.location.href
      const platform = await getPlatformService().getPlatform(url)
      const params: SearchKOLParams = {
        platform
      }
  
      switch (platform) {
        case Platform.YOUTUBE:
          await getYoutubeChannelId().then((res)=>{
            
            if(res){
                params.id = res as string
            }else{
                throw new Error("getYoutubeChannelId error")
            }
        }).catch(()=>{            
            getYoutubeData(url).then(res=>{
                                
                if(!res.id){
                    throw new Error("getYoutubeData error")
                }
                params.id = res.id
            })
        })
          break
        case Platform.TIKTOK:
          params.handler = guard(extractKOLHandler(url), 'TIKTOK Handler is null')
          break
        case Platform.INS:
          params.handler = guard(extractInsKOLHandler(url), 'INSTAGRAM Handler is null')
          break
      }
      const kolService = getKOLService()
      return kolService.searchKolRelation(params)
    }
  })
  log('kolRelationData',kolRelationData)
  const simpleData = kolRelationData?.find(e=>e.title === "POSTED") || kolRelationData?.[0]

    const [isExpanded, setIsExpanded] = useState(false)

    if(isKolRelationError || (kolRelationData && kolRelationData.length === 0)){
      return <></>
    }

    // 简单版本的卡片
    const SimpleCard = () => (
      <motion.div className="flex items-center justify-center flex-col h-full py-1">
        <div className='flex justify-around items-center w-full'>
          <span />
          <div className='text-[16px] font-bold'>{simpleData?.title}</div>
          <img className='w-[14px] h-[14px]' src={extendPng} alt="extend" />
        </div>
        <div className='text-[12px] w-full px-4 flex justify-center'>
          <div className='truncate w-[45%] text-center'>{simpleData?.properties[1].value}</div>
          <div className='mx-1'>·</div>
          <div className='truncate w-[45%] text-center'>{simpleData?.properties[0].value}</div>
        </div>
      </motion.div>
    )

    // 详细版本的卡片
    const DetailedCard = () => (
      <motion.div className="p-4 flex flex-col gap-4 text-[12px]">
      {kolRelationData?.map((item,index)=>{
        return  <div className={clsx({"border-t pt-4":index !== 0})} key={item.title}>
          <div className="font-bold text-gray-900 mb-2">{item.title}</div>
          <div className="flex flex-col gap-1 text-[12px]">
            {item.properties.map(property=>{
              return <div className="flex flex-wrap items-baseline justify-between" key={property.key}>
                <span className="text-gray-500 mr-2">{property.key}:</span>
                {property.type === 'link' ? (
                  <a href={property.href} target='_blank' rel="noreferrer" className="text-gray-900 underline text-[12px]">
                    View
                  </a>
                ) : (
                  <span className="text-gray-500 text-[12px]">{property.value}</span>
                )}
              </div>
            })}
          </div>
        </div>
      })}
      </motion.div>
    )

    if(isKolRelationLoading) return null

    return (
      <motion.div
        className="rounded-xl bg-white shadow-sm absolute left-1/2 -translate-x-1/2 w-[176px] overflow-hidden text-[black] border border-[#cacaca]"
        style={{
          top: 'calc(100% - 38px)'
        }}
        onMouseEnter={() => setIsExpanded(true)}
        onMouseLeave={() => setIsExpanded(false)}
        animate={{
          height:  "auto"
        }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
      >
        {isKolRelationLoading?<div className='h-full flex items-center justify-center'><span className='loading'/></div>:isExpanded ? (
          <DetailedCard />
        ) : (
          <div className='h-full'>
            <SimpleCard />
          </div>
        )}
      </motion.div>
    )
}

