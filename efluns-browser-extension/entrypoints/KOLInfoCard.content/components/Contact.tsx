import { EmailTemplate } from '@/@types'
import { KOL } from '@/@types/kol'
import EmailTemplateDropdown from '@/components/EmailTemplateDropdown'
import { autoInfoCardState } from '@/utils/storages'
import clsx from 'clsx'
import { isEmpty } from 'lodash-es'
import { Mail } from 'lucide-react'
import { HTMLAttributes } from 'react'

export interface ContactProps extends HTMLAttributes<HTMLDivElement> {
	data: KOL | undefined
	hasEmail?:boolean
	emailTemplate: EmailTemplate | undefined
	isLoading?: boolean
	isSendEmailButtonDisabled: boolean
	isSendPreparing?: boolean
	onEmailCopy?: (email: string) => Promise<void>
	onCustomEmailAddress?: (email: string) => void
	onSendEmail?: () => void
	isManualMode?: boolean
	onManualSearch?: () => void
	isEnterpriseUser?: boolean
}

export default function Contact({
	data,
	hasEmail,
	emailTemplate,
	isLoading,
	isSendPreparing,
	onEmailCopy,
	onCustomEmailAddress,
	onSendEmail,
	isSendEmailButtonDisabled,
	isManualMode,
	onManualSearch,
	isEnterpriseUser,
	className,
	...props
}: ContactProps) {
	const email = data?.email
	const splitedEmailInfo = email?.match(/(.*?)@(.*)/)
	// const isLoadingOrEmptyData = isLoading || isEmptyData

	const [autoInfoCard,setAutoInfoCard] = useStorageState(autoInfoCardState)


	return (
		<div className={clsx(' flex flex-col gap-4', className)} {...props}>
			<section>
				<header className="section-header justify-between">
					<div>Email</div>
					<div className='flex items-center gap-2'>
						<span className='text-[#66667c] text-[.8em]'>Auto</span>
						<div className="tooltip tooltip-top" data-tip={!isEnterpriseUser ? "Ask Admin to Enable" : ""}>
							<input
								type="checkbox"
								className="toggle toggle-sm"
								checked={autoInfoCard && isEnterpriseUser}
								onChange={(e) => isEnterpriseUser && setAutoInfoCard(e.target.checked)}
								disabled={!isEnterpriseUser}
							/>
						</div>
					</div>
				</header>
				<div className="section-body min-h-[40px] ">
					{isLoading ? (
						<button className="btn btn-outline w-full justify-start flex-nowrap cursor-default">
							<span className=" loading loading-spinner text-[#e5e6e6]" />
							<div className="flex gap-1 flex-1 h-[50%] w-[160px]">
								<div className="h-full w-full bg-base-300 rounded animate-pulse" />
							</div>
						</button>
					) : email ? (
						<a
							className=" btn btn-outline w-full justify-start flex-nowrap"
							onClick={(e) => {
								email && onEmailCopy?.(email)
								return void e.preventDefault()
							}}
							target="_blank"
							rel="noreferrer"
						>
							<svg
								className="w-8 h-8 rounded "
								xmlns="http://www.w3.org/2000/svg"
								enableBackground="new 0 0 24 24"
								height="24"
								viewBox="0 0 24 24"
								width="24"
								focusable="false"
								fill="currentColor"
							>
								<path d="M2 5v14h20V5H2zm19 1v.88l-9 6.8-9-6.8V6h18zM3 18V8.13l9 6.8 9-6.8V18H3z" />
							</svg>
							<div className=" text-ellipsis text-nowrap overflow-hidden flex " css={css``}>
								<div className=" overflow-hidden text-ellipsis">{splitedEmailInfo?.[1]}</div>
								<div>@{splitedEmailInfo?.[2]}</div>
							</div>
						</a>
					) : isManualMode ? (
						<button
							className="btn btn-outline w-full justify-start flex-nowrap border-[#e0e0e3] pr-10 bg-[#f9fafb] text-[#6c727f] !hover:bg-[#f3f4f6] !hover:text-[#000]"
							onClick={onManualSearch}
						>
							<Mail className='w-[24px] h-[24px] text-gray-400'/>
							<span>Click to find email</span>
						</button>
					) : (
						<div className="text-nowrap flex items-center gap-2 ">
							<input
								type="text"
								placeholder="Input Email Address"
								className="input input-bordered w-full "
								onChange={(e) => {
									e.target.value
								}}
							/>
							<button
								className="btn btn-outline w-16"
								onClick={(e) => {
									const nextEmail = ((e.target as HTMLButtonElement).previousSibling as HTMLInputElement).value

									onCustomEmailAddress?.(nextEmail)
								}}
							>
								Confirm
							</button>
						</div>
					)}
				</div>
				{hasEmail && <div className='text-center'>ⓘ Official email found in channel info</div>}

			</section>

			<section>
				<header className="section-header justify-between">Quick Connect</header>
				<div className="section-body   text-nowrap" css={css``}>
					<div className="join w-full ">
						<button
							className="btn btn-outline flex-1 flex items-center gap-2 join-item  relative"
							onClick={() => {
								onSendEmail?.()
							}}
							disabled={isSendEmailButtonDisabled}
						>
							{isSendPreparing && <span className="loading loading-spinner" />}
							<div
								className={clsx({
									'-translate-y-2': !!emailTemplate
								})}
							>
								{!emailTemplate ? 'Select Email Template' : !email ? isManualMode ? 'Find email first' : 'Invalid Email' : 'Send Email'}
								</div>
							{emailTemplate && (
								<div className="text-xs opacity-60 absolute bottom-1 left-50% -translate-x-0.5 z-10 max-w-[calc(100%-1rem)] truncate">
									Using: {emailTemplate?.name}
								</div>
							)}
						</button>

						<EmailTemplateDropdown
							menuButtonClassName="join-item !rounded-l-none btn-outline w-16"
							onNew={async () => {
								await sendMessage('openSidePanel', void 0)

								setTimeout(async () => {
									await sendMessage('navigateTo', 'establish-contact')
									await sendMessage('toogleEmailTemplateModal', true)
								}, 2000)
							}}
						/>
					</div>
				</div>
			</section>
		</div>
	)
}
