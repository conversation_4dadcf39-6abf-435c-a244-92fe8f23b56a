import { useState } from "react"
import { createPortal } from 'react-dom';
import { Eye, EyeOff, Plus, X } from "lucide-react"
import { Greeting } from "@/@types/easyDm";
import { getEasyMessageService } from "@/services/EasyMessageService";
import { easyDMGreetingsState } from "@/utils/storages";
import { cardMessage } from "@/hooks/useMessage";
import { createSendEvent } from "@/utils/analysisPost";

const sendEvent = createSendEvent('easy_dm')

export default function EasyDM() {
  const [greetings, setGreetings] = useStorageState(easyDMGreetingsState)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newGreeting, setNewGreeting] = useState<{ title: string; content: string; isVisible: boolean }>({
    title: "",
    content: "",
    isVisible: true,
  })

  // Function to send a greeting
  const sendGreeting = (greeting: string) => {
    sendEvent('click_send')

    cardMessage.promise(()=>{
      return getEasyMessageService().addSendMessageTask(getNickName(), getMessageLink(), greeting)
    },{
      pending:"Sending greeting...",
      success:"Greeting sent successfully",
      error:"Failed to send greeting"
    })
  }

  // Function to add a new greeting
  const addGreeting = () => {
    if (newGreeting.title && newGreeting.content) {
      const newId = (greetings.length + 1).toString()
      setGreetings([
        ...greetings,
        {
          id: newId,
          title: newGreeting.title,
          content: newGreeting.content,
          isVisible: newGreeting.isVisible,
        },
      ])
      setNewGreeting({ title: "", content: "", isVisible: true })
      setShowAddDialog(false)
    }
  }

  // Function to toggle greeting visibility
  const toggleVisibility = (id: string) => {
    setGreetings(
      greetings.map((greeting) => (greeting.id === id ? { ...greeting, isVisible: !greeting.isVisible } : greeting)),
    )
  }

  const deleteGreeting = (id: string) => {
    setGreetings(greetings.filter((greeting) => greeting.id !== id))
  }

  return (
    <div onClick={(e)=>{
      e.stopPropagation()
      e.preventDefault()  // 添加这一行来阻止默认行为
    }} className="relative text-black">
      {/* EasyDM Dropdown - Positioned at left:0, top:0 */}
      <div className="absolute left-0 top-0 w-[280px] rounded-md bg-white shadow-lg border border-gray-200 z-[101]">
        <div className="py-1 px-2">
          <div className="px-2 py-1.5 text-sm font-semibold">EasyDM</div>
          <div className=" max-h-[500px] overflow-y-auto">
          {greetings.map((greeting) => (
            <div
              key={greeting.id}
              className="flex items-center justify-between px-2 py-1.5 hover:bg-gray-100 rounded-md"
            >
              <div
                onClick={() => sendGreeting(greeting.content)}
                className={`flex-1 text-sm cursor-pointer ${greeting.isDefault ? "font-bold" : ""}`}
              >
                <div className="text-xs text-gray-500">{greeting.title}</div>
                <span>{greeting.content}</span>
              </div>
              <div className="flex items-center cursor-pointer ml-2" onClick={() => toggleVisibility(greeting.id)}>
                {greeting.isVisible ? (
                  <Eye className="h-4 w-4 text-gray-500" />
                ) : (
                  <EyeOff className="h-4 w-4 text-gray-500" />
                )}
              </div>
              <div className="flex items-center cursor-pointer ml-2" onClick={() => deleteGreeting(greeting.id)}>
                <X className="h-4 w-4 text-gray-500" />
              </div>
            </div>
          ))}
          </div>
          <div className="h-px bg-gray-200 my-1"/>
          <div
            className="flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer hover:bg-gray-100 rounded-md"
            onClick={() => setShowAddDialog(true)}
          >
            <Plus className="h-4 w-4" />
            <span>Add New Template</span>
          </div>
        </div>
      </div>
      <GreetingListSimple greetings={greetings} onSendGreeting={sendGreeting} />

      {/* Add New Template Dialog */}
      {showAddDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold">Add New Message Template</h3>
              <button className="text-gray-500 hover:text-gray-700" onClick={() => setShowAddDialog(false)}>
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4 space-y-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Title</label>
                <input
                  type="text"
                  value={newGreeting.title}
                  onChange={(e) => setNewGreeting({ ...newGreeting, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter template title"
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Content</label>
                <textarea
                  value={newGreeting.content}
                  onChange={(e) => setNewGreeting({ ...newGreeting, content: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[80px]"
                  placeholder="Enter message content"
                />
              </div>

              <div className="flex items-center" 
                   onClick={(e) => {
                     e.stopPropagation(); // 阻止冒泡到父元素
                     e.preventDefault(); // 阻止默认行为
                     
                     // 直接切换状态，不依赖 onChange
                     const newValue = !newGreeting.isVisible;
                     setNewGreeting({ ...newGreeting, isVisible: newValue });
                   }}>
                <label className="inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={newGreeting.isVisible}
                    // 可以移除 onChange，因为我们现在使用上面的 onClick
                  />
                  <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-black-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-black"/>
                  <span className="ms-3 text-sm font-medium text-gray-700">Show on profile</span>
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-2 p-4 border-t">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                onClick={() => setShowAddDialog(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-black/80"
                onClick={addGreeting}
              >
                Add
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

interface GreetingListProps {
  greetings: Greeting[]
  onSendGreeting: (greeting: string) => void
}

// Even simpler version with minimal styling
function GreetingListSimple({ greetings, onSendGreeting }: GreetingListProps) {
  const brother = document.querySelector('div[class*=DivUserIdentifierWrapper]')
  const parent = brother?.parentNode
  const container = useRef(parent?.querySelector("#efluns-easy-dm-list-container"))
  if(!container.current){
    container.current = parent?.insertBefore(document.createElement('div'), brother?.nextSibling as HTMLElement) as HTMLElement
    container.current.id = "efluns-easy-dm-list-container"
  }

  return createPortal(
    <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
      {greetings
        .filter((greeting) => greeting.isVisible !== false)
        .map((greeting) => (
          <button
            key={greeting.id}
            onClick={() => onSendGreeting(greeting.content)}
            style={{
              backgroundColor: "#f3f4f6",
              border: "none",
              borderRadius: "9999px",
              padding: "4px 12px",
              fontSize: "14px",
              cursor: "pointer",
            }}
          >
            {greeting.title}
          </button>
        ))}
    </div>,
    container.current
  )
}

function getNickName(){
  return  guard(document.querySelector('[data-e2e="user-subtitle"]')?.textContent,"获取 nickname失败")
}

function getMessageLink(){
  const link =  guard((document.querySelector('a[href*="messages?"]') as HTMLAnchorElement)?.href,"获取 messageLink失败")
  const lang = new URL(link).searchParams.get("lang")
  const u = new URL(link).searchParams.get("u")
  return `https://www.tiktok.com/messages?allow_label=true&lang=${lang}&scene=business&u=${u}`
}