"use client"

import { useCallback, useMemo } from "react"
import { FakeDetectionResult } from "@/@types/card"
import { Platform } from "@/@types/platform"
import clsx from "clsx"
import { Download, Eye, ScanEye } from "lucide-react"
import { useQuery } from "@tanstack/react-query"
import { getKOLService } from "@/services/KOLService"
import { makeModuleLog } from "@/utils/log"
import { extractInsKOLHandler } from "@/utils/extractKOLHandler"
import AudienceLoading from "./AudienceLoading"
import Excel from "@/assets/img/Excel.png"


// 正确的数据结构
const demoData: FakeDetectionResult = {
  handler:"fakeDetection",
  platform:Platform.YOUTUBE,
  timestamp:new Date().toISOString(),
  statistics: {
    // 网红账户统计
    influencer: {
      sampleCount: 4, // 样本中网红账户的数量
      estimatedCount: 341, // 估算的网红账户总数
      samplePercentage: 8, // 样本中网红账户的百分比
      estimatedPercentage: 8, // 估算的总数中网红账户的百分比
    },
    // 真实人物账户统计
    realPeople: {
      sampleCount: 38, // 样本中真实人物账户的数量
      estimatedCount: 3241, // 估算的真实人物账户总数
      samplePercentage: 76, // 样本中真实人物账户的百分比
      estimatedPercentage: 76.01, // 估算的总数中真实人物账户的百分比
    },
    // 虚假账户统计
    fakeAccounts: {
      sampleCount: 8, // 样本中虚假账户的数量
      estimatedCount: 682, // 估算的虚假账户总数
      samplePercentage: 16, // 样本中虚假账户的百分比
      estimatedPercentage: 15.99, // 估算的总数中虚假账户的百分比
    },
    sampleTotal: 50, // 用于分析的样本总数
    actualFollowerCount: 4264, // 实际粉丝总数
  },
}

interface Props {
	isOpen: boolean
	onToggle: () => void
}

const log = makeModuleLog('FakeDectCard')

export default function FakeDectCard({ isOpen, onToggle }: Props) {
  const handler = extractInsKOLHandler(window.location.href)


  const {
    data: fakeData,
    isLoading,
    error
} = useQuery({
    queryKey: ['FAKE_DETECTION', handler],
    queryFn: async () => {
        const data = await getKOLService().createFakeDetectionTask(handler!)
        log(data)
        return data
    },
    //展开的时候发请求，并且缓存时间无限，这样能确保只有第一次展开的时候会请求
    enabled: isOpen,
    staleTime: Infinity
})

const sampleData = useMemo(()=>fakeData?.result, [fakeData])



  return (
    <>
        <button
        className={clsx('btn btn-outline relative infocardBtn', isOpen ? 'btn-active hover:opacity-80' : '')}
        onClick={onToggle}
    >
        <ScanEye size={18}/>
        <span>Fake Check</span>
    </button>
      {isOpen && <div className="absolute max-h-[900px] -bottom-3 right-0 translate-y-[100%] bg-[#f6f6f8] rounded-md pb-[20px] px-[4%] pt-0 w-full max-w-4xl">
        
        {error ? (
            //  @ts-expect-error 添加一个code
            !error.statusCode ? (
                <div className='py-6 text-center w-full'>
                    <span>Error: {error.message}</span>
                </div>
            ) : (
                <FakeDectCardContent sampleData={sampleData!} taskId={fakeData?.taskId || ""} isFreeUser={true}/>
            )
        ) : isLoading ? <AudienceLoading/> : (
            <FakeDectCardContent sampleData={sampleData!} taskId={fakeData?.taskId || ""}/>
        )}
      </div>
      }
    </>
  )
}


function FakeDectCardContent({sampleData,taskId,isFreeUser}:{sampleData:FakeDetectionResult,taskId:string,isFreeUser?:boolean}){
    const finalData = isFreeUser ? demoData : sampleData

    const exportExcel = useCallback(async () => {
        getKOLService().exportFakeDetectionTask(taskId)
    }, [taskId])

    // 计算总数和百分比
    const totalEstimated =
      finalData.statistics.influencer.estimatedCount +
      finalData.statistics.realPeople.estimatedCount +
      finalData.statistics.fakeAccounts.estimatedCount

    // 准备水平条形图数据
    const barData = [
      {
        name: "Real People",
        value: Math.round((finalData.statistics.realPeople.estimatedCount / totalEstimated) * 100),
        color: "#10B981"
      },
      {
        name: "Fake Accounts", 
        value: Math.round((finalData.statistics.fakeAccounts.estimatedCount / totalEstimated) * 100),
        color: "#F97316"
      },
      {
        name: "Influencer",
        value: Math.round((finalData.statistics.influencer.estimatedCount / totalEstimated) * 100),
        color: "#3B82F6"
      }
    ]

    return <>
        {isFreeUser && (
          <div className="absolute inset-0 pointer-events-none select-none overflow-hidden z-[9] backdrop-blur-sm bg-white/30">
            {Array.from({ length: 3 }).map((_, i) => (
              <div
                key={i}
                className="absolute text-[#e5e5e5] text-4xl font-bold whitespace-nowrap"
                style={{
                  top: `${i * 35}%`,
                  left: '50%',
                  transform: 'translateX(-50%) translateY(80px) rotate(-30deg)',
                  letterSpacing: '0.5rem',
                  color:'#f09837',
                  fontWeight:'900',
                  fontSize:'30px',
                  width: '100%',
                  textAlign: 'center'
                }}
              >
                ⚠️ NOT REAL DATA
              </div>
            ))}
          </div>
        )}
            {/* Header */}
            <div className="flex items-center mt-5 text-[18px] font-semibold text-gray-800 gap-2">
          <ScanEye size={20}/>
          Fake Check
        </div>

        {/* Horizontal Bar Charts */}
        <div className="space-y-4 my-8">
          {barData.map((item, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xl font-medium text-gray-700">{item.name}</span>
                <span className="text-2xl font-bold" style={{ color: item.color }}>
                  {item.value}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${item.value}%`,
                    backgroundColor: item.color,
                  }}
                />
              </div>
            </div>
          ))}
        </div>

        {/* Total Count */}
        {/* <div className="text-center mb-5">
          <div className="text-[32px] font-bold text-gray-800 mb-1 leading-none">
            {finalData.statistics.actualFollowerCount.toLocaleString()}
          </div>
          <div className="text-[14px] text-gray-400 font-normal">
            Total followers count
          </div>
        </div> */}

        {/* Divider */}
        <div className="h-px bg-gray-200 mb-4" />

        {/* Download Button */}
        <button
          onClick={exportExcel}
          className="w-full py-3 px-4 bg-white border border-gray-200 rounded-lg text-[18px] font-medium text-gray-700 cursor-pointer flex items-center justify-center gap-2 transition-all duration-200 mb-3 hover:bg-gray-50 hover:border-gray-300"
        >
          <img src={Excel} className="w-[18qpx] h-[18px]" alt="" />
          Download detailed Excel
        </button>

        {/* Description */}
        <div className="text-[14px] text-gray-400 text-center leading-[1.4]">
          See sample accounts from each category
        </div>
    </>
}