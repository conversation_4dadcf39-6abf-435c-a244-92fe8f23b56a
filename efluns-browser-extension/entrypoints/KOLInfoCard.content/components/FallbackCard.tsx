import clsx from 'clsx'
import { HTMLMotionProps, motion } from 'framer-motion'
import { ReactNode } from 'react'

export interface FallbackCardProps extends HTMLMotionProps<'div'> {
	children?: ReactNode
}

export default function FallbackCard({ className, children, ...props }: FallbackCardProps) {
	return (
		<motion.div
			initial={{
				opacity: 0
			}}
			animate={{
				opacity: 1
			}}
			exit={{
				opacity: 0
			}}
			className={clsx(' w-full card bg-base-100 drop-shadow-lg rounded-box overflow-hidden', className)}
			{...props}
		>
			<div className="card-body justify-center items-center">{children}</div>
		</motion.div>
	)
}
