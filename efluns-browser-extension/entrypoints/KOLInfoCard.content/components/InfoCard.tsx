import { ErrCardType } from '@/@types/card'
import { EMAIL_REG } from '@/constants/reg'
import { getEasykolCookieService } from '@/services/EasykolCookieService'
import { getEmailAuthorizationService } from '@/services/EmailAuthorizationService'
import {
	getUserAreaFromIntro,
	getUserAreaFromIntroCache,
	setUserAreaFromIntroCache
} from '@/utils/ins/getUserAreaFromIntro'
import { getChineseName, getCountryCodeFromName } from '@/utils/tiktok/getCountryIcon'
import { getUserRegion } from '@/utils/tiktok/getUserRegion'
import clsx from 'clsx'
import { HTMLMotionProps, motion } from 'framer-motion'
import { Settings } from 'lucide-react'
import useKOLInfo from '../hooks/useKOLInfo'
import useToast from '../hooks/useToast'
import Contact from './Contact'
import KOLFooter from './KOLFooter'
import KOLStatistics from './KOLStatistics'
import ManualCard from './ManualCard'
import Region from './Region'
import SettingsPanel from './settings-panel'
import { MarqueeNotification } from './MarqueeNotification'
const log = makeModuleLog('InfoCard')
const $event = createAnalysisPost('InfoCard')

export interface InfoCardProps extends HTMLMotionProps<'div'> {}

export default function InfoCard({ className, ...props }: InfoCardProps) {
	const { isInYoutubeKOLPage, isInTikTokKOLPage, isInInsKOLPage } = useSupportPageInfo()
	const { isShow: isToastShow, toast, type: toastType, content: toastContent } = useToast()
	const [notifies] = useStorageState(notifiesState)
    const notify = useMemo(() => notifies.find(notify => notify.valid && notify.position === 'INFOCARD'), [notifies]);

	const {
		platformData,
		data: kolInfo,
		error: searchInfoErr,
		tiktokLinks,
		onEmailUpdate,
		isLoading: isKOLInfoLoading,
		sendEmail,
		isSendEmailPreparing,
		region: currentRegion,
		hasEmail,
		isManualMode,
		triggerManualSearch,
		refetchKolData,
		isEnterpriseUser
	} = useKOLInfo()

	const [isSettingsOpen, setIsSettingsOpen] = useState(false)

	function getPlatformValue<T>(youtubeValue: T, tiktokValue: T, insValue: T): T | undefined {
		return isInYoutubeKOLPage ? youtubeValue : isInTikTokKOLPage ? tiktokValue : isInInsKOLPage ? insValue : undefined
	}

	const links = getPlatformValue(platformData.youtube?.links, tiktokLinks, [])
	const region = getPlatformValue(platformData.youtube?.region, currentRegion, currentRegion)

	const [insRegion, setInsRegion] = useState<string | undefined>(undefined)
	const [tiktokRegion, setTiktokRegion] = useState<string | undefined>(undefined)

	useEffect(() => {
		if (isInInsKOLPage) {
			const hander = extractInsKOLHandler(window.location.href)
			if (!hander) return
			const cacheData = getUserAreaFromIntroCache(hander)
			if (cacheData) {
				setInsRegion(cacheData)
			} else {
				autoInfoCardState.getValue().then((value) => {
					if (!value) {
						getUserAreaFromIntro()
					}
				})
			}
			const unlisten = listenEvent('INS_AREA', (data) => {
				setInsRegion(data)
				setUserAreaFromIntroCache(hander, data)
			})
			return () => {
				unlisten()
			}
		}
		// if (isInTikTokKOLPage) {
		// 	getKOLService().getKOLRegion({
		// 		platform:Platform.TIKTOK,
		// 		handler:extractKOLHandler(window.location.href)!
		// 	}).then(res=>{
		// 		setTiktokRegion(res.region)
		// 	})
			// getUserRegion(window.location.href).then((data) => {
			// 	setTiktokRegion(data)
			// })
		// }
	}, [isInInsKOLPage])

	const currentRegionInfo = useMemo(() => {
		if (region) {
			return {
				region: isInChina() ? getChineseName(region) : region,
				code: region
			}
		} else if (tiktokRegion) {
			return {
				region: isInChina() ? getChineseName(tiktokRegion) : tiktokRegion,
				code: tiktokRegion
			}
		} else if (insRegion) {
			return {
				region: isInChina() ? getChineseName(getCountryCodeFromName(insRegion)) : insRegion,
				code: getCountryCodeFromName(insRegion)
			}
		}
	}, [insRegion, region, tiktokRegion])

	const [emailTemplateId] = useStorageState(currentEmailTemplateIdState)
	const [emailTemplateList] = useStorageState(emailTemplatesState)
	const emailTemplate = useMemo(
		() => emailTemplateList.find((item) => item.id === emailTemplateId),
		[emailTemplateId, emailTemplateList]
	)

	const isLimit = useMemo(
		// @ts-expect-error 添加一个 code 用于弹窗
		() => searchInfoErr instanceof Error && searchInfoErr.statusCode && searchInfoErr.statusCode >= 1900,
		[searchInfoErr]
	)

	const handleEmailSend = useCallback(
		() =>
			void sendEmail()
				.then(() => {
					toast('success', 'Sending...Please check in Gmail')
					$event('sendEmail', {
						kolId: kolInfo?.id
					})
				})
				.catch((err) => {
					toast('error', `Sending failed. need gmail authorization`)
					// 邮箱授权登录
					const emailAuthService = getEmailAuthorizationService()
					emailAuthService.requestEmailAuthorization()
				}),
		[kolInfo?.id, sendEmail, toast]
	)

	const handleEmailCopy = useCallback(
		async (email: string) => {
			try {
				await navigator.clipboard.writeText(email)
				toast('success', 'Copy successful')
			} catch {
				toast('error', 'Copy failed')
			}
			$event('copyEmail', {
				kolId: kolInfo?.id
			})
		},
		[kolInfo?.id, toast]
	)

	useEffect(() => {
		if (!isInYoutubeKOLPage) return
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				// 如果是文本内容变化
				if (mutation.type === 'characterData') {
					const targetNode = mutation.target as HTMLElement
					const parentElement = targetNode.parentElement

					if (parentElement?.id === 'email') {
						const email = parentElement.innerText
						if (email.trim() && EMAIL_REG.test(email)) {
							onEmailUpdate?.(email, 'reveal_button')
							observer.disconnect()
						}
					}
				}
			})
		})

		observer.observe(document.body, {
			childList: true,
			subtree: true,
			characterData: true,
			characterDataOldValue: true
		})

		return () => observer.disconnect()
	}, [isInYoutubeKOLPage, onEmailUpdate])

	// if(isLimit) return <LimitCard isOpen={true} toggleOpen={()=>{}} classname={clsx({
	// 	'!w-[300px]':isInYoutubeKOLPage,
	// 	'!w-[400px] text-[1.25rem]':isInTikTokKOLPage || isInInsKOLPage

	// })}/>
	const limitCardType: ErrCardType | null = useMemo(() => {
		if (isLimit) {
			return 'limit'
		} else if (searchInfoErr instanceof Error) {
			return 'error'
		}
		return null
	}, [isLimit, searchInfoErr])

	const handleManualCardClick = useCallback(
		async (type: ErrCardType) => {
			if (type === 'manual') {
				triggerManualSearch()
			} else if (type === 'limit' || type === 'error') {
				// 处理 limit 情况的逻辑
				// 比如打开升级页面等
				// @ts-expect-error 企业用户
				if (searchInfoErr?.statusCode <= 1911) {
					window.open('https://airy-update.notion.site/EasyKOL-14ae8694012c804595fffc6afe85eefa', '_blank')
				} else {
					// sync session to web
					await getEasykolCookieService().requestCookiePermission()
					// @ts-expect-error 企业用户
					const url = [1912, 1921].includes(searchInfoErr?.statusCode)
						? 'https://easykol.com/settings/enterpriseInfo'
						: 'https://easykol.com/settings/quotaQuery'
					window.open(url, '_blank')
				}
			}
			// else if(type === 'error'){
			// 	refetchKolData()
			// }
		},
		// @ts-expect-error 企业用户
		[triggerManualSearch, searchInfoErr?.statusCode]
	)

	if (isSettingsOpen) return <SettingsPanel onClose={() => setIsSettingsOpen(false)} />

	return (
		<motion.div className={clsx(className, ' flex gap-4 w-full')} {...props}>
			<div className="h-full relative card bg-base-100 drop-shadow">
				{/* {isLimit && <div className='absolute top-0 left-0 w-full h-full'>
					<LimitCard isOpen={true} toggleOpen={()=>{}} classname={clsx("w-full h-full max-w-none z-[999] text-[1.5rem]",{
					
						// '!w-[300px]':isInYoutubeKOLPage,
						// '!w-[400px] text-[1.25rem]':isInTikTokKOLPage || isInInsKOLPage
					})}/>
				</div>} */}
				{isToastShow && (
					<div
						css={css`
							.alert-error {
								background-color: #ffe3e3;
								color: #ff0000;
								border-color: #ff0000;
							}
						`}
						className="fixed top-2 z-10 toast toast-top toast-center"
					>
						<div className={` text-center alert alert-${toastType} flex`}>
							<span>{toastContent}</span>
						</div>
					</div>
				)}

				<div className="flex justify-between items-center">
					<Region
						currentRegionInfo={currentRegionInfo!}
						links={links}
						isLoading={isKOLInfoLoading}
						limitCardType={limitCardType}
					/>
					<div onClick={() => setIsSettingsOpen(true)}>
						<Settings className="w-6 h-6 mr-2 cursor-pointer" />
					</div>
				</div>

				<div
					className={clsx('sections card-body flex flex-row p-[20px] pt-[10px]', {
						'!p-4': isInYoutubeKOLPage
					})}
					css={css`
						gap: 2rem;

						.section-header {
							display: flex;
							align-items: center;
							gap: 0.5rem;
							font-size: 1.25rem;
							line-height: 1.75rem;
							font-weight: 600;
							text-wrap: nowrap;
							height: 30px;
						}
					`}
				>
					{limitCardType ? (
						<ManualCard type={limitCardType} onCardClick={handleManualCardClick} err={searchInfoErr} />
					) : (
						<Contact
							className={clsx({
								'min-w-[280px]': isInTikTokKOLPage,
								'min-w-[200px]': isInYoutubeKOLPage
							})}
							data={kolInfo!}
							hasEmail={hasEmail}
							emailTemplate={emailTemplate}
							isLoading={isKOLInfoLoading}
							isSendPreparing={isSendEmailPreparing}
							isSendEmailButtonDisabled={!kolInfo?.email || !emailTemplateId || isSendEmailPreparing}
							onSendEmail={handleEmailSend}
							onCustomEmailAddress={(email) => onEmailUpdate?.(email, 'user_submit')}
							onEmailCopy={handleEmailCopy}
							isManualMode={isManualMode}
							onManualSearch={triggerManualSearch}
							isEnterpriseUser={isEnterpriseUser}
						/>
					)}

					<KOLStatistics
						videos={isInYoutubeKOLPage ? platformData.youtube?.videos : []}
						regionCode={currentRegionInfo?.code}
						onOpenSettings={setIsSettingsOpen}
					/>
				</div>
				<KOLFooter
					toast={toast}
					limitCardType={limitCardType}
					isLoading={isKOLInfoLoading}
					links={links?.slice(0, 5)}
					region={region!}
				/>
				<div className="w-full">
					{notify?.content && <MarqueeNotification message={notify?.content} />}
				</div>
			</div>
		</motion.div>
	)
}
