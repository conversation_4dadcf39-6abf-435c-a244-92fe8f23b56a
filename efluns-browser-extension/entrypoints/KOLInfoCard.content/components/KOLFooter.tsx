import { Link } from "@/@types"
import TagWordCard from "./TagWordCard"
// import { TTShopDataCard } from "./TTShopDataCard"
import AudienceCard from "./AudienceCard"
import { ErrCardType } from "@/@types/card"
import ListCard from "./ListCard"
import { Toast } from "react-hot-toast"
import FakeDectCard from "./FakeDectCard"
import { getPlatform } from "@/entrypoints/Entrypoint.content/utils"
import { Platform } from "@/@types/platform"


interface TiktokFooterProps{
    region:string | undefined
    links:Link[] | undefined
    isLoading:boolean
    limitCardType:ErrCardType | null
    toast?:(type: "error" | "success", content: string) => void
}

const $event = createAnalysisPost("InfoCard")

export default function KOLFooter({region,links,isLoading,limitCardType,toast}:TiktokFooterProps){
    type OpenCard = 'tags' | 'audience' | 'shop' | 'list' | 'fakeDetect'
    const [openCard, setOpenCard] = useState<OpenCard | null>(null);
    const platform = getPlatform(location.href)
    const isInReelsPage = location.href.includes("/reels/")

    const handleCardToggle = useCallback((cardType: OpenCard) => {
        if(isInReelsPage && (cardType === "tags" || cardType === "list")){
            toast?.("error","Please switch to the POSTS page to view")
            return
        }
        const control = openCard === cardType ? null : cardType
        setOpenCard(control);
        ttsSearchOpen.setValue(control === "shop")
    }, [isInReelsPage, openCard, toast]);

    useEffect(()=>{
        ttsSearchOpen.getValue().then(bool=>{
            if(bool){
                setOpenCard("shop")
            }
        })
    },[])

    const isInYoutubeKOLPage = platform === Platform.YOUTUBE
    const isInInstagramKOLPage = platform === Platform.INS
    
    
    return <div className="flex gap-2 px-4 pb-2 items-center whitespace-nowrap">        
            <div className="flex items-center gap-2 min-w-fit ml-auto">
                {/* {isInTikTokKOLPage && <TTShopDataCard
                    isOpen={openCard === 'shop'} 
                    onToggle={() => handleCardToggle('shop')} 
                    isLoading={isLoading}
                    region={region}
                />} */}
                {isInInstagramKOLPage && <FakeDectCard
                    isOpen={openCard === 'fakeDetect'} 
                    onToggle={() => handleCardToggle('fakeDetect')} 
                />}
                {<AudienceCard
                    isOpen={openCard === 'audience'} 
                    onToggle={() => handleCardToggle('audience')} 
                />}
                {!isInYoutubeKOLPage && <TagWordCard 
                    isOpen={openCard === 'tags'} 
                    onToggle={() => handleCardToggle('tags')} 
                    disabled={isInReelsPage}
                />}
                {!isInYoutubeKOLPage&& <ListCard
                    isOpen={openCard === 'list'} 
                    onToggle={() => handleCardToggle('list')} 
                    disabled={isInReelsPage}
                />}
            </div>
        

    </div>
}