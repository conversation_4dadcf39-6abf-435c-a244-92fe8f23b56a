import { InstagramChannelData, InstagramPost, TiktokChannelData, YoutubeVideo } from '@/@types'
import getLatestVideosViewCountStatistics, { getInstagramStatistics, getTiktokStatistics, getYoutubeStatistics } from '@/utils/youtube/getLatestVideosViewCountStatistics'
import { useAtom, useAtomValue } from 'jotai'
import { HTMLAttributes } from 'react'
import { lastVideoDateState, latestVideoAmountState } from '../store'
import { getTiktokVideoData } from '@/entrypoints/TiktokEarlyScript.content/utils'
import { getStoredInstagramData, listenFromInstagramEarlyScript } from '@/entrypoints/InstagramEarlyScript.content/utils'
import { DefaultDisplaySettings, videoLastDateList } from '@/constants/infocard'
import { StatItemProps, StatsCardProps } from '@/@types/card'
import clsx from 'clsx'
import Hide from '@/assets/img/hide.png'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import StatsDisplay from './StatsCard'
import { Platform } from '@/@types/platform'

const log = makeModuleLog('KOLStatistics')
const $event = createAnalysisPost("InfoCard")
export interface YoutubeKOLStatisticsProps extends HTMLAttributes<HTMLDivElement> {
	videos: YoutubeVideo[] | undefined
	regionCode:string | null | undefined
	onOpenSettings?: (bool:boolean) => void
}


export default function KOLStatistics({ videos,onOpenSettings,regionCode, ...props }: YoutubeKOLStatisticsProps) {
	const [lastVideoDateId,setLastVideoDateId] = useAtom<typeof lastVideoDateState>(lastVideoDateState)
	const { isInYoutubeKOLPage, isInTikTokKOLPage,isInInsKOLPage } = useSupportPageInfo()
	const [tiktokVideoData,setTiktokVideoData] = useState<TiktokChannelData>(getTiktokVideoData(location.href)!)
	const [instagramPostsData,setInstagramPostsData] = useState<InstagramChannelData>(getStoredInstagramData(location.href)!)
	const [pricingConfig] = useStorageState(pricingConfigState)
	const platform = getPlatform(location.href)
	const [displaySettings] = useStorageState(displaySettingsState)
	const lastVideoDate = useAtomValue(lastVideoDateState)
	const shouldShowGlass = platform === Platform.INS && !location.href.includes("/reels") && !!document.querySelector("[role=tablist] a[href*=reels]")


	useEffect(() => {
		let cleanup: (() => void) | undefined;
		
		if (isInTikTokKOLPage) {
			cleanup = listenEvent('FROM_TIKTOK_EARLY_SCRIPT',(ttdata) => {
				log("收到来自 TikTok early-script 的消息:", ttdata);
				setTiktokVideoData(ttdata.data);
			});
		} else if (isInInsKOLPage) {
			cleanup = listenFromInstagramEarlyScript((igdata) => {
				setInstagramPostsData(igdata);
			});
		}

		return () => {
			cleanup?.();
		};
	}, [isInTikTokKOLPage, isInInsKOLPage]); // 依赖项添加页面类型

	// useEffect(() => {
	// 	listenEvent("DATA_MONITOR_ERROR",(data) => {
	// 		log("数据监控错误:", data);
	// 	});
	// }, []);

	const statsCardProps = useMemo(
		() => {
			let data = {stats:[] as StatItemProps[]}
			if(isInYoutubeKOLPage){
				data = getYoutubeStatistics(videos || [],lastVideoDateId)
			}else if(isInTikTokKOLPage){
				data =  getTiktokStatistics(tiktokVideoData?.videos || [],lastVideoDateId)
			}else if(isInInsKOLPage){
				data = getInstagramStatistics(instagramPostsData?.posts || [],lastVideoDateId)
			}

			
			return {
				stats: Object.keys(DefaultDisplaySettings)
					.filter(key => displaySettings[key as keyof typeof displaySettings] && data?.stats.find(e => e.key === key))
					.map(key => {
						const stat = data?.stats.find(e => e.key === key)						
						if (key === "estimatedPrice") {
							return {
								...stat,
								value: Math.ceil((pricingConfig[platform as Exclude<Platform, Platform.TWITTER>][getCountryPriority(regionCode)] * Number(stat!.value))/1000/10) * 10
							}
						}
						return stat
					})
					.filter(e=>e?.value) as StatItemProps[]
			}
		},
		[displaySettings, instagramPostsData?.posts, isInInsKOLPage, isInTikTokKOLPage, isInYoutubeKOLPage, lastVideoDateId, platform, pricingConfig, regionCode, tiktokVideoData?.videos, videos]
	)
	

	const needRefresh = useMemo(()=>{
		if(platform === Platform.YOUTUBE) return false
		const length = (isInTikTokKOLPage?tiktokVideoData?.videos?.length:instagramPostsData?.posts?.length) || 0
		return (lastVideoDate === "last-10-posts" && length <10) || (lastVideoDate === "last-5-posts" && length <5)
	},[isInTikTokKOLPage, tiktokVideoData?.videos?.length, instagramPostsData?.posts?.length, lastVideoDate, platform])

	const isInReelInsKOLPage = isInInsKOLPage && location.href.includes("/reels")
	const isInTaggedInsKOLPage = isInInsKOLPage && location.href.includes("/tagged/")
	
	return (
		<>
			<div className={clsx("flex flex-col gap-4 w-full flex-1 ",{
				"min-w-[130px]":isInYoutubeKOLPage,
				// "hidden":!statsCardProps.stats.length
			})}>
				<section className="">
					<header className="section-header card-title justify-end ">
						<select
						className="select select-ghost "
						value={lastVideoDateId}
						onChange={(e) => {
							setLastVideoDateId(e.target.value as any)
						}}
					>
						{(isInReelInsKOLPage || isInTaggedInsKOLPage?videoLastDateList.filter(item=>item.type === "count"):videoLastDateList).map((item) => (
							<option value={item.id} key={item.id}>
								{item.label}
							</option>
						))}
					</select>

					</header>

					{/* <div className="stats  section-body glass w-full mt-[10px]">
						<div className="stat py-8 text-center">
							<div className="stat-title">{isInInsKOLPage?'Avg Likes':'Avg Views'}</div>
							<div className="stat-value">{countFormatter.format(avgViewCount)}</div>
							<div className="stat-desc" />
						</div>

						<div className="stat py-8 text-center border-none">
							<div className="stat-title">Trimmed Views</div>
							<div className="stat-value">{countFormatter.format(trimmedAvgViewCount)}</div>
							<div className="stat-desc" />
						</div>

					</div> */}
					<StatsDisplay statsData={statsCardProps.stats} onOpenSettings={onOpenSettings} shouldShowGlass={shouldShowGlass} needRefresh={needRefresh} />
				</section>

				{/* {links.length > 0 && isInYoutubeKOLPage && (
					<section className="">
						<ul className="section-body flex gap-1 w-full flex-wrap">
							{links?.map((item) => (
								<li key={item.title} className=" rounded-2xl ">
									<a
										href={item.url}
										target="_blank"
										rel="noreferrer"
										className="btn btn-square glass p-0 inline-flex justify-center items-center tooltip hover:tooltip-open"
										data-tip={item.title}
									>
										<img src={item.icon} className=" w-8 h-8 rounded" alt={item.title} />
									</a>
								</li>
							))}
						</ul>
					</section>
				)} */}
			</div>
		</>
	)
}
