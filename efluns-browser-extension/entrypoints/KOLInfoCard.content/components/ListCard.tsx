import React, { useState, useEffect } from 'react';
import {  getListCount } from '@/utils/tiktok/getList';
import clsx from 'clsx';
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils';
import { Platform } from '@/@types/platform';
import { User, Users } from "lucide-react"
import { CardModal } from './CardModal';
import { createSendEvent } from '@/utils/analysisPost';


interface Props {
  isOpen: boolean;
  onToggle: () => void;
  disabled?: boolean;
}

const sendEvent = createSendEvent('list')
export default function ListCard({ isOpen, onToggle, disabled }: Props) {
    const [list, setList] = useState<[string, number][] | null>(null);
    const [isShowMessage, setIsShowMessage] = useState(false);


    useEffect(() => {
        if (isOpen) {
            const list = getListCount() || [];
            setList(list);
            sendEvent('open')
        }
    }, [isOpen]);

    const routerToKolPage = (username: string) => {
        const platform = getPlatform(window.location.href)
        if(platform === Platform.TIKTOK){
            window.open(`https://www.tiktok.com/@${username}`, '_blank')
        }else{
            window.open(`https://www.instagram.com/${username}`, '_blank')
        }
    }

    const openAllProfiles = useCallback(() => {
        list?.slice(0, 15).forEach(([item, count]) => {
            routerToKolPage(item)
        })
    }, [list])

    const loadMore = ()=>{
        setIsShowMessage(true)
    }

    const closeModal = ()=>{
        onToggle()
        setIsShowMessage(false)
    }

    return <>
        <button 
            className={clsx('btn btn-outline relative', isOpen ? 'btn-active hover:opacity-80' : '', disabled && 'opacity-50 cursor-not-allowed')}
            onClick={onToggle}
        >
            <svg className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none">
                <path d="M621.920904 425.220339c0 1.446328-1.012429 9.545763-10.99209 49.030508l-26.033899 98.639548c-8.388701 29.215819-26.467797 55.683616-53.948022 78.824859-29.215819 25.021469-58.865537 37.749153-88.081356 37.749153-25.6 0-45.414689-8.822599-60.60113-27.480226-14.463277-16.343503-21.694915-40.20791-21.694915-70.870057 0-71.448588 20.39322-131.760452 60.456497-179.055367l0.144632-0.289265C458.485876 366.354802 500.429379 344.225989 549.749153 344.225989c22.851977 0 40.20791 8.967232 53.224858 27.769491 12.727684 16.632768 18.946893 34.133333 18.946893 53.224859m324.555932 290.277966c-2.314124-4.19435-6.653107-6.653107-11.425989-6.653107h-62.481355c-4.19435 0-8.244068 2.024859-10.702825 5.496045-31.963842 45.125424-75.20904 81.862147-128.144633 109.487006-61.468927 30.662147-134.074576 46.282486-215.79209 46.282485-106.59435 0-193.80791-30.951412-259.181921-91.841807-65.952542-63.059887-99.362712-151.575141-99.362712-263.231639 0-104.135593 34.567232-191.349153 102.978531-259.037288 67.543503-68.266667 153.889266-102.833898 256.433898-102.833898 96.036158 0 174.427119 28.637288 232.858757 84.754802 54.671186 54.815819 82.440678 123.516384 82.440678 204.366102 0 68.555932-20.827119 129.735593-61.758192 181.514124-34.856497 42.955932-69.279096 64.650847-102.110734 64.650847-20.39322 0-22.128814-8.388701-22.128814-17.500565 0-16.777401 4.049718-37.170621 12.149153-61.468926L741.966102 300.40226c1.012429-3.905085 0.144633-8.099435-2.314125-11.281356-2.458757-3.181921-6.363842-5.062147-10.413559-5.062147H672.542373c-5.929944 0-10.99209 3.905085-12.583051 9.545763l-9.111864 31.963842c-22.851977-41.79887-59.444068-63.059887-109.053108-63.059887-71.159322 0-133.785311 31.819209-185.99774 94.155932-54.960452 62.770621-82.729944 141.306215-82.729943 233.581921 0 50.910734 15.331073 93.577401 45.270056 126.842938 29.79435 33.844068 70.580791 51.055367 121.20226 51.055367 53.224859 0 100.664407-21.550282 141.59548-64.216949 13.59548 50.332203 51.633898 61.902825 83.453108 61.902824 59.877966 0 116.574011-32.108475 168.352542-96.036158 52.357062-67.977401 78.824859-144.343503 78.824859-227.362712 0-103.846328-34.133333-190.625989-101.532204-258.024858-71.159322-72.027119-168.063277-108.474576-287.674576-108.474577-129.59096 0-238.210169 43.389831-322.820339 128.723164C115.706215 287.240678 73.184181 390.942373 73.184181 512.867797c0 129.59096 41.509605 235.60678 123.371751 315.154802 81.717514 77.812429 189.902825 117.152542 321.518644 117.152542 93.288136 0 179.055367-19.525424 254.698305-58.287005 73.907345-37.315254 132.19435-90.540113 173.41469-158.228249 2.458757-3.905085 2.458757-8.967232 0.289265-13.161582" stroke="currentColor" strokeWidth="2.67" fill='currentColor' />
            </svg>
            <span>List</span>
        </button>
        {isOpen && (
            <div className='p-4 pt-0 absolute -bottom-3 left-0 w-full h-auto max-h-[300px] overflow-y-auto translate-y-[100%] bg-[#f6f6f8] rounded-md'>
               <ul className='text-[20px]'>
               <li  className='flex justify-between items-center px-[20px] py-[15px] border-b border-[#E5E5E5] cursor-pointer'>
                        <span className='text-[#252447] w-[80%] truncate flex items-center gap-2 text-[22px] font-bold'>@User List <span className='text-[#727886]'>({list?.length})</span></span>
                        <button onClick={loadMore} className='btn btn-outline btn-md mr-2'>Load More </button>
                        <button onClick={openAllProfiles} className='btn btn-outline btn-md'><Users className='h-4 w-4 text-gray-500' /> Open All Profiles</button>
                    </li>
                {list?.length ? list?.map(([item, count]) => (
                    <li onClick={() => routerToKolPage(item)} className='flex justify-between items-center px-[20px] py-[10px] border-b border-[#E5E5E5] hover:bg-[#E5E5E5] cursor-pointer last:border-b-0' key={item}>
                        <span className='text-[#252447] w-[80%] truncate flex items-center gap-2'><User className="h-4 w-4 text-gray-500" />@{item}</span>
                        <span className='text-[#6C6C85]'>{count}</span>
                    </li>
                )): <li className='flex justify-center items-center h-full text-[#6C6C85]'>No list found</li>}
               </ul>
                {/* <button className='absolute top-[3px] right-[8px] border-none bg-transparent' 
                    onClick={onToggle}>⬆️hide</button> */}
                {isShowMessage && <CardModal close={()=>closeModal()} />}

            </div>
        )}
    </>
}


