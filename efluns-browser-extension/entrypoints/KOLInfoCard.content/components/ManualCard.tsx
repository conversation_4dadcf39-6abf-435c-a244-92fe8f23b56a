import { ErrCardType } from '@/@types/card';
import lockIcon from '../../../assets/img/lock.png'
import clsx from 'clsx';

interface ManualCardProps {
  type: ErrCardType;
  onCardClick: (type: ErrCardType) => void;
  err:{ statusCode?:number } & Error | null
}

function ManualCard({ type, onCardClick,err }: ManualCardProps) {
  return (
		<div className={clsx("rounded-lg px-[10px] flex justify-center items-center flex-col bg-white shadow-md border border-[#cccccc47] mt-2 max-w-[170px]",
		{
        "flex-1":type !== 'manual'
      }
    )}>
			<div className="flex items-center justify-center">
				<img src={lockIcon} alt="lock" className="w-8 h-8 mb-2" />
			</div>
			<div className="text-center">
				<h3 className="text-gray-600 text-lg mb-2 max-w-[20rem]">
				<span>{err?.message}</span>
				</h3>
				<button
					onClick={() => {
						onCardClick(type)
					}}
					className="bg-[#1a2b3c] text-white px-6 py-2 rounded-full"
				>
					{type === 'limit' ? <span>{err?.statusCode && err?.statusCode > 1911? 'Check' : 'Upgrade'}</span> : type === 'error' ? <span>update</span> : <span> Find Email</span>}
				</button>
			</div>
		</div>
	)
};

export default ManualCard;
