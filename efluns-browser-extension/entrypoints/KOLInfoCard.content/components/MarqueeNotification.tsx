import { useState, useEffect, useRef } from "react"
import { X } from "lucide-react"

interface MarqueeNotificationProps {
  message: string
  speed?: number
  onClose?: () => void
}

export function MarqueeNotification({ message, speed = 30, onClose }: MarqueeNotificationProps) {
  const [isVisible, setIsVisible] = useState(true)
  const containerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const [contentWidth, setContentWidth] = useState(0)
  const [containerWidth, setContainerWidth] = useState(0)
  const [position, setPosition] = useState(0)
  const [isHovered, setIsHovered] = useState(false)

  useEffect(() => {
    if (containerRef.current && contentRef.current) {
      setContainerWidth(containerRef.current.offsetWidth)
      setContentWidth(contentRef.current.offsetWidth)
      setPosition(containerWidth)
    }
  }, [containerWidth])

  useEffect(() => {
    if (!isVisible || containerWidth === 0 || contentWidth === 0 || isHovered) return

    const animate = () => {
      setPosition((prevPosition) => {
        if (prevPosition < -contentWidth) {
          return containerWidth
        }
        return prevPosition - 1
      })
    }

    const intervalId = setInterval(animate, speed)
    return () => clearInterval(intervalId)
  }, [isVisible, containerWidth, contentWidth, speed, isHovered])

  const handleClose = () => {
    setIsVisible(false)
    if (onClose) onClose()
  }

  if (!isVisible) return null

  return (
      <div
        className="mx-auto flex items-center py-2 px-4 float-end w-full"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div ref={containerRef} className="overflow-hidden w-full">
          <div
            ref={contentRef}
            className="whitespace-nowrap text-red-800 font-medium"
            css={css`
            a {
              color: #b91c1c !important;
            }
            `}
            style={{ transform: `translateX(${position}px)` }}
          >
            <span dangerouslySetInnerHTML={{__html:message}} />
          </div>
        </div>
        {/* <button
          onClick={handleClose}
          className="ml-4 text-gray-500 hover:text-gray-700 focus:outline-none"
          aria-label="关闭通知"
        >
          <X className="h-4 w-4" />
        </button> */}
      </div>
  )
}
