import { Link } from "@/@types"
import { ErrCardType } from "@/@types/card"
import { getCountryIcon } from "@/utils/tiktok/getCountryIcon"
import { getPlatform } from "@/entrypoints/Entrypoint.content/utils"
import { Platform } from "@/@types/platform"
import { extractInsAccountFromDescription } from "../utils/getEmailFromDescription"

interface Props{
    currentRegionInfo:{
        code:string | null
        region:string | null | undefined
    }
    links:Link[] | undefined
    isLoading:boolean
    limitCardType:ErrCardType | null
}

export default function Region({currentRegionInfo,links,isLoading,limitCardType}:Props){
    const platform = getPlatform(window.location.href)
    const [notifies] = useStorageState(notifiesState)
    const notify = useMemo(() => notifies.find(notify => notify.valid && notify.position === 'INFOCARD'), [notifies]);

    const finalLinks = useMemo(()=>{
        if(links?.some(item=>item.url.includes('instagram'))){
            return links
        }
        const insLink = extractInsAccountFromDescription()
        if(insLink){
            return [insLink,...(links||[])]
        }
        return links
    },[links])

    const fn:any = useCallback((message:any,sender:any,sendResponse:any)=>{
		if(message.type === "getInfoCardRegion"){
			sendResponse(currentRegionInfo?.code)
		}
	},[currentRegionInfo]) 

	// 处理消息监听
	useEffect(() => {
		browser.runtime.onMessage.addListener(fn)
		return ()=>{
			browser.runtime.onMessage.removeListener(fn)
		}
	},[fn]) 

    function RegionAvatar(){
        if(isLoading && platform === Platform.TIKTOK){
            return <span className=" loading loading-spinner text-[#e5e6e6] mx-[20px]" />
        }
        return currentRegionInfo?.code && <div className="flex items-center gap-2 ml-2">
        {/* Region:  */}
            <div className="w-8 h-8 rounded-full border border-gray-300 overflow-hidden"><img src={getCountryIcon(currentRegionInfo.code)!} className="w-full h-full object-cover" /></div> 
            <span className="text-[1.2em] whitespace-nowrap">{currentRegionInfo.region}</span>
    </div>
    }

    return (
        <div className="flex bg-white w-full rounded-lg pt-4 px-[2px]  text-center gap-[20px] border-b min-h-[40px]">
        {RegionAvatar()}
            <>
            {!!finalLinks?.length && <ul className="section-body flex gap-1 flex-wrap whitespace-nowrap">
                {finalLinks?.slice(0,6).map((item) => (
                    <li key={item.title} className=" rounded-2xl ">
                        <a
                            href={item.url}
                            target="_blank"
                            rel="noreferrer"
                            className="btn btn-square glass p-0 inline-flex justify-center items-center tooltip hover:tooltip-open"
                            data-tip={item.title}
                        >
                            <img src={item.icon} className=" w-8 h-8 rounded" alt={item.title} />
                        </a>
                    </li>
                ))}
        </ul>}
        </>
        </div>
    )
}