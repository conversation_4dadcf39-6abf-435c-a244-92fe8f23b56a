import trackIcon from '@/assets/img/track.png'
import { getEasykolCookieService } from '@/services/EasykolCookieService';
import { css } from '@emotion/react'; // 导入 css


async function jumpToTrackPage(){
    await getEasykolCookieService().requestCookiePermission()
    window.open(`https://easykol.com/dataManagement/easykolTrack?link=${window.location.href}`, '_blank')
}

export function SingleTrackBtn() {
    return <button onClick={(e)=>{
        e.stopPropagation()
        e.preventDefault()
        jumpToTrackPage()
    }} 
    css={css`
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        padding: 6px 12px;
        border-radius: 8px;
        background-color: #fff;
        font-size: 12px;
        line-height: 100%;
        letter-spacing: 0%;
        vertical-align: middle;
        color: #000;
        border: 1px solid #6C6C85;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
            background-color: #f0f0f0; // 悬停时的背景色
        }
    `}>
        <img style={{width: '12px', height: '12px'}} src={trackIcon} alt="track" />
        <span>{'TrackData'}</span>
    </button>
}