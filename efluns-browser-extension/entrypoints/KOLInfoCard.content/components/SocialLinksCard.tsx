import { ContactInfo, ContactType } from "@/@types/kol"
import { faEnvelope } from "@fortawesome/free-regular-svg-icons"
import { faChevronDown, faLink, faPhone } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { useQuery } from "@tanstack/react-query"
import clsx from "clsx"
import { Toaster } from "react-hot-toast"
import useToast from "../hooks/useToast"
import { useEffect, useState } from "react"
import { getSocialIcon } from "../utils/socialCardUtil"

interface SocialLinksCardProps{
  linkInfo:{
    userLink:string[]
    linkContent:string
  }
    patchKolLinks:(links:string[])=>Promise<ContactInfo[]>
    linkClick:()=>void
}

export default function SocialLinksCard({linkInfo,patchKolLinks,linkClick}:SocialLinksCardProps){
    const [isExpanded, setIsExpanded] = useState(true)
    const { isShow: isToastShow, toast, type: toastType, content: toastContent } = useToast()
    const [autoCollapsed, setAutoCollapsed] = useState(false)
    const {isInYoutubeKOLPage,isInTikTokKOLPage,isInInsKOLPage} = useSupportPageInfo()

    const {data:links,isLoading} = useQuery({
        queryKey:['FETCH_KOL_LINKS_CONTACT',linkInfo.linkContent],
        enabled:!!linkInfo.userLink?.length && isExpanded,
        queryFn:async ()=>{
            return patchKolLinks(linkInfo.userLink)
        },
        staleTime:Infinity
    })

    const computedlLinks = useMemo(()=>{
    if(!links) return []
    const socialLinks = links.filter(link=>link.type === ContactType.SOCIAL && ['youtube','instagram','tiktok'].includes(link.linkType))
    const otherLinks = links.filter(link=>link.type !== ContactType.SOCIAL)
    return [...socialLinks,...otherLinks]
    },[links])

    useEffect(() => {
        if(!isLoading && !links?.length && !autoCollapsed){
            setAutoCollapsed(true)
            setIsExpanded(false)
        }
    }, [autoCollapsed, isLoading, links])

    const copyLink = (link:string) => {
        navigator.clipboard.writeText(link)
				toast('success', 'Copy successful')
    }

    const handleLinkClick = () => {
        const link = linkInfo.userLink[0]
        window.open(link.startsWith('http')?link:`https://${link}`)
    }
    return (<>
        <div className={clsx("w-full max-w-[180px] z-[2] absolute  translate-x-[-10px]",{
          "ml-[9px]":isInYoutubeKOLPage && isExpanded,
          "!translate-x-[-260px] !z-[1] translate-y-[20px]":isInInsKOLPage,
          // "!translate-x-[-220px]":isInTikTokKOLPage,
        })}>
        <Toaster />
        {isToastShow && (
					<div className="absolute -top-8 z-10 toast toast-top toast-center !p-[2px]">
						<div className={` text-center alert alert-${toastType} flex`}>
							<span className="text-[10px]">{toastContent}</span>
						</div>
					</div>
				)}
          <div className={clsx("rounded-lg bg-white p-2   text-[#787878] text-[12px]",{
            "border border-[#cacaca] shadow-sm":isExpanded
          })}>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex w-full items-center gap-2 rounded-t-xl p-1 hover:bg-gray-50"
            >
              <FontAwesomeIcon
                icon={faChevronDown}
                className={clsx(
                  "w-[12px] h-[12px] min-w-[12px] min-h-[12px] text-gray-500 transition-transform",
                  isExpanded ? "rotate-0" : "-rotate-90"
                )}
              />
              <span onClick={linkClick} className=" text-sm hover:underline truncate">{linkInfo.linkContent}</span>
            </button>
            
            <div
              className={clsx(
                "grid transition-all duration-200 ease-in-out",
                isExpanded ? "grid-rows-[1fr]" : "grid-rows-[0fr]"
              )}
            >
              {isLoading?<div className="flex justify-center items-center h-full gap-2">
                <span className="loading w-4 h-4" /> 
              </div>:
              <div className="overflow-hidden">
                {!computedlLinks?.length && <div className="text-center text-gray-500">No Data</div>}
                {computedlLinks?.map((item,index)=>item.type === ContactType.SOCIAL?(
                   <button
                   key={index}
                   onClick={()=>{window.open(item.url)}}
                   className="flex w-full items-center gap-2 p-1 hover:bg-gray-50 border border-[#cacaca] rounded-lg px-2 my-2"
                 >
                   <img src={getSocialIcon(item.linkType)} alt={item.linkType} className="w-4 h-4" />
                   <span className=" truncate">{item.content}</span>
                 </button>
                ):(
                   <button
                    key={index}
                    onClick={()=>{copyLink(item.content)}}
                    className="flex w-full justify-between items-center gap-2 p-1 hover:bg-gray-50 border border-[#cacaca] rounded-lg px-2 my-2"
                  >
                    <div className="flex items-center gap-2">
                    <FontAwesomeIcon icon={item.type === 'email' ? faEnvelope : item.type === 'phone' ? faPhone : faLink} className="h-4 w-4 text-gray-500" />
                    <span className=" truncate">{item.content}</span>
                    </div>
                    <img src={getSocialIcon(item.linkType)} alt={item.linkType} className={clsx("w-3 h-3",{'hidden':getSocialIcon(item.linkType) === undefined})} />
                  </button>
                ))}
              </div>
              }
            </div>
          </div>
        </div>
      </>
    )
}
