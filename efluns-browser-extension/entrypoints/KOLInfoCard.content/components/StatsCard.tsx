"use client"

import { useState } from "react"
import { Play, Heart, MessageCircle, Calculator, DollarSign, Info } from "lucide-react"
import { StatItemProps } from "@/@types/card"
import Hide from '@/assets/img/hide.png'

interface StatsDisplayProps {
  statsData: StatItemProps[]
  onOpenSettings?: (bool:boolean) => void
  shouldShowGlass?: boolean
  needRefresh?: boolean
}

interface DisplaySettings {
  views: string
  likes: string
  comments: string
  er: string
  estimatedPrice: string
}

export default function StatsDisplay({ statsData, onOpenSettings, shouldShowGlass, needRefresh }: StatsDisplayProps) {
  const [showInfoTooltip, setShowInfoTooltip] = useState(false)

  const openReelsPage = useCallback(()=>{
    (document.querySelector("[role=tablist] a[href*=reels]") as HTMLAnchorElement).click()
  },[])

  // Icon mapping for different stat types
  const getIconForStat = (key: keyof DisplaySettings) => {
    switch (key) {
      case "views":
        return Play
      case "likes":
        return Heart
      case "comments":
        return MessageCircle
      case "er":
        return null // Special case for ER text
      case "estimatedPrice":
        return DollarSign
      default:
        return null
    }
  }

  // Get first stat for first row
  const firstStat = statsData[0]
  // Get remaining stats for second row
  const remainingStats = statsData.slice(1)

  return (
    <div className="mt-6 relative">

      {/* 毛玻璃遮罩层 - 当 shouldShowGlass 为 true 时显示 */}
      {shouldShowGlass ? (
      <div className="absolute z-10 inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-lg">
        <button className="bg-[#1a2b3c] text-white px-6 py-2 rounded-full" onClick={openReelsPage}>
          Reels Data
        </button>
      </div>
      ):needRefresh?<div className="absolute z-10 inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-lg">
      <button className="bg-[#1a2b3c] text-white px-6 py-2 rounded-full" onClick={()=>{location.reload()}}>
        Refresh
      </button>
    </div>
    :<></>}

      <div className="space-y-4">
        {/* First Row - First stat item (always single item, centered) */}
        {firstStat && (
          <div className="relative">
            <div className="flex justify-center">
              <div className="flex items-center gap-3">
                {(() => {
                  const Icon = getIconForStat(firstStat.key)
                  if (Icon) {
                    return <Icon className="h-5 w-5 text-gray-500 flex-shrink-0" />
                  } else if (firstStat.key === "er") {
                    return (
                      <div className="h-5 w-5 text-gray-500 flex-shrink-0 text-xs font-semibold flex items-center justify-center">
                        ER
                      </div>
                    )
                  }
                  return null
                })()}
                {firstStat.value === "hide" ? (
                  <div className="flex items-center justify-center"><img src={Hide} alt="hide" className="w-[24px] h-[24px]" /></div>
                ) : (
                  <div className="text-3xl font-bold text-gray-800">{firstStat.value}</div>
                )}
              </div>
            </div>

            {/* Show settings buttons only for price-related stats */}
            {statsData.length > 2 && (
              <div className="absolute top-0 right-0 flex items-center gap-1">
                <button
                  className="flex items-center justify-center rounded-full w-8 h-8 p-0 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-200 transition-colors"
                  onClick={()=>onOpenSettings?.(true)}
                >
                  <Calculator className="h-4 w-4 text-gray-600" />
                </button>
                <div className="relative">
                  <button
                    className="flex items-center justify-center rounded-full w-8 h-8 p-0 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-200 transition-colors"
                    onMouseEnter={() => setShowInfoTooltip(true)}
                    onMouseLeave={() => setShowInfoTooltip(false)}
                  >
                    <Info className="h-4 w-4 text-gray-600" />
                  </button>
                  {showInfoTooltip && (
                    <div className="absolute top-full right-0 mt-2 w-64 p-3 bg-gray-800 text-white text-xs rounded-lg shadow-lg z-20">
                      <div className="space-y-2">
                        <div className="font-semibold">Data Sources:</div>
                        <div>
                          • <strong>Price:</strong> Calculated based on CPM rates and view count
                        </div>
                        <div>
                          • <strong>Views:</strong> Median views from recent posts
                        </div>
                        <div>
                          • <strong>Likes:</strong> Median likes from recent posts
                        </div>
                        <div>
                          • <strong>ER:</strong> Median engagement rate from recent posts
                        </div>
                        <div>
                          • <strong>Comments:</strong> Median comments from recent posts
                        </div>
                      </div>
                      {/* Arrow pointing up-right to the button */}
                      <div className="absolute bottom-full right-4 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-800"/>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Second Row - Remaining stats (dynamic centering based on count) */}
        {remainingStats.length > 0 && (
          <div className="flex justify-center">
            <div
              className="flex items-center justify-around"
              style={{
                // Ensure proper spacing regardless of item count
                minWidth: remainingStats.length === 1 ? "auto" : `${remainingStats.length * 120}px`,
              }}
            >
              {remainingStats.map((stat) => {
                const Icon = getIconForStat(stat.key)
                return (
                  <div key={stat.key} className="flex items-center justify-center gap-3 flex-1">
                    {Icon ? (
                      <Icon className="h-4 w-4 text-gray-500 flex-shrink-0" />
                    ) : stat.key === "er" ? (
                      <div className="h-4 w-4 text-gray-500 flex-shrink-0 text-xs font-semibold flex items-center justify-center">
                        ER
                      </div>
                    ) : null}
                    {stat.value === "hide" ? (
				<div className="flex items-center justify-center"><img src={Hide} alt="hide" className="w-[24px] h-[24px]" /></div>
			) : <div className="text-3xl font-bold text-gray-800">{stat.value}</div>}
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Empty state when no stats are provided */}
        {statsData.length === 0 && (
          <div className="flex justify-center py-8">
            <div className="text-gray-400 text-sm">No statistics to display.</div>
          </div>
        )}
      </div>
    </div>
  )
}
