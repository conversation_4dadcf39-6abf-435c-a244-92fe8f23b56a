import { createTTShopUrl, TTSErrorMessage, TTSOffscreenMessage } from "@/entrypoints/offscreen/const";
import { getTTShopService } from "@/services/TTShopService"
import { createSendEvent } from "@/utils/analysisPost";
import { useQuery } from "@tanstack/react-query"
import clsx from "clsx";


interface Props {
    isOpen: boolean;
    onToggle: () => void;
    isLoading: boolean;
    region?: string;
}


const sendEvent = createSendEvent('ttshop')

export function TTShopDataCard({ isOpen, onToggle }: Props){

    
    const handle = extractKOLHandler(window.location.href)?.replace("@","") || null

    const {data:domain,error:domainError,refetch:domainRefetch} = useQuery({
        queryKey: ['TIKTOK_SHOP_DOMAIN'],
        queryFn: () => {
            const domain = getTTShopService().getLastTTShopDomain()
            sendEvent('open')
            return domain
        },
        enabled: isOpen,
    })


    const {data, error,refetch} = useQuery({
        queryKey: ['TIKTOK_SHOP_DATA', handle],
        queryFn: () => getTTShopService().searchCreator(handle,60000,domain!),
        enabled: !!domain,
        staleTime: Infinity
    })
    

    useEffect(() => {
        const messageListener:any = (message: any) => {
            if (message.type === TTSOffscreenMessage.REFRESH_QUERY) {   
                if(error?.message === TTSErrorMessage.NEED_CAPTCHA){
                    refetch();
                }             
                
            }
        };

        browser.runtime.onMessage.addListener(messageListener);
        
        // 清理函数,组件卸载时移除监听器
        return () => {
            browser.runtime.onMessage.removeListener(messageListener);
        };
    }, [error?.message, refetch]);


    const formatData = useMemo(()=>{
        if(!data) return null

        const gmvTotal = data.video_gmv?.value?.format || data.med_gmv_revenue_range?.value || 0
        const gmvPerUnit = data.avg_revenue_per_buyer?.value?.format || data.avg_revenue_per_buyer_range?.value || 0
        const gpm = data.gpm?.value?.format || data.gpm_range?.value || 0
        const shoppableVideos = data.ec_video_publish_cnt_30d?.value || 0

        return {
            gmvTotal,
            gmvPerUnit,
            gpm,
            shoppableVideos
        }
    },[data])

    const openTikTokShop = useCallback(()=>{
            if(!domain){
                window.open("https://seller-us-accounts.tiktok.com/account/login")
            }else if(!data?.creator_oecuid?.value){
                window.open(createTTShopUrl(domain), '_blank')
            }else{
                window.open(createTTShopUrl(domain,data?.creator_oecuid.value), '_blank')
            }

    },[data?.creator_oecuid.value, domain])

    const errorData = useMemo(()=>{
        if(domainError) return domainError
        if(error) return error
        return null
    },[domainError,error])


    const allRefetch = useCallback(async()=>{
        if(domainError){
            await domainRefetch()
        }
        if(error){
            await refetch()
        }
    },[domainError, domainRefetch, error, refetch])

    // const hideBtn = useCallback(()=>{
    //     return <button 
    //     className='absolute top-[3px] right-[8px] border-none bg-transparent flex items-center' 
    //     onClick={onToggle}
    // >
    //     <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    //         <path d="M18 15l-6-6-6 6"/>
    //     </svg>
    //     hide
    // </button>
    // },[onToggle])

    return <>
        <button 
            id="ttsBtn"
            className={clsx('btn btn-outline relative', isOpen ? 'btn-active hover:opacity-80' : '')}
            onClick={onToggle}
        >
            <svg width="18" height="18" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4.7928 13.279C4.62716 11.7055 5.86094 10.335 7.44315 10.335H23.5568C25.1391 10.335 26.3728 11.7055 26.2072 13.279L25.0493 24.279C24.9065 25.6353 23.7628 26.665 22.399 26.665H8.60105C7.23721 26.665 6.09346 25.6353 5.95069 24.279L4.7928 13.279Z" stroke="currentColor" strokeWidth="2.67"/>
                <path d="M21 10V7.5C21 4.46243 18.5376 2 15.5 2V2C12.4624 2 10 4.46243 10 7.5V10" stroke="currentColor" strokeWidth="2.67"/>
                <circle cx="10.75" cy="15.25" r="1.75" fill="currentColor"/>
                <circle cx="20.25" cy="15.25" r="1.75" fill="currentColor"/>
            </svg>
            <span>Shop</span>

        </button>
        
        {isOpen && (
            <div className="absolute max-h-[500px] pt-5 pb-5 -bottom-3 left-0 w-full translate-y-[100%] bg-white rounded-md">
                {formatData ? (
                    <>
                    <div className="rounded-md flex items-center justify-evenly">
                        <div className="border p-4 rounded-md hover:shadow-sm transition-shadow font-medium text-gray-900 text-center">
                            <div className="text-[#66667c]">GMV</div>
                            <div className="text-xl font-bold">{formatData?.gmvTotal}</div>
                        </div>
                        <div className="border p-4 rounded-md hover:shadow-sm transition-shadow font-medium text-gray-900 text-center">
                            <div className="text-[#66667c]">GMV Per Customer</div>
                            <div className="text-xl font-bold">{formatData?.gmvPerUnit}</div>
                        </div>
                            <div className="border p-4 rounded-md hover:shadow-sm transition-shadow font-medium text-gray-900 text-center">
                            <div className="text-[#66667c]">GPM</div>
                            <div className="text-xl font-bold">{formatData?.gpm}</div>
                        </div>
                        <div className="border p-4 rounded-md hover:shadow-sm transition-shadow font-medium text-gray-900 text-center">
                            <div className="text-[#66667c]">Shoppable Videos</div>
                            <div className="text-xl font-bold">{formatData?.shoppableVideos}</div>
                        </div>
                    </div>
                    <div className="mt-4 w-full flex justify-between items-center">
                        <div className="flex-1"/>
                        <button onClick={openTikTokShop} className="btn btn-outline mx-auto rounded-xl">
                            Open in TikTok Shop {'>'}
                        </button>
                        <span className="flex-1 text-right pr-4 text-[#737388]">last 30 days</span>
                    </div>

                    </>
                ) : errorData?<div className="flex flex-col items-center justify-center text-[14px]">
                <span className="text-red-500 font-medium block">{errorData.message}</span>
                {errorData.message !== TTSErrorMessage.NO_DATA && <div className="flex items-center gap-3 whitespace-nowrap mt-3">
                    <span>Try to open and check</span>
                    <span onClick={openTikTokShop} className="text-blue-600 hover:text-blue-800 underline cursor-pointer">
                        Tiktok Shop
                    </span>
                    <span>and</span>
                    <button 
                        onClick={()=>{allRefetch()}}
                        className="px-4 py-2 bg-black text-white rounded-md hover:opacity-80 transition-colors"
                    >
                            Try again
                        </button>
                    </div>}
                </div>:(
                    <div className="py-6 text-center flex items-center justify-center gap-2">
                        <span className="loading loading-spinner" />🔍 Getting store data, please wait...
                    </div>
                )}
                {/* {hideBtn()} */}
            </div>
        )}
    </>;
}