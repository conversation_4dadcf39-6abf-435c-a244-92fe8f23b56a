import React, { useState, useEffect } from 'react';
import {  getTagsCount } from '@/utils/tiktok/getTags';
import clsx from 'clsx';
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils';
import { Platform } from '@/@types/platform';
import { CardModal } from './CardModal';
import { createSendEvent } from '@/utils/analysisPost';

const sendEvent = createSendEvent('tags')
interface Props {
  isOpen: boolean;
  onToggle: () => void;
  disabled?: boolean;
}

export default function TagWordCard({ isOpen, onToggle, disabled }: Props) {
    const [tags, setTags] = useState<[string, number][] | null>(null);
    const [isShowMessage, setIsShowMessage] = useState(false);


    useEffect(() => {
        if (isOpen) {
            const tagList = getTagsCount() || [];
            setTags(tagList);
            sendEvent('open')
        }
    }, [isOpen]);

    const routerToTag = (tag: string) => {
        const platform = getPlatform(window.location.href)
        if(platform === Platform.TIKTOK){
            window.open(`https://www.tiktok.com/tag/${tag}`, '_blank')
        }else{
            window.open(`https://www.instagram.com/explore/search/keyword/?q=%23${tag}`, '_blank')
        }
    }

    const openAllTags = useCallback(() => {
        tags?.slice(0, 15).forEach(([tag, count]) => {
            routerToTag(tag)
        })
    }, [tags])


    const loadMore = ()=>{
        setIsShowMessage(true)
    }

    const closeModal = ()=>{
        onToggle()
        setIsShowMessage(false)
    }

    return <>
        <button 
            className={clsx('btn btn-outline relative', isOpen ? 'btn-active hover:opacity-80' : '', disabled && 'opacity-50 cursor-not-allowed')}
            onClick={onToggle}
        >
            <svg width="18" height="18" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.10938 11.5547H26.6649" stroke="currentColor" strokeWidth="2.66667" strokeLinecap="round"/>
                <path d="M10.668 25.7812L13.3346 6.22569" stroke="currentColor" strokeWidth="2.66667" strokeLinecap="round"/>
                <path d="M18.668 25.7812L21.3346 6.22569" stroke="currentColor" strokeWidth="2.66667" strokeLinecap="round"/>
                <path d="M6.22266 20.4453H25.7782" stroke="currentColor" strokeWidth="2.66667" strokeLinecap="round"/>
            </svg>
            <span>Tags</span>
        </button>
        {isOpen && (
            <div className='p-4 pt-0 absolute -bottom-3 left-0 w-full h-auto max-h-[300px] overflow-y-auto translate-y-[100%] bg-[#f6f6f8] rounded-md'>
               <ul className='text-[20px]'>
               <li  className='flex justify-between items-center px-[20px] py-[15px] border-b border-[#E5E5E5] cursor-pointer'>
                        <span className='text-[#252447] w-[80%] truncate flex items-center gap-2 text-[22px] font-bold'>Tag List <span className='text-[#727886]'>({tags?.length})</span></span>
                        <button onClick={loadMore} className='btn btn-outline btn-md mr-2'>Load More </button>
                        <button onClick={openAllTags} className='btn btn-outline btn-md'>Open All Tags</button>
                    </li>
                {tags?.length ? tags?.map(([tag, count]) => (
                    <li onClick={() => routerToTag(tag)} className='flex justify-between items-center px-[20px] py-[10px] border-b border-[#E5E5E5] hover:bg-[#E5E5E5] cursor-pointer last:border-b-0' key={tag}>
                        <span className='text-[#252447] w-[80%] truncate'>#{tag}</span>
                        <span className='text-[#6C6C85]'>{count}</span>
                    </li>
                )): <li className='flex justify-center items-center h-full text-[#6C6C85]'>No tags found</li>}
               </ul>
                {/* <button className='absolute top-[3px] right-[8px] border-none bg-transparent' 
                    onClick={onToggle}>⬆️hide</button> */}
                {isShowMessage && <CardModal close={()=>closeModal()} />}
            </div>
        )}
    </>
}

