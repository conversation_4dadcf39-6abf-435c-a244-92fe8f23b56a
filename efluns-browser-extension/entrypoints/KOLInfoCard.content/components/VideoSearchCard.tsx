import React, { useState, useCallback, useEffect, useRef } from 'react';
import { debounce } from 'lodash-es';

interface Props {
    isOpen: boolean;
    onToggle: () => void;
}


export default function VideoSearchCard({ isOpen, onToggle }: Props) {
    const [searchText, setSearchText] = useState('');
    const [matchCount, setMatchCount] = useState(0);
    const [currentMatch, setCurrentMatch] = useState(0);
    const [matchedElements, setMatchedElements] = useState<Element[]>([]);
    const [isSticky, setIsSticky] = useState(false);
    const inputRef = useRef(null);
    
    useEffect(() => {
        if (!isOpen) {
            // 清除搜索状态
            setSearchText('');
            setMatchCount(0);
            setCurrentMatch(0);
            setMatchedElements([]);
            // 清除所有高亮
            document.querySelectorAll('.video-search-highlight').forEach(el => 
                el.classList.remove('video-search-highlight')
            );
        }
    }, [isOpen]);

    const debouncedSearch = useCallback(
        debounce((text: string) => {
                    // 先清除所有之前的高亮
        document.querySelectorAll('.video-search-highlight').forEach(el => 
            el.classList.remove('video-search-highlight')
        );

        if (!text) {
            setMatchedElements([]);
            setMatchCount(0);
            setCurrentMatch(0);
            return;
        }
        
        const result = [...document.querySelectorAll("picture img")]
            .filter((item) => (item as HTMLImageElement).alt.includes(text));
        
        setMatchedElements(result);
        setMatchCount(result.length);
        
        if (result.length > 0) {
                setCurrentMatch(1);
                result[0].classList.add('video-search-highlight');
            }
        }, 500),
        []
    );

    const handleSearch = useCallback((text: string) => {
        setSearchText(text);
        debouncedSearch(text);
    }, [debouncedSearch]);

    // 处理上下切换
    const handleNavigate = (direction: 'prev' | 'next') => {
        const newMatch = direction === 'prev' 
            ? Math.max(1, currentMatch - 1)
            : Math.min(matchCount, currentMatch + 1);
        
        if (newMatch !== currentMatch) {
            // 移除之前的高亮
            matchedElements[currentMatch - 1]?.classList.remove('video-search-highlight');
            // 添加新的高亮
            matchedElements[newMatch - 1]?.classList.add('video-search-highlight');
            setCurrentMatch(newMatch);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            handleSearch(searchText);
        }
    };

    // 自动滚动到当前匹配项
    useEffect(() => {
        if (currentMatch > 0 && matchedElements[currentMatch - 1]) {
            matchedElements[currentMatch - 1].scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }, [currentMatch, matchedElements]);

    useEffect(() => {
        const handleScroll = () => {
            if (inputRef.current) {
                const rect = (inputRef.current as HTMLElement).getBoundingClientRect();
                // 当元素即将离开视口底部时，设置为固定
                if (rect.bottom < 0) {
                    setIsSticky(true);
                } else {
                    setIsSticky(false);
                }
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    return <>
        <button 
        ref={inputRef}
            className='relative whitespace-nowrap bg-white cursor-pointer px-4 py-2 font-bold rounded-md hover:bg-gray-100 text-[12px] border'
            onClick={onToggle}
        >
            {isOpen ? '🔍 HomePage Search' : '👀 HomePage Search'}
        </button>
        {isOpen && (
            <>
                <div 
                    className={`${
                        isSticky 
                            ? 'fixed top-[10px] right-[20px] w-full max-w-[400px] z-50 rounded-md bg-[#f6f6f8] scale-[.8]' 
                            : 'absolute bottom-[-10px] left-[50%] translate-x-[-50%] w-full min-w-[400px] scale-[.8] translate-y-[100%] bg-[#f6f6f8] rounded-md p-2 before:content-[""] before:absolute before:top-[-10px] before:right-[45%] before:border-l-[10px] before:border-l-transparent before:border-r-[10px] before:border-r-transparent before:border-b-[10px] before:border-b-[#f6f6f8]'
                    }`}
                >
                    <div className='flex items-center gap-2 bg-white rounded-md shadow-sm border border-gray-200'>
                        <div className='flex-1 flex items-center'>
                            <span className='px-2 text-gray-400'>🔍</span>
                            <input
                                type="text"
                                className='w-full py-1 px-2 outline-none'
                                placeholder='Search the description of the loaded video'
                                value={searchText}
                                onChange={(e) => handleSearch(e.target.value)}
                                onKeyDown={handleKeyDown}
                            />
                        </div>
                        {searchText && (
                            <div className='flex items-center gap-1 px-2 text-sm text-gray-600 border-l'>
                                <span>{currentMatch}/{matchCount}</span>
                                <div className='flex gap-1'>
                                    <button 
                                        className='p-1 hover:bg-gray-100 rounded'
                                        onClick={() => handleNavigate('prev')}
                                    ><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M19 15l-7-7-7 7"/></svg></button>
                                    <button 
                                        className='p-1 hover:bg-gray-100 rounded'
                                        onClick={() => handleNavigate('next')}
                                    ><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M5 9l7 7 7-7"/></svg></button>
                                </div>
                            </div>
                        )}
                        <button 
                            className='p-2 hover:bg-gray-100 text-gray-500'
                            onClick={onToggle}
                        >✕</button>
                    </div>
                </div>
                {/* 添加高亮组件 */}
            </>
        )}
    </>;
}
