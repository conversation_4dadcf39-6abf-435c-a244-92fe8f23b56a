import { Platform } from '@/@types/platform'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import { getEasykolCookieService } from '@/services/EasykolCookieService'
import { getKOLService } from '@/services/KOLService'
import Grabber from '@/assets/img/Grabber.png'

interface WebSiteSearchEntryProps {
	type: 'tt_search' | 'tt_tag' | 'tt_following' | 'youtube_search' | 'youtube_tag' | 'ins_tagged' | 'ins_following' | 'ins_tag'
}

const followingSelector = '[data-e2e="follow-info-popup"]>div:nth-child(2)>div:nth-child(1)'

export function WebSiteSearchEntry() {
	const [type, setType] = useState<WebSiteSearchEntryProps['type'] | null>(null)

	const changeType = useCallback(() => {
		const url = location.href
		const platform = getPlatform(url)

		if(platform === Platform.YOUTUBE && url.includes("search_query")){
			setType("youtube_search")
		}else if(platform === Platform.YOUTUBE && url.includes("/hashtag")){
			setType("youtube_tag")
		}else if(platform === Platform.INS && url.includes("/tagged")){
			setType("ins_tagged")
		}else if(platform === Platform.INS && url.includes("/following")){
			setType("ins_following")
		}else if(platform === Platform.INS && url.includes("/search/keyword")){
			setType("ins_tag")
		}else if(platform === Platform.TIKTOK && url.includes("/tag")){
			setType("tt_tag")
		}else if(platform === Platform.TIKTOK && url.includes("/search?")){
			setType("tt_search")
		}else if(platform === Platform.TIKTOK && document.querySelector('[data-e2e="follow-info-popup"]>div:nth-child(2)>div:nth-child(1)')){
			setType("tt_following")
		}else{
			setType(null)
		}
	},[])

	const followerObserver = useCallback(()=>{
		const observer = new MutationObserver((mutations) => {
			if (!location.href.startsWith('https://www.tiktok.com/@')) return
			const hasElement = !!document.querySelector(followingSelector)
			if (hasElement) {
				setType("tt_following")
			} else{  // 只有当当前type是tt_following时才设置为null
				setType(null)
			}
		})
	
		observer.observe(document.body, {
			childList: true,
			subtree: true
		})
	
		return () => {
			observer.disconnect()
		}
	},[])

	useEffect(() => {
		window.navigation?.addEventListener('navigatesuccess', () => {
			changeType()
		})
		changeType()
		const unfollowerObserver = followerObserver()

		return unfollowerObserver
	}, [])


	const containerStyle: React.CSSProperties = {
		// position: "absolute",
		// bottom: "0",
		// left: "50%",
		// transform: "translateX(-50%) translateY(100%)",
		zIndex:9,
		display: 'inline-flex',
		alignItems: 'center',
		gap: '20px',
		borderRadius: '7px',
		width: 'fit-content',
		whiteSpace: 'nowrap',
		transition: 'all 0.2s ease-in-out',
		cursor: 'pointer',
		position:'fixed',
		bottom:100,
		right:0
	}

	const textStyle: React.CSSProperties = {
		color: 'white',
		fontSize: '12px',
		fontWeight: 500,
		lineHeight: '20px'
	}

	const iconStyle: React.CSSProperties = {
		width: '16px',
		height: '16px',
		color: '#6B7280'
	}

	const getText = useCallback(() => {
		switch (type) {
			case 'tt_search':
				return decodeURI(new URL(location.href).searchParams.get('q') || '')
			case 'tt_tag':
				return decodeURI(location.pathname.split('/').filter(Boolean).pop() || '')
			case 'tt_following':
				return location.href
			case 'youtube_search':
				return decodeURI((document.querySelector('[name="search_query"]') as HTMLInputElement)?.value || '')
			case 'youtube_tag':
				return decodeURI(location.pathname.split('/').filter(Boolean).pop() || '')
			case 'ins_tag':
				return decodeURI(new URL(location.href).searchParams.get('q')?.replace('#', '') || '')
			case 'ins_following':
				return location.href
			case 'ins_tagged':
				return location.href
			default:
				return ''
		}
	}, [type])

	const buttonTitle = useMemo(() => {
		if (!type?.includes('ins')) {
			return type?.includes('search') ? '💥Search List' : '💥#Hashtag List'
		} else {
			switch (type) {
				case 'ins_tagged':
					return '💥TAGGED List'
				case 'ins_following':
					return '💥Following List'
				case 'ins_tag':
					return '💥Hashtag List'
				default:
					break
			}
		}
	}, [type])

	const handleClick = useCallback(async () => {
		// sync session to web
		await getEasykolCookieService().requestCookiePermission()
		// const env = await extensionEnvState.getValue()
		// const url = env === 'beta' ?  'https://bigbang-beta.easykol.com' : 'https://easykol.com'
		const url = 'https://easykol.com'
		const text = getText()
		const platform = getPlatform(location.href)
		switch (type) {
			case 'ins_tagged':
				window.open(`${url}/search/tagged?platform=INSTAGRAM&url=${text}`, '_blank')
				break
			case 'ins_following':
				window.open(`${url}/search/following?platform=INSTAGRAM&url=${text}`, '_blank')
				break
			case 'ins_tag':
				window.open(`${url}/search/hashtag?platform=INSTAGRAM&tag=${text}`, '_blank')
				break
			case 'tt_search':
				window.open(`${url}/search/input?platform=TIKTOK&input=${text}`, '_blank')
				break
			case 'tt_tag':
				window.open(`${url}/search/hashtag?platform=TIKTOK&tag=${text}`, '_blank')
				break
			case 'tt_following':
				window.open(`${url}/search/following?platform=TIKTOK&url=${text}`, '_blank')
				break
			case 'youtube_search':
			    window.open(`https://easykol.com/search/hashtag?platform=YOUTUBE&tag=${text}`, '_blank')
			break;
			case 'youtube_tag':
			    window.open(`https://easykol.com/search/hashtag?platform=YOUTUBE&tag=${text}`, '_blank')
			    break;
			default:
				window.open(`${url}/search/hashtag?platform=${platform}&hashtag=${text}`, '_blank')
				break
		}
		return
	}, [getText, type])

	// const [isYoutubeKolPage, setIsYoutubeKolPage] = useState(false)

	// useEffect(() => {
	// 	// 监听路由变化
	// 	const handleRouteChange = () => {
	// 		getKOLService()
	// 			.getIsYoutubeKOLPage(window.location.href)
	// 			.then((isYoutubeKolPage) => {
	// 				setIsYoutubeKolPage(isYoutubeKolPage)
	// 			})
	// 	}

	// 	// 初始化时记录当前 URL
	// 	// 使用 popstate 事件监听浏览器历史记录变化
	// 	window.addEventListener('yt-navigate-finish', handleRouteChange)

	// 	// 组件卸载时清理
	// 	return () => {
	// 		window.removeEventListener('yt-navigate-finish', handleRouteChange)
	// 	}
	// }, [])

	// if (isYoutubeKolPage) {
	// 	return null
	// }

	if(!type) return null

	return (
		<div style={containerStyle} className="efluns-hover-container" onClick={handleClick}>
			<img src={Grabber} width={200} alt="" />
		</div>
	)
}
