import { getEasykolCookieService } from '@/services/EasykolCookieService'

interface WebSiteTooltipEntryProps {
	type: 'following' | 'followers'
}

export function WebSiteTooltipEntry({ type }: WebSiteTooltipEntryProps) {
	const containerStyle: React.CSSProperties = {
		position: 'absolute',
		bottom: '0',
		zIndex: 1000,
		left: '50%',
		transform: 'translateX(-50%) translateY(100%)',
		display: 'inline-flex',
		alignItems: 'center',
		gap: '8px',
		backgroundColor: 'white',
		borderRadius: '7px',
		width: 'fit-content',
		whiteSpace: 'nowrap',
		transition: 'all 0.2s ease-in-out',
		cursor: 'pointer'
	}

	const arrowStyle: React.CSSProperties = {
		position: 'absolute',
		top: '-8px',
		left: '50%',
		transform: 'translateX(-50%)',
		width: '16px',
		height: '8px',
		overflow: 'hidden'
	}

	const arrowInnerStyle: React.CSSProperties = {
		position: 'absolute',
		width: '12px',
		height: '12px',
		left: '50%',
		top: '4px',
		transform: 'translateX(-50%) rotate(45deg)',
		backgroundColor: 'white',
		border: '1px solid #000',
		borderRight: 'none',
		borderBottom: 'none'
	}

	const textStyle: React.CSSProperties = {
		color: '#111827',
		fontSize: '12px',
		fontWeight: 500,
		lineHeight: '20px'
	}

	const iconStyle: React.CSSProperties = {
		width: '16px',
		height: '16px',
		color: '#6B7280'
	}

	const handleClick = async () => {
		// sync session to web
		await getEasykolCookieService().requestCookiePermission()
		// const env = await extensionEnvState.getValue()
		// const mainUrl = env === 'beta' ?  'https://bigbang-beta.easykol.com' : 'https://easykol.com'
		const mainUrl = 'https://easykol.com'
		// const links = encodeURI(location.href)
		const url =
			type === 'following'
				? `${mainUrl}/search/following?platform=TIKTOK&url=${location.href}`
				: `${mainUrl}/search/fans?platform=TIKTOK&links=${location.href}`
		window.open(url, '_blank')
	}

	return (
		<div style={containerStyle} className="efluns-hover-container" onClick={handleClick}>
			<div style={arrowStyle}>
				<div style={arrowInnerStyle} />
			</div>
			<span style={textStyle}>{type === 'following' ? 'Following List' : 'Similar from Followers'}</span>
			<svg style={iconStyle} viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					d="M11.1998 24.8255H13.3332V27.4922H6.6665V20.8255H9.33317V22.9589L13.3332 18.9589L15.1998 20.8255L11.1998 24.8255ZM21.9998 24.8255L17.9998 20.8255L19.8665 18.9589L23.9998 23.0922V20.8255H26.6665V27.4922H19.9998V24.8255H21.9998ZM11.1998 10.1589L15.1998 14.1589L13.3332 16.0255L9.33317 12.0255V14.1589H6.6665V7.49219H13.3332V10.1589H11.1998ZM21.9998 10.1589H19.9998V7.49219H26.6665V14.1589H23.9998V11.8922L19.8665 16.0255L17.9998 14.1589L21.9998 10.1589Z"
					fill="#1F2937"
				/>
			</svg>
		</div>
	)
}
