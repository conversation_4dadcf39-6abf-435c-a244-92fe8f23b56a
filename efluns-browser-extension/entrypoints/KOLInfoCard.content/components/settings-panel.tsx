"use client"

import { useState } from "react"
import { ChevronDown, Play, Heart, MessageCircle, Bookmark } from "lucide-react"
import { Platform } from "@/@types/platform"
import { getPlatform } from "@/entrypoints/Entrypoint.content/utils"
import clsx from "clsx"

interface SettingsPanelProps {
  onClose: () => void
  onSettingsChange?: (settings: any) => void
}

export default function SettingsPanel({ onClose, onSettingsChange }: SettingsPanelProps) {
  const [currentCategory, setCurrentCategory] = useState("info card")
  const platform = getPlatform(location.href)
  const [selectedPlatform, setSelectedPlatform] = useState(platform)

  // Internal state management
  const [displaySettings, setDisplaySettings] = useStorageState(displaySettingsState)

  const [videoCardSettings, setVideoCardSettings] = useStorageState(videoCardSettingsState)

  const [pricingConfig, setPricingConfig] = useStorageState(pricingConfigState)

  // 添加一个计算已启用选项数量的函数
  const getEnabledSettingsCount = () => {
    return Object.values(displaySettings).filter(Boolean).length;
  };

  // 修改 Toggle 组件，添加禁用状态和提示
  const Toggle = ({ checked, onChange, disabled, tooltip }: { 
    checked: boolean; 
    onChange: (checked: boolean) => void;
    disabled?: boolean;
    tooltip?: string;
  }) => {
    return (
      <div className="relative group">
        <button
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            checked ? "bg-blue-600" : "bg-gray-200"
          } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
          onClick={() => !disabled && onChange(!checked)}
          disabled={disabled}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              checked ? "translate-x-6" : "translate-x-1"
            }`}
          />
        </button>
        {disabled && tooltip && (
          <div className="absolute right-0 bottom-full mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            {tooltip}
          </div>
        )}
      </div>
    );
  };

  // 修改 handleDisplaySettingChange 函数
  const handleDisplaySettingChange = (key: string, value: boolean) => {
    const currentEnabledCount = getEnabledSettingsCount();
    const willEnable = value && !displaySettings[key as keyof typeof displaySettings];
    
    // 如果当前已经有4个选项启用，且试图启用新的选项，则阻止
    if (currentEnabledCount >= 4 && willEnable) {
      return;
    }
    
    const newSettings = { ...displaySettings, [key]: value };
    setDisplaySettings(newSettings);
    onSettingsChange?.({ displaySettings: newSettings, videoCardSettings, pricingConfig });
  };

  const handleVideoCardSettingChange = (key: string, value: boolean) => {
    const newSettings = { ...videoCardSettings, [key]: value }
    setVideoCardSettings(newSettings)
    onSettingsChange?.({ displaySettings, videoCardSettings: newSettings, pricingConfig })
  }

  const handlePricingConfigChange = (platform: Platform, tier: string, value: number) => {
    const newConfig = {
      ...pricingConfig,
      [platform]: {
        ...pricingConfig[platform as keyof typeof pricingConfig],
        [tier]: value,
      },
    }
    setPricingConfig(newConfig)
    onSettingsChange?.({ displaySettings, videoCardSettings, pricingConfig: newConfig })
  }

  return (
    <div className={clsx("w-full h-full bg-white rounded-lg shadow-lg p-4 z-10",{
      "min-w-[500px]":platform !== Platform.YOUTUBE,
      "min-w-[300px]":platform === Platform.YOUTUBE,
    })}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Display Settings</h3>
        <button
          onClick={onClose}
          className="p-1 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 transition-colors"
        >
          <span className="text-lg text-gray-600">✕</span>
        </button>
      </div>

      {/* Category Tabs */}
      <div className="flex mb-4 border-b border-gray-200">
        {["info card", "video card", "pricing config"].map((category) => (
          <button
            key={category}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              currentCategory === category
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setCurrentCategory(category)}
          >
            {category
              .split(" ")
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" ")}
          </button>
        ))}
      </div>

      {/* Content based on selected category */}
      <div className="max-h-80 overflow-y-auto">
        {currentCategory === "info card" && (
          <div className="space-y-4">
            <div className="space-y-3">
              {[
                { key: "estimatedPrice", label: "Estimated Price" },
                { key: "views", label: "Med Views" },
                { key: "likes", label: "Med Likes" },
                { key: "comments", label: "Med Comments" },
                { key: "er", label: "Med ER" },
              ].map(({ key, label }) => {
                const isDisabled = !displaySettings[key as keyof typeof displaySettings] && getEnabledSettingsCount() >= 4;
                return (
                  <div key={key} className="flex items-center justify-between py-2">
                    <span className="text-sm font-medium text-gray-700">{label}</span>
                    <Toggle
                      checked={displaySettings[key as keyof typeof displaySettings]}
                      onChange={(checked) => handleDisplaySettingChange(key, checked)}
                      disabled={isDisabled}
                      tooltip={isDisabled ? "最多只能选择4个选项" : undefined}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {currentCategory === "video card" && (
          <div className="space-y-4">
            <div className="space-y-3">
              {[
                { key: "plays", label: "Play", icon: Play },
                { key: "likes", label: "Likes", icon: Heart },
                { key: "comments", label: "Comments", icon: MessageCircle },
                { key: "er", label: "ER", icon: null },
                // { key: "favorites", label: "Favorites", icon: Bookmark },
                { key: "publishTime", label: "Publish Time", icon: null },
                { key: "viralMultiplier", label: "Viral Multiplier", icon: null },
              ].map(({ key, label, icon: Icon }) => (
                <div key={key} className="flex items-center justify-between py-2">
                  <div className="flex items-center gap-2">
                    {Icon && <Icon className="h-4 w-4 text-gray-600" />}
                    <span className="text-sm font-medium text-gray-700">{label}</span>
                  </div>
                  <Toggle
                    checked={videoCardSettings[key as keyof typeof videoCardSettings]}
                    onChange={(checked) => handleVideoCardSettingChange(key, checked)}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {currentCategory === "pricing config" && (
          <div className="space-y-6">
            <div>
              <p className="text-xs text-gray-600 mb-4">
                Set influencer marketing CPM prices based on country tiers and platforms
              </p>
            </div>

            {/* Platform Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Platform</label>
              <div className="relative">
                <select
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value as Platform)}
                  className="w-full p-3 text-sm border border-gray-300 rounded-md bg-white text-gray-700 appearance-none pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                >
                  {Object.keys(pricingConfig).map((platform) => (
                    <option key={platform} value={platform}>
                      {platform}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* Basic Tier Pricing */}
            <div>
              <h5 className="font-medium text-gray-700 mb-4">Basic Tier Pricing</h5>

              <div className="space-y-4">
                {Object.entries(pricingConfig[selectedPlatform as keyof typeof pricingConfig]).map(([tier, price]: [string, number]) => (
                  <div key={tier} className="flex items-center justify-between">
                    <div className="text-sm font-medium text-gray-700 min-w-[40px]">{tierToText(tier as "T1" | "T2" | "T3")}</div>
                    <div className="flex items-center gap-2">
                      <span className="text-gray-500 text-sm">$</span>
                      <input
                        type="text"
                        value={price.toString()}
                        onChange={(e) => {
                          const value = e.target.value
                          // Allow empty string or valid numbers
                          if (value === "" || /^\d+$/.test(value)) {
                            handlePricingConfigChange(
                              selectedPlatform,
                              tier,
                              value === "" ? 0 : Number.parseInt(value, 10),
                            )
                          }
                        }}
                        className="w-20 p-2 text-sm border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                        placeholder="0"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Save Button */}
            <div className="pt-4 border-t border-gray-200">
              <button
                onClick={() => {
                  // Could add save logic here
                  onClose()
                }}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
              >
                Save Settings
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}


function tierToText(tier: "T1" | "T2" | "T3") {
  // 这里将不同的 tier 映射为对应的英文描述
  switch (tier) {
    case "T1":
      return "Developed CPM";
    case "T2":
      return "Developing CPM";
    case "T3":
      return "Underdeveloped CPM";
    default:
      return tier;
  }


}