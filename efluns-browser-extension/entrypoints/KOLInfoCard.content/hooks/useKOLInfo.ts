import { EmailSource, KOL, SearchKOLParams } from '@/@types/kol'
import { Platform } from '@/@types/platform'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import { getCommonService } from '@/services/CommonService'
import { getKOLService } from '@/services/KOLService'
import { getPlatformService } from '@/services/PlatformService'
import { autoInfoCardState, kolCacheState } from '@/utils/storages'
import { convertSocialMediaToLinks } from '@/utils/tiktok/convertSocialMediaToLinks'
import { getYoutubeData } from '@/utils/youtube'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { getEmailFromDescription } from '../utils/getEmailFromDescription'
import { getLinksFromKolPage } from '../utils/getLinksFromKolPage'
import { renderAvatarCard } from '../utils/renderAvatarCard'

type ResolveFunction = (kolId: string) => void

const log = makeModuleLog('useKOLInfo')

export default function useKOLInfo() {
	const getSearchKOLParams = useCallback(async (url: string, youtuberId?: string | null) => {
		const platform = await getPlatformService().getPlatform(url)
		const params: SearchKOLParams = {
			platform
		}

		switch (platform) {
			case Platform.YOUTUBE:
				params.id = guard(youtuberId, 'YOUTUBE ID is null')
				break
			case Platform.TIKTOK:
				params.handler = guard(extractKOLHandler(url), 'TIKTOK Handler is null')
				break
			case Platform.INS:
				params.handler = guard(extractInsKOLHandler(url), 'INSTAGRAM Handler is null')
				break
		}

		return params
	}, [])
	// const { isInYoutubeKOLPage, isInTikTokKOLPage } = useSupportPageInfo()
	const currentPlatform = getPlatform(window.location.href)
	log('currentPlatform', currentPlatform)

	const [localKolInfo, setLocalKolInfo] = useState<KOL | null>(null)
	// const [autoInfoCard] = useStorageState(autoInfoCardState)
	const [_autoInfoCard, setAutoInfoCard] = useState<boolean | undefined>(undefined)
	const [manualTriggered, setManualTriggered] = useState(false)
	const [socialEmail, setSocialEmail] = useState<string | null>(null)

	const {data:isEnterpriseUser} = useQuery({
		queryKey:['IS_ENTERPRISE_USER'],
		queryFn:async ()=>{
			return getKOLService().getIsEnterpriseUser()
		}
	})

	const autoInfoCard = useMemo(() => {
		return _autoInfoCard && isEnterpriseUser
	}, [_autoInfoCard, isEnterpriseUser])

	useEffect(() => {
		autoInfoCardState.getValue().then((value) => {
			setAutoInfoCard(value)
		})
		renderAvatarCard()
		getCommonService()
			.pollNotifies()
			.catch((error) => {
				log('pollNotifies error (expected in content script):', error)
			})
	}, [])

	const { data: youtubeData, isLoading: isYoutubeDataLoading } = useQuery({
		queryKey: ['YOUTUBE_CHANNEL_DATA'],
		queryFn: async () => getYoutubeData(window.location.href),
		enabled: currentPlatform === Platform.YOUTUBE
	})

	const {
		data: kolData,
		isLoading: isKolLoading,
		error: kolError,
		refetch: refetchKolData
	} = useQuery({
		queryKey: ['FETCH_KOL_INFO', youtubeData?.id],
		// 确保 autoInfoCard 已经加载完成
		enabled:
			autoInfoCard !== undefined &&
			(autoInfoCard || manualTriggered) &&
			(currentPlatform !== Platform.YOUTUBE || !!youtubeData?.id),
		queryFn: async () => {
			const currentURL = decodeURIComponent(window.location.href)
			const email = await getEmailFromDescription()
			// const pageType = await getKOLService().getYtbPageType(location.href)
			log('FETCH_KOL_INFO', {
				currentURL,
				youtuberId: youtubeData?.id
			})

			//这是为了卡片的loading状态
			let resolveKolId: ResolveFunction | undefined

			const kolIdPromise = new Promise<string>((resolve) => {
				resolveKolId = resolve
			})

			const shouldGetLinks = currentPlatform === Platform.YOUTUBE ? !!youtubeData?.id : true
			autoInfoCard &&
				shouldGetLinks &&
				getLinksFromKolPage(async (links) => {
					const kolId = await kolIdPromise
					if (!kolId) {
						return []
					}
					if (youtubeData) {
						links = youtubeData.links.map((item) => item.url)
					}
					log('links', links)
					const socialLinks = await getKOLService().patchKolLinks(kolId, links)
					const email = socialLinks.find((item) => item.type === 'email')?.content
					if (email) {
						setSocialEmail(email)
					}
					return socialLinks
				})
			const kolService = getKOLService()

			const params = await getSearchKOLParams(currentURL, youtubeData?.id)
			const result = await kolService.searchKOL(params).catch((error) => {
				resolveKolId && resolveKolId('')
				throw error
			})

			if (resolveKolId) {
				resolveKolId(result?.kolInfo?.id)
			}
			log(result, 'cccc')

			if (result) {
				// 最多存储 100 条，如果超过 100 条，则删除最早的一条
				const cache = await kolCacheState.getValue()
				if (cache && Object.keys(cache).length >= 100) {
					const sortedKeys = Object.keys(cache).sort(
						(a, b) => new Date(cache[a].cachedAt).getTime() - new Date(cache[b].cachedAt).getTime()
					)
					delete cache[sortedKeys[0]]
				}
				await kolCacheState.setValue({
					...(cache || {}),
					[encodeURIComponent(currentURL)]: {
						kolData: result,
						url: currentURL,
						cachedAt: new Date().toISOString()
					}
				})
			}

			if (email !== result?.kolInfo?.email) {
				if (email) {
					result.kolInfo.email = email
					kolService.updateKOLInfo(result.kolInfo.id, {
						email,
						emailSource: 'front_bio_email'
					})
				}
			}
			return result
		}
	})

	const {data:tiktokRegion,isLoading:isTiktokRegionLoading} = useQuery({
		queryKey:['FETCH_KOL_REGION'],
		enabled:autoInfoCard !== undefined && !autoInfoCard && currentPlatform === Platform.TIKTOK,
		queryFn:async ()=>{
			return getKOLService().getKOLRegion({
				platform:Platform.TIKTOK,
				handler:extractKOLHandler(window.location.href)!
			})
		}
	})

	const { data: emailData, isLoading: isEmailLoading } = useQuery({
		queryKey: ['FETCH_KOL_EMAIL', kolData?.kolInfo?.id],
		enabled: !!kolData?.kolInfo && !kolData.kolInfo.email,
		queryFn: async () => {
			const currentURL = decodeURIComponent(window.location.href)
			const kolService = getKOLService()
			const params = await getSearchKOLParams(currentURL, youtubeData?.id)
			return kolService.searchKOLEmail(params)
		}
	})

	//外链爬取
	// const {data:linksContactData,isLoading:isLinksContactLoading} = useQuery({
	// 	queryKey:['FETCH_KOL_LINKS_CONTACT',kolData?.kolInfo?.id],
	// 	enabled:!!kolData?.kolInfo && autoInfoCard,
	// 	queryFn:async ()=>{
	// 		let socialLinks:ContactInfo[] = []
	// 		await getLinksFromKolPage(async (links)=>{
	// 			socialLinks = await getKOLService().patchKolLinks(kolData?.kolInfo?.id||'',links)
	// 			return socialLinks
	// 		})
	// 		return socialLinks
	// 	}
	// })

	// console.log("爬取到外链",linksContactData);

	// 合并 KOL 信息
	const mergedKolInfo = useMemo(() => {
		if (!kolData?.kolInfo) return null

		if (kolData.kolInfo.email) {
			return kolData.kolInfo
		}

		if (emailData?.kolInfo?.email) {
			return {
				...kolData.kolInfo,
				email: emailData.kolInfo.email
			}
		}
		// console.log("合并kolInfo的时候判断socialEmail",socialEmail);

		if (socialEmail) {
			return {
				...kolData.kolInfo,
				email: socialEmail
			}
		}
		return kolData.kolInfo
	}, [kolData?.kolInfo, emailData?.kolInfo, socialEmail])

	// 使用本地状态或合并后的远程数据
	const currentKolInfo = localKolInfo || mergedKolInfo

	const { mutateAsync: sendEmail, isPending: isSendEmailPreparing } = useMutation({
		mutationKey: ['SEND_EMAIL', currentKolInfo?.id],
		mutationFn: async () => {
			const kolId = guard(currentKolInfo?.id, 'KOL ID')
			// 如果本地有修改过的 email，使用本地的
			return getKOLService().sendEmail(kolId, localKolInfo?.email || undefined)
		}
	})

	return {
		platformData: {
			youtube: youtubeData
		},
		tiktokLinks: convertSocialMediaToLinks(kolData?.socialMediaIds[0]),
		region: kolData?.region || tiktokRegion?.region,
		hasEmail: kolData?.hasEmail,
		data: currentKolInfo,
		error: kolError,
		socialMediaIds: kolData?.socialMediaIds,
		kolLoading: isKolLoading,
		emailLoading: isEmailLoading,
		isLoading: isKolLoading || isEmailLoading || isYoutubeDataLoading || isTiktokRegionLoading,
		isEnterpriseUser,
		mutateData: useCallback(
			(updater: (nextKOLInfo: KOL) => KOL) => {
				const baseInfo = localKolInfo || currentKolInfo

				if (!baseInfo) {
					return
				}

				// 计算新数据
				const newKOLInfo = updater(baseInfo)

				// 更新本地状态
				setLocalKolInfo(newKOLInfo)
			},
			[localKolInfo, currentKolInfo]
		),
		sendEmail,
		isSendEmailPreparing,
		refetchKolData,
		onEmailUpdate: useCallback(
			async (email: string, emailSource: EmailSource) => {
				if (!currentKolInfo?.id) {
					return
				}
				const kolService = getKOLService()
				setLocalKolInfo({
					...currentKolInfo,
					email
				})
				await kolService.updateKOLInfo(currentKolInfo.id, {
					email,
					emailSource
				})
			},
			[currentKolInfo]
		),
		triggerManualSearch: useCallback(() => {
			setManualTriggered(true)
		}, []),
		isManualMode: autoInfoCard !== undefined ? !autoInfoCard && !manualTriggered : true
	}
}
