export default function useToast() {
	const [type, setType] = useState<'error' | 'success'>()
	const [content, setContent] = useState<string>()
	const [isShow, setIsShow] = useState<boolean>(false)
	const resetState = useCallback(() => {
		setType(undefined)
		setContent(undefined)
		setIsShow(false)
	}, [])
	return {
		isShow,
		type,
		content,
		toast: useCallback(
			(type: 'error' | 'success', content: string) => {
				setType(type)
				setContent(content)
				setIsShow(true)
				setTimeout(() => {
					resetState()
				}, 3000)
			},
			[resetState]
		)
	}
}
