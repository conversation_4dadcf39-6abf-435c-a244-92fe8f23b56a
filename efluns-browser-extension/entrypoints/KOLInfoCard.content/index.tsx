import AuthWrapper from '@/components/AuthWrapper'
import JotaiDevTools from '@/components/JotaiDevTools'
import { initDayJS } from '@/utils/day'
import getSupportPage, { SupportPage } from '@/utils/getSupportPage'
import type { EmotionCache } from '@emotion/cache'
import createCache from '@emotion/cache'
import { CacheProvider } from '@emotion/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Root, createRoot } from 'react-dom/client'
import App from './App'
import globalStyles from './global.css?inline'
import { injectWebSiteEntry } from './utils/injectWebSiteEntry'
import { renderAvatarCard } from './utils/renderAvatarCard'
import { injectWebSiteSearchEntry } from './utils/injectWebSiteSearchEntry'
import { easyMessageControl } from './utils/easyMessageControl'
import EasyDM from './components/EasyDM'
import { renderAppWithTailwind } from '../TiktokEarlyScript.content/utils/renderApp'
import { injectSingleTrackBtn } from './utils/injectSingleTrackBtn'
import { getTagsCount } from '@/utils/tiktok/getTags'
import { getCommonService } from '@/services/CommonService'
import { getPostHandler } from '@/utils/getPostHander'
import { setSimilarFromMode, startInjectSimilarFromCheckboxes } from '@/utils/youtube/similarFromVideos'
import "../../assets/content.css"
import { videoCardSettingControl } from '@/utils/videoCardSettingControl'

const YOUTUBE_CONTAINER_SELECTOR_V1 = '#channel-header-container'
const YOUTUBE_CONTAINER_SELECTOR_V2 = '.page-header-view-model-wiz__page-header-headline'
const TIKTOK_CONTAINER_SELECTOR = '#main-content-others_homepage > div'
const INS_CONTAINER_SELECTOR = '[role="main"]'

const ANCHOR_ID = 'efluns-info-anchor'
const APP_ROOT_ID = 'efluns-info-app-root'

const YOUTUBE_AVATAR_SELECTOR = 'yt-avatar-shape img'
const TIKTOK_AVATAR_SELECTOR = '[data-e2e="user-avatar"] img'
const INS_AVATAR_SELECTOR = 'header img'


async function getContainerSelectors() {
	const supportPage = await getSupportPage(window.location.href)

	switch (supportPage) {
		case SupportPage.YOUTUBE_KOL:
			return [YOUTUBE_CONTAINER_SELECTOR_V2, YOUTUBE_CONTAINER_SELECTOR_V1]
		case SupportPage.TIKTOK_KOL:
			if(!location?.pathname?.split('/')?.filter(Boolean)?.pop()?.startsWith("@")){
				throw new Error('Unsupported page')
			}
			return [TIKTOK_CONTAINER_SELECTOR]
		case SupportPage.INS_KOL:
			return [INS_CONTAINER_SELECTOR]
	}

	throw new Error('Unsupported page')
}

async function getAvatarUrl(){
	const supportPage = await getSupportPage(window.location.href)
	switch(supportPage){
		case SupportPage.YOUTUBE_KOL:
			return document.querySelector(YOUTUBE_AVATAR_SELECTOR)?.getAttribute('src')
		case SupportPage.TIKTOK_KOL:
			return document.querySelector(TIKTOK_AVATAR_SELECTOR)?.getAttribute('src')
		case SupportPage.INS_KOL: {
			const imgElement = document.querySelector(INS_AVATAR_SELECTOR) as HTMLImageElement
			if (!imgElement) return
			
			try {
				const canvas = document.createElement('canvas')
				canvas.width = imgElement.naturalWidth / 15
				canvas.height = imgElement.naturalHeight / 15
				const ctx = canvas.getContext('2d')
				ctx?.drawImage(imgElement, 0, 0, canvas.width, canvas.height)
				return canvas.toDataURL('image/jpeg', 0.3)
			} catch (error) {
				console.error('转换Instagram头像失败:', error)
				return null
			}
		}
	}
}

async function getAnchor(): Promise<HTMLElement> {
	const containerSelectors = await getContainerSelectors()
	const container = await Promise.any(
		containerSelectors.map((selector) =>
			waitFor(selector, {
				suppressLog: true
			})
		)
	)
	let anchor = Array.from(container.children).find((child) => child.id === ANCHOR_ID) as HTMLElement | undefined

	if (!anchor) {
		anchor = document.createElement('div')
		anchor.id = ANCHOR_ID
		container.appendChild(anchor)
	}

	return anchor
}

let styleCache: EmotionCache | null = null
function prepareAppEnv(anchor: HTMLElement): Promise<{
	appRoot: HTMLElement
	styleCache: EmotionCache
}> {
	return new Promise((resolve, reject) => {
		const createShadowDOM = () => {
			const appRoot = document.createElement('div')
			appRoot.id = APP_ROOT_ID

			/** Inject styles into shadow dom */
			const basicStyleElement = document.createElement('style')
			basicStyleElement.innerHTML = globalStyles

			const emotionStyleElement = document.createElement('style')
			styleCache = createCache({
				key: ANCHOR_ID,
				container: emotionStyleElement
			})
			styleCache.compat = true

			const html = document.createElement('html')
			const head = document.createElement('head')
			const body = document.createElement('body')


			// 获取当前页面的 font-size
			//TODO：只是一个尝试，因为不同的页面html的font-size不一样，所以需要根据页面来调整，font-size为10的时候展示最佳
			const currentFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)
			const targetFontSize = 10 // 目标 font-size

			// 计算缩放比例
			const scale = targetFontSize / currentFontSize

			// 应用缩放
			if (scale !== 1) {
				const scaleStyle = document.createElement('style')
				scaleStyle.textContent = `
					#${APP_ROOT_ID}>div{
						scale: ${scale};
						transform-origin: right top;
					}
				`
				head.appendChild(scaleStyle)
			}

			head.appendChild(basicStyleElement)
			head.appendChild(emotionStyleElement)
			body.appendChild(appRoot)
			html.appendChild(head)
			html.appendChild(body)
			return html
		}

		if (anchor.shadowRoot === null) {
			anchor.attachShadow({ mode: 'open' })
		}

		let html: HTMLHtmlElement | null = null
		if (anchor.shadowRoot?.childElementCount === 0) {
			html = createShadowDOM()
			anchor.shadowRoot?.appendChild(html)
		} else {
			html = anchor.shadowRoot?.querySelector('html') ?? null
		}

		const appRoot = (html?.querySelector(`#${APP_ROOT_ID}`) as HTMLElement | null) ?? null

		if (!appRoot) {
			reject(new Error('appRoot not found'))
			return
		}
		if (!styleCache) {
			reject(new Error('styleCache not found'))
			return
		}

		resolve({
			appRoot,
			styleCache
		})
	})
}

let root: Root | null = null
async function renderApp() {
	return getAnchor()
		.then((anchor) => prepareAppEnv(anchor))
		.then(({ appRoot, styleCache }) => {
			cardPositionState.getValue().then((position)=>{
				initDayJS()

				const queryClient = new QueryClient({
					defaultOptions: {
						queries: {
							refetchOnWindowFocus: false,
							retry: false
						},
						mutations: {
							retry: false
						}
					}
				})
				if(root){
					root.unmount()
				}
				root = createRoot(appRoot)
				root.render(
					<CacheProvider value={styleCache!}>
						<QueryClientProvider client={queryClient}>
							<AuthWrapper>
								<App position={position}/>
							</AuthWrapper>
							<JotaiDevTools />
						</QueryClientProvider>
					</CacheProvider>
				)
			})

		})
		.catch((err) => {
			// console.error('renderApp Failed', err)
		})
}

function extensionMessageHandler(){
	browser.runtime.onMessage.addListener((message:any,sender,sendResponse)=>{
		switch(message.type){
			case 'getAvatarUrl':
				getAvatarUrl().then(sendResponse)
				return true
			case 'GET_TAGS':
				sendResponse(getTagsCount()?.length)
				return true
			case 'getPostHandler':
				sendResponse(getPostHandler())
				break
			case 'setSimilarFromMode':
				setSimilarFromMode()
				break
		}
	})
}



export default defineContentScript({
	matches: ['https://www.youtube.com/*', 'https://www.tiktok.com/*', 'https://www.instagram.com/*'],
	runAt: 'document_end',
	main() {
		renderApp()
		window.navigation?.addEventListener('navigate', () => {
			root?.unmount()
		})
		window.navigation?.addEventListener('navigatesuccess', () => {
			sleep(1000).then(() => {
				renderApp()
			})
		})

		// injectWebSiteEntry()

		injectWebSiteSearchEntry()

		injectSingleTrackBtn()

		extensionMessageHandler()

		easyMessageControl()//tt的message控制

        // 使用renderAppWithTailwind渲染EasyDM组件
		waitForElementToRunCallback({
			selector:"a[href*='messages?']",
			callback:(element)=>{
				if(element.querySelector("#efluns-message-anchor")){
					return
				}
				renderAppWithTailwind(EasyDM,{
					container:element,
					anchorId:"efluns-message-anchor",
					appRootId:"efluns-message-app-root"
				})
			}
		})

		// 注入similarFromCheckboxes
		startInjectSimilarFromCheckboxes()

		// renderAvatarCard()
		videoCardSettingControl()//控制视频卡片展示的设置
	}
})
