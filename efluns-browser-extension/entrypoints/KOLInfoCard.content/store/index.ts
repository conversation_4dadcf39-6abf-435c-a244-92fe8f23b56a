import { Message } from '@/@types/easyDm'
import { videoLastDateList } from '@/constants/infocard'
import { atom } from 'jotai'
import { atomWithProxy } from 'jotai-valtio'
import { proxy } from 'valtio/vanilla'


const log = makeModuleLog('KOLInfoCardStore')

export const latestVideoAmountState = atom<number>(8)


export const lastVideoDateState = atom<typeof videoLastDateList[number]['id']>(videoLastDateList[1].id)

export const messagesProxyState = proxy<Message[]>([]);

export const messagesState = atomWithProxy(messagesProxyState);