import { waitFunc } from "@/utils/waitFunc";

export function easyMessageControl(){
    if(window.location.href.startsWith("https://www.tiktok.com/messages")){
        browser.runtime.onMessage.addListener(addMessageListener as any)
    }
}

function addMessageListener(message:any,sender:any,sendResponse:any){
    if(message.type === "readyToMessage"){
        console.log("检查是否准备就绪");
        sendResponse(!!getNickNameTab(message.nickname))
        return
    }else if(message.type === "sendMessage"){
        console.log("发送消息");
        sendMessage(message.nickname,message.text).then(res=>{
            console.log("发送消息成功");
            sendResponse(res)
        })
        return true
    }
}

function getNickNameTab(nickname:string) {
    return Array.from(document.querySelectorAll('[data-e2e="chat-list-item"] p[class*=InfoNickname]'))?.find(e=>{
        return e.textContent === nickname
    }) as HTMLElement | undefined
}

async function sendMessage(nickname:string,text:string){
    const tab = guard(await waitFunc(()=>getNickNameTab(nickname),{
        timeout:60000,
        interval:1000
    }),"获取nickname tab失败")
    tab.click()
    await new Promise(resolve=>{
        setTimeout(()=>{
            resolve(true)
        },500)
    })
    await simulateDraftInputViaPaste(text)
}

async function simulateDraftInputViaPaste(text: string) {
    console.log("开始等待输入框就绪");
    
    const editorContent = await waitFor('.public-DraftEditor-content[contenteditable="true"]',{
        timeout:60000
    });
    if (!editorContent) {
        console.error('找不到 Draft.js 编辑器元素');
        return;
    }

    // 聚焦编辑器
    editorContent.focus();

    // 创建剪贴板数据
    const clipboardData = new DataTransfer();
    clipboardData.setData('text/plain', text);

    // 模拟粘贴事件
    const pasteEvent = new ClipboardEvent('paste', {
        bubbles: true,
        cancelable: true,
        clipboardData: clipboardData
    });

    editorContent.dispatchEvent(pasteEvent);

    // 等待粘贴完成后模拟回车事件
    setTimeout(() => {
        // 模拟回车键按下
        const enterKeyDown = new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            bubbles: true,
            cancelable: true
        });
        editorContent.dispatchEvent(enterKeyDown);

        // 模拟回车键抬起
        const enterKeyUp = new KeyboardEvent('keyup', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            bubbles: true,
            cancelable: true
        });
        editorContent.dispatchEvent(enterKeyUp);
    }, 100); // 给一个小延时确保粘贴事件已经处理完
}