import { Link } from "@/@types"
import { Platform } from "@/@types/platform"
import { getPlatform } from "@/entrypoints/Entrypoint.content/utils"

const INS_DESCRIPTION_ELEMENT = "header section:nth-child(4)"
const YOUTUBE_DESCRIPTION_ELEMENT = "yt-description-preview-view-model"
const TIKTOK_DESCRIPTION_ELEMENT = '#main-content-others_homepage [data-e2e="user-bio"]'

const log = makeModuleLog("getEmailFromDescription")
const extractEmailFromText = (text: string): string | null => {
  if (!text) return null
  
  
  // 更新邮箱匹配的正则表达式，使用更严格的规则
  const emailRegex = /[a-zA-Z0-9][a-zA-Z0-9._%+-]*@([a-zA-Z0-9][a-zA-Z0-9-]*\.){1,}[a-zA-Z]+/
  
  const matches = text.match(emailRegex)
  
  // log("当前用户描述", text,"提取邮箱", matches?.[0] || null);

  if (!matches || matches.length === 0) {
    return null
  }

  // 检查邮箱后面是否跟着省略号
  const email = matches[0]
  const emailIndex = text.indexOf(email)
  const nextChar = text[emailIndex + email.length]
  

  if (nextChar === '.') {
    return null
  }

  return email
}

export const extractInsAccountFromDescription = (description:string = getDescriptionText() || ''):Link | null =>{
  const username = extractInstagramHandles(description)
  if(!username) return null
  return {
    title:username,
    url:`https://www.instagram.com/${username}/`,
    icon:"https://encrypted-tbn0.gstatic.com/favicon-tbn?q=tbn:ANd9GcSMItu-kpj9mUqJlYFLBMsNp279lOFn_WdIAoqsMZYtJYhjg8xQh9qCgB3BvVFbtYpLemKWr-ZbwnTCA2jDa_i60wjmuwUIX-vxhbaBmAQONd2D"
  }
}

function extractInstagramHandles(text: string): string | null {
  const handles = new Set()
  let processedText = text;
  
  // 1. 首先处理特殊的 "insta is username" 模式
  const instaIsRegex = /\binsta\s+is\s+([a-zA-Z0-9._]{1,30})\b/gi
  let match
  
  while ((match = instaIsRegex.exec(text)) !== null) {
  if (match[1] && !match[1].includes('+')) {
  handles.add(match[1])
  }
  }
  
  // 替换掉文本中已匹配的部分，防止重复匹配
  processedText = processedText.replace(/\binsta\s+is\s+([a-zA-Z0-9._]{1,30})\b/gi, '');
  
  // 2. 处理其他 Instagram 用户名模式
  const igRegex = /\b(?:ig|insta(?:gram)?)\s*(?:[.:：]|@)?\s*@?([a-zA-Z0-9._]{1,30})\b/gi
  
  while ((match = igRegex.exec(processedText)) !== null) {
  // 排除没有真正提供用户名的情况
  if (match[1] && !match[1].includes('+')) {
  handles.add(match[1])
  }
  }
  if(handles.size > 0){
    return Array.from(handles)[0] as string
  }
  return null
  }


function getDescriptionText(){
  const platform = getPlatform(location.href)
  if(!platform) return null
    switch(platform){
        case Platform.YOUTUBE:
            return (document.querySelector(YOUTUBE_DESCRIPTION_ELEMENT) as HTMLElement)?.innerText||''
        case Platform.TIKTOK:
            return (document.querySelector(TIKTOK_DESCRIPTION_ELEMENT) as HTMLElement)?.innerText||''
        case Platform.INS:
            return (document.querySelector(INS_DESCRIPTION_ELEMENT) as HTMLElement)?.innerText||''
    }
}

export const getEmailFromDescription = ()=>{
  return extractEmailFromText(getDescriptionText()||'')
}