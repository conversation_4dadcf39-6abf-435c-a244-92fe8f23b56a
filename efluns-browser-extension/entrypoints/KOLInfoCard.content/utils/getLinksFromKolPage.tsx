import { ContactInfo } from "@/@types/kol"
import { createRoot } from 'react-dom/client'
import SocialLinksCard from "../components/SocialLinksCard"
import cssStyle from '../global.css?inline'
import { getPlatform } from "@/entrypoints/Entrypoint.content/utils"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { Platform } from "@/@types/platform"

const TIKTOK_LINK_SELECTOR = '#main-content-others_homepage [class*="DivShareLinks"] a'
const INSTAGRAM_LINK_SELECTOR = 'header section [bioLinks]'
const YOUTUBE_LINK_SELECTOR = 'yt-attribution-view-model'

function extractDomain(url:string){
    return url.split('+')[0]?.trim();
}

export const getLinksFromKolPage = async (callback: (links: string[]) => Promise<ContactInfo[]>): Promise<void> => {
    const platform = getPlatform(location.href)
    
    const selector = platform === Platform.TIKTOK?TIKTOK_LINK_SELECTOR:platform === Platform.INS?INSTAGRAM_LINK_SELECTOR:YOUTUBE_LINK_SELECTOR
    const linkDom = await waitFor(selector).catch(()=>null) 
    
    if(!linkDom || (linkDom.style.display === 'none' && platform !== Platform.YOUTUBE)) return 

    if(platform === Platform.INS && !(linkDom instanceof HTMLButtonElement) && !linkDom.querySelector("a[rel='me nofollow noopener noreferrer']")) return

    if(platform === Platform.YOUTUBE && document.querySelector("#easy-kol-collector-social-links-card")) document.querySelector("#easy-kol-collector-social-links-card")?.remove()

    const linkInfo = platform === Platform.TIKTOK? {
        userLink:[linkDom.innerText || ''],
        linkContent:linkDom.innerText || ''
    }: platform === Platform.INS ? {
        userLink:JSON.parse(linkDom.getAttribute('bioLinks') || '[]'),
        linkContent:extractDomain(linkDom.innerText) || ''
    }:{
        userLink:[""],
        linkContent:linkDom.querySelector("a.yt-core-attributed-string__link")?.textContent || ''
    }

    const queryClient = new QueryClient({
        defaultOptions: {
            queries: {
                refetchOnWindowFocus: false,
                retry: false
            },
            mutations: {
                retry: false
            }
        }
    })
    
    // 创建容器元素并附加 Shadow DOM
    const container = document.createElement('div')
    container.id = 'easy-kol-collector-social-links-card'
    const shadowRoot = container.attachShadow({ mode: 'open' })
    const shadowContainer = document.createElement('div')
    const styleElement = document.createElement('style')
    styleElement.textContent = cssStyle
    shadowRoot.appendChild(styleElement)
    shadowRoot.appendChild(shadowContainer)
    shadowContainer.style.height = '28px'
    linkDom.parentNode?.insertBefore(container, linkDom)
    linkDom.style.display = 'none'
    linkDom.removeAttribute('bioLinks')

    // 创建 React root 并渲染组件
    const root = createRoot(shadowContainer)

    const linkHandleClick = ()=>{
        platform === Platform.YOUTUBE 
        ? [...linkDom.querySelectorAll('a')].at(-1)?.click()
        : platform === Platform.INS && linkDom.querySelector('a')
        ? linkDom.querySelector('a')?.click()
        : linkDom.click();
    }

    root.render(
        <QueryClientProvider client={queryClient}>
            <AuthWrapper>
                <SocialLinksCard linkInfo={linkInfo} patchKolLinks={callback} linkClick={linkHandleClick} />
            </AuthWrapper>
        </QueryClientProvider>
    )
    
    // const kolLinks = await callback([linkDom.innerText || ''])
    // console.log("获取到的结果",kolLinks);
    
    // return kolLinks
}
