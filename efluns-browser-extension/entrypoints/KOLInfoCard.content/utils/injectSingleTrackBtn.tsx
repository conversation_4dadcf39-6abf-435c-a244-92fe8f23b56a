import { createRoot } from "react-dom/client"
import { SingleTrackBtn } from "../components/SingleTrackBtn"
import { getPlatform } from "@/entrypoints/Entrypoint.content/utils"
import { Platform } from "@/@types/platform"
import { renderAppWithTailwind } from "@/entrypoints/TiktokEarlyScript.content/utils/renderApp"
import SingleAudienceCard from "../components/SingleAudienceCard"

const TIKTOK_POST_SELECTOR = "[class*='DivBrowserModeContainer']"
const TIKTOK_POST_SELECTOR_2 = "[class*='DivDescriptionContentWrapper-StyledDetailContentWrapper'] [class*='DivInfoContainer']"
const YOUTUBE_VIDEO_MENU_SELECTOR = "ytd-menu-renderer.ytd-watch-metadata #flexible-item-buttons"

export function injectSingleTrackBtn() {
    // waitForElementToRunCallback({selector:INS_POST_SELECTOR,callback:(postElement)=>{
    //     const dom = document.createElement('div')
    //     postElement.parentNode?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode?.appendChild(dom)
    //     dom.style.position = 'absolute'
    //     dom.style.bottom = '40px'
    //     dom.style.left = '25px'
    //     dom.style.zIndex = '1000'
    //     const root = createRoot(dom)
    //     root.render(<SingleTrackBtn />)
    // }})



        const platform = getPlatform(location.href)
        if(platform === Platform.INS){
            const trackBtn = document.createElement("div")
            document.body.appendChild(trackBtn)
            trackBtn.style.position = 'fixed'
            trackBtn.style.top = '65px'
            trackBtn.style.left = '20px'
            trackBtn.style.zIndex = '1000'
            trackBtn.style.display = 'none'

            // track按钮
            const trackBtnRoot = document.createElement("div")
            trackBtn.appendChild(trackBtnRoot)
            // audience按钮
            const audienceBtn = document.createElement("div")
            trackBtn.appendChild(audienceBtn)
            audienceBtn.style.marginTop = "5px"

            const root = createRoot(trackBtnRoot)
            root.render(<SingleTrackBtn />)
            renderAppWithTailwind(SingleAudienceCard,{
                container:audienceBtn,
                anchorId:"single-audience-btn",
                appRootId:"single-audience-btn-root",
            })



            window.navigation?.addEventListener('navigatesuccess', () => {
                if(location.href.includes('/p/') || location.href.includes('/reel/')){
                    trackBtn.style.display = 'block'
                }else{
                    trackBtn.style.display = 'none'
                }

            })
        }else if(platform === Platform.TIKTOK){
            waitForElementToRunCallback({selector:TIKTOK_POST_SELECTOR,callback:(postElement)=>{
                const dom = document.createElement('div')
                postElement.appendChild(dom)
                dom.style.position = 'absolute'
                dom.style.top = '65px'
                dom.style.left = '10px'
                dom.style.zIndex = '1000'

                const trackBtn = document.createElement("div")
                const audienceBtn = document.createElement("div")
                dom.appendChild(trackBtn)
                dom.appendChild(audienceBtn)

                const root = createRoot(trackBtn)
                root.render(<SingleTrackBtn />)
                renderAppWithTailwind(SingleAudienceCard,{
                    container:audienceBtn,
                    anchorId:"single-audience-btn",
                    appRootId:"single-audience-btn-root",
                })
            }})
        
            waitForElementToRunCallback({selector:TIKTOK_POST_SELECTOR_2,callback:(postElement)=>{
                const dom = document.createElement('div')
                dom.style.marginLeft = '20px'
                dom.style.marginRight = '20px'
                postElement.appendChild(dom)
                const root = createRoot(dom)
                root.render(<SingleTrackBtn />)
                renderAppWithTailwind(SingleAudienceCard,{
                    container:postElement,
                    anchorId:"single-audience-btn",
                    appRootId:"single-audience-btn-root",
                })
            }})
        }else if(platform === Platform.YOUTUBE){
            waitForElementToRunCallback({selector:YOUTUBE_VIDEO_MENU_SELECTOR,callback:(postElement)=>{
                const dom = document.createElement('div')
                dom.style.marginLeft = "10px"
                dom.style.marginTop = "5px"
                postElement.parentElement?.insertBefore(dom, postElement.nextSibling)
                const root = createRoot(dom)
                root.render(<SingleTrackBtn />)
                const audienceBtn = document.createElement("div")
                audienceBtn.style.marginLeft = "10px"
                audienceBtn.style.marginTop = "5px"
                postElement.parentElement?.insertBefore(audienceBtn, dom.nextSibling)

                renderAppWithTailwind(SingleAudienceCard,{
                    container:audienceBtn,
                    anchorId:"single-audience-btn",
                    appRootId:"single-audience-btn-root",
                })
            }})
        }
}