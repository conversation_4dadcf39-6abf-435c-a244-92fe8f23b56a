import { waitForElementToRunCallback } from "@/utils/DOM"
import { WebSiteTooltipEntry } from "../components/WebSiteTooltipEntry"
import { createRoot } from "react-dom/client"

const log = makeModuleLog('KOLInfoCard.content/utils/injectWebSiteEntry')
const followingSelector = '[data-e2e="follow-info-popup"]>div:nth-child(2)>div:nth-child(1)'
const followersSelector = '[data-e2e="follow-info-popup"]>div:nth-child(2)>div:nth-child(2)'

export function injectWebSiteEntry(){
	waitForElementToRunCallback({selector:followingSelector,callback:(followingElement)=>{
		log("找到关注列表")
        log(followingElement)
        // renderApp(()=>
        //     <WebSiteTooltipEntry type="following" />
        // ,{
        //     anchorId:"efluns-following-entry",
        //     appRootId:"efluns-following-app-root",
        //     container:followingElement
        // })
        const dom = document.createElement('div')
        followingElement.appendChild(dom)
        const root = createRoot(dom)
        root.render(<WebSiteTooltipEntry type="following" />)
	}})

    waitForElementToRunCallback({selector:followersSelector,callback:(followersElement)=>{
        log("找到粉丝列表")
        log(followersElement)
        const dom = document.createElement('div')
        followersElement.appendChild(dom)
        const root = createRoot(dom)
        root.render(<WebSiteTooltipEntry type="followers" />)
    }})
}