import { createRoot } from "react-dom/client"
import { WebSiteSearchEntry } from "../components/WebSiteSearchEntry"
import { isInsKOLPage } from "@/utils/ins"

const TIKTOK_TAG_ENTRY = '#main-content-challenge [class*=DivShareTitleContainer]'//tt的tag入口
const TIKTOK_SEARCH_ENTRY = "#search-tabs [class*=DivTabsNavList]" //tt的搜索入口
const TIKTOK_FOLLOWING_ENTRY = '[data-e2e="follow-info-popup"]>div:nth-child(2)>div:nth-child(1)'

const YOUTUBE_TAG_ENTRY = "[page-subtype='hashtag-landing-page'] #page-header-container yt-dynamic-text-view-model"//youtube的tag入口
const YOUTUBE_SEARCH_ENTRY = "yt-searchbox .ytSearchboxComponentInputBox" //youtube的搜索入口

const INSTAGRAM_TAGGED = "main [role='tablist'] a:last-child" //ins的tagged
const INSTAGRAM_FOLLOWING = '[role="dialog"]:has(input[autocapitalize="none"]) [role="heading"]' //ins的following
const INSTAGRAM_HASHTAG = "main>div>div:first-child:has(svg[aria-label] circle)>div>div:first-child:not(:has([data-pagelet='story_tray']))" //ins的hashtag



export async function injectWebSiteSearchEntry(){

    async function run(){
        console.log(111);
        
        document.querySelector('#efluns-website-search-entry')?.remove()
        const div = document.createElement('div')
        div.id = "efluns-website-search-entry"
        const author = location.href.includes("tagged") ?  await waitFor('[role="main"]') : document.body
        author.appendChild(div)
        const root = createRoot(div)
        root.render(<WebSiteSearchEntry />)
    }
    run()
    window.navigation?.addEventListener('navigatesuccess', () => {
        run()
    })
    // waitForElementToRunCallback({
    //     selector:TIKTOK_TAG_ENTRY,
    //     callback:(element)=>{
    //         const div = document.createElement('div')
    //         // div.style.marginLeft = "-5px"
    //         // div.style.marginTop = "10px"
    //         element.appendChild(div)
    //         const root = createRoot(div)
    //         root.render(<WebSiteSearchEntry type="tt_tag" />)
            
    //     }
    // })

    // waitForElementToRunCallback({
    //     selector:TIKTOK_SEARCH_ENTRY,
    //     callback:(element)=>{
    //         const div = document.createElement('div')
    //         element.appendChild(div)
    //         const root = createRoot(div)
    //         root.render(<WebSiteSearchEntry type="tt_search" />)
    //     }
    // })

    // waitForElementToRunCallback({selector:TIKTOK_FOLLOWING_ENTRY,callback:(followingElement)=>{
    //     // renderApp(()=>
    //     //     <WebSiteTooltipEntry type="following" />
    //     // ,{
    //     //     anchorId:"efluns-following-entry",
    //     //     appRootId:"efluns-following-app-root",
    //     //     container:followingElement
    //     // })
    //     const dom = document.createElement('div')
    //     followingElement.appendChild(dom)
    //     const root = createRoot(dom)
    //     root.render(<WebSiteSearchEntry type="tt_following" />)
	// }})

    // waitForElementToRunCallback({
    //     selector:YOUTUBE_TAG_ENTRY,
    //     callback:(element)=>{
    //         const div = document.createElement('div')
    //         div.id = "efluns-youtube-tag-entry"
    //         element.appendChild(div)
    //         const root = createRoot(div)
    //         root.render(<WebSiteSearchEntry type="youtube_tag" />)
            
    //     }
    // })

    // waitForElementToRunCallback({
    //     selector:YOUTUBE_SEARCH_ENTRY,
    //     callback:(element)=>{
    //         const div = document.createElement('div')
    //         element.appendChild(div)
    //         const root = createRoot(div)
    //         root.render(<WebSiteSearchEntry type="youtube_search" />)
            
    //     }
    // })

    // waitForElementToRunCallback({
    //     selector:INSTAGRAM_TAGGED,
    //     callback:(element)=>{
    //         const div = document.createElement('div')
    //         // div.style.position = "absolute"
    //         // div.style.right = "-192px"
    //         // div.style.zIndex = "999"
    //         element.appendChild(div)
    //         const root = createRoot(div)
    //         root.render(<WebSiteSearchEntry type="ins_tagged" />)
    //     }
    // })

    // waitForElementToRunCallback({
    //     selector:INSTAGRAM_FOLLOWING,
    //     callback:(element)=>{
    //         const div = document.createElement('div')
    //         // div.style.position = "absolute"
    //         // div.style.zIndex = "9"
    //         // div.style.bottom = "-41px"
    //         // div.style.left = "50%"
    //         // div.style.transform = "translateX(-50%)"
    //         element.appendChild(div)
    //         const root = createRoot(div)
    //         root.render(<WebSiteSearchEntry type="ins_following" />)
    //     }
    // })

    // waitForElementToRunCallback({
    //     selector:INSTAGRAM_HASHTAG,
    //     callback:(element)=>{
    //         if(!location.href.includes("explore/search/keyword")) return
    //         // element.style.alignItems = "center"
    //         // element.style.flexDirection = "row"
    //         // element.style.gap = "20px"
    //         const div = document.createElement('div')
    //         element.appendChild(div)
    //         const root = createRoot(div)
    //         root.render(<WebSiteSearchEntry type="ins_tag" />)
    //     }
    // })
}