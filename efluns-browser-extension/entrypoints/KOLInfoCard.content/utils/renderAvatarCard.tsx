import { INS_AVATAR_SELECTOR, TIKTOK_AVATAR_SELECTOR, YOUTUBE_AVATAR_SELECTOR } from "@/constants/selector"
import { renderAppWithTailwind } from "@/entrypoints/TiktokEarlyScript.content/utils/renderApp"
import { AvatarCard } from "../components/AvatarCard"

export async function renderAvatarCard(){
	const supportPage = await getSupportPage(window.location.href)
	let imgElement:HTMLImageElement | null = null
	let container:HTMLElement | null = null
	switch (supportPage) {
		case SupportPage.YOUTUBE_KOL:
			imgElement = await waitFor(YOUTUBE_AVATAR_SELECTOR) as HTMLImageElement
			container = imgElement.parentElement as HTMLElement
			break;
		case SupportPage.TIKTOK_KOL:
			imgElement = await waitFor(TIKTOK_AVATAR_SELECTOR) as HTMLImageElement
			container = imgElement.parentElement?.parentElement as HTMLElement
			break;
		case SupportPage.INS_KOL:
			imgElement = await waitFor(INS_AVATAR_SELECTOR) as HTMLImageElement
			container = imgElement.parentElement?.parentElement as HTMLElement
			break;
		default:
			break;
	}
	// console.log("imgElement",imgElement)
	if(!imgElement || !container) return
	renderAppWithTailwind(()=>{
		return <AvatarCard />
	},{
		anchorId:"efluns-avatar-card-anchor",
		appRootId:"efluns-avatar-card-app-root",
		container:container!
	})
}