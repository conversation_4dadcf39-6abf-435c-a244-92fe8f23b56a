import * as Sentry from "@sentry/browser";

export function initSentry(){
  Sentry.init({
    dsn: 'https://<EMAIL>/701',
    integrations: [
      Sentry.browserTracingIntegration(),
      // 可以添加其他集成
      Sentry.replayIntegration({
        maskAllText: true,
        blockAllMedia: true,
      }),
    ],
    
    // 设置采样率 - 生产环境可以调低
    tracesSampleRate: 1.0,
    
    // 设置环境
    environment: process.env.NODE_ENV || "production",
    
    // 浏览器插件特有的设置
    release:browser.runtime.getManifest().version,
  })
  
}
