import YOUTUBE_ICON from  "../../../assets/img/socialCard/link_youtube.png"
import INSTAGRAM_ICON from  "../../../assets/img/socialCard/link_ins.png"
import TIKTOK_ICON from  "../../../assets/img/socialCard/link_tiktok.png"
import SNAPCHAT_ICON from  "../../../assets/img/socialCard/link_snapchat.png"
import { LinkType } from "@/@types/kol"

const iconMap:Partial<{[key in LinkType]:string}> = {
    youtube:YOUTUBE_ICON,
    instagram:INSTAGRAM_ICON,
    tiktok:TIKTOK_ICON,
    snapchat:SNAPCHAT_ICON,
}

export function getSocialIcon(linkType:LinkType){
    return iconMap[linkType]
}