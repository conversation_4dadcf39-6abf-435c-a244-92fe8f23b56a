import AuthCard from '@/components/AuthCard'
import { getCommonService } from '@/services/CommonService'
import { isNil } from 'lodash-es'
import { HTMLAttributes } from 'react'

const log = makeModuleLog('LoginApp')
export interface AppProps extends HTMLAttributes<HTMLDivElement> {}

export default function App({ ...props }: AppProps) {
	const [session] = useStorageState(authSessionState)
	const isLogin = !isNil(session)

	log({ session })
	return (
		<div className="h-screen w-screen flex items-center justify-center">
			<div className="w-[450px] h-fit">
				{isLogin ? (
					<div className="flex flex-col items-center justify-center gap-8">
						<h1 className="text-5xl font-bold text-nowrap text-center relative">
							Welcome to{' '}
							<span className="bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-violet-500">
								EasyKOL
							</span>
						</h1>
						<button
							className="btn btn-primary rounded-full btn-lg w-[200px]"
							onClick={async () => {
								const commonService = getCommonService()
								sendMessage('openSidePanel', void 0)
								await commonService.openTab({
									url: 'https://airy-update.notion.site/EasyKOL-15ae8694012c806a83fbe5853a64fa3d'
								})
							}}
						>
							Continue
						</button>
					</div>
				) : (
					<AuthCard className="" />
				)}
			</div>
		</div>
	)
}
