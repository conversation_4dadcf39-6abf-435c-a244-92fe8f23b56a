<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Login to EasyKOL</title>
		<meta name="manifest.default_icon" content="{ '48': '/icon/48.png', '128': '/icon/128.png' }" />
		<meta name="manifest.open_at_install" content="true" />
		<meta name="manifest.browser_style" content="true" />
		<meta name="manifest.type" content="browser_action" />
		<!-- Set include/exclude if the page should be removed from some builds -->
		<meta name="manifest.include" content="['chrome']" />
	</head>
	<body>
		<!-- ... -->
		<div id="root"></div>
		<script type="module" src="./main.tsx"></script>
	</body>
</html>
