import JotaiDevTools from '@/components/JotaiDevTools'
import { QueryClientProvider } from '@tanstack/react-query'
import { createRoot } from 'react-dom/client'
import '~/assets/index.css'
import App from './App'

const container = document.getElementById('root')
const root = createRoot(container!)

generateFingerprint().then((browserId)=>{
	reportUserId()//上报浏览器指纹
})

root.render(
	<QueryClientProvider client={queryClient}>
		<App />
		<JotaiDevTools />
	</QueryClientProvider>
)
