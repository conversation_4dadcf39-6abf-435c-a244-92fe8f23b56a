import AuthGatekeeper from './components/AuthGatekeeper'
import { AliveScope, KeepAlive } from 'react-activation'
import FindSimilarsPage from './pages/KOLPage'
import EstablishContactPage from './pages/EstablishContactPage'
import NavigateBridge from './components/NavigateBridge'
import Modal from '@/components/Modal'
import { NarrowSidebar } from './components/NarrowSidebar'
import { routerState } from './store'
import { useAtomValue } from 'jotai'
import { useEffect } from 'react'
import { css } from '@emotion/react'

function Root() {
  const router = useAtomValue(routerState)

  // 通知所有标签页侧边栏已打开
  useEffect(() => {
    const notifyTabs = async () => {
      try {
        const tabs = await browser.tabs.query({})
        for (const tab of tabs) {
          if (tab.id) {
            browser.tabs.sendMessage(tab.id, { type: 'sidePanelOpened' }).catch(() => {
              // 忽略错误，某些标签页可能没有内容脚本
            })
          }
        }
      } catch (error) {
        console.error('Failed to notify tabs about sidebar opening:', error)
      }
    }
    
    notifyTabs()
  }, [])

  return (
    <AuthGatekeeper>
      <div className="bg-base-200 h-screen overflow-hidden relative" css={css`
        .ka-wrapper {
          height: 100%;
          overflow:auto;
        }
      `}>
        <div className='flex'>
          <div className='flex-1'>
            <AliveScope>
              {router.router === "find-similars" ? (
                <KeepAlive id='find-similars'>
                  <FindSimilarsPage />
                </KeepAlive>
              ) : (
                <KeepAlive id='establish-contact'>
                  <EstablishContactPage />
                </KeepAlive>
              )}
            </AliveScope>
          </div>
          <div className='w-[45px]'>
            <NarrowSidebar />
          </div>
        </div>
      </div>
      <NavigateBridge />
      <Modal />
    </AuthGatekeeper>
  )
}

export default Root 