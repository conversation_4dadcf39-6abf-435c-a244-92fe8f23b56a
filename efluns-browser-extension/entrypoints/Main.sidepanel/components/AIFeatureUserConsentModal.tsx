import { forwardRef, HTMLAttributes } from 'react'

export interface AIFeatureUserConsentModalProps extends HTMLAttributes<HTMLDialogElement> {}

export default forwardRef<HTMLDialogElement, AIFeatureUserConsentModalProps>(function AIFeatureUserConsentModal(
	{ onSubmit, ...props }: AIFeatureUserConsentModalProps,
	ref
) {
	return (
		<dialog ref={ref} id="user-consent-modal" className="modal" {...props}>
			<div className="modal-box">
				<h3 className="font-bold text-lg text-center mb-4">User Privacy Policy</h3>
				<div className="space-y-2">
					<p className="">To use our AI-powered recommendation feature:</p>
					<ul
						css={css`
							list-style: initial;
							padding-left: 15px;
						`}
					>
						<li>We analyze public channel info and content</li>
						<li>No personal data is shared with AI tools</li>
						<li>This helps us recommend similar channels</li>
					</ul>
					<p>
						For more details, see our full{' '}
						<a
							href="https://post.jellow.club/efluns-privacy-policy/"
							rel="noreferrer noopener noreferrer"
							target="_blank"
						>
							Privacy Policy
						</a>
						.
					</p>
				</div>
				<div className="modal-action">
					<form method="dialog" className="flex flex-wrap justify-center gap-2">
						{/* if there is a button in form, it will close the modal */}
						<button className="btn">No, Don&apos;t Use This Feature</button>
						<button
							className="btn btn-primary"
							onClick={async () => {
								await isUserConsentedState.setValue(true)
								onSubmit?.()
							}}
						>
							I Understand and Agree
						</button>
					</form>
				</div>
			</div>
		</dialog>
	)
})
