import insLogo from '@/assets/img/ins.png'
import tiktokLogo from '@/assets/img/tiktok.png'
import youtubeLogo from '@/assets/img/youtube.png'
import { useState, useRef, useEffect } from 'react'
import { DEFAULT_SEARCH_FORM, searchFormState } from '../store'
import { cloneDeep } from 'lodash-es'
import toast from 'react-hot-toast'
import { useSetAtom } from 'jotai'
import clsx from 'clsx'
import { RegionSelector } from './RegionSelector'


const $event = createAnalysisPost("AISearchPage")
interface Props{
    submit:(params: Omit<AiSearchParams, "projectId">)=>void
    isFinding:boolean
}

export default function AISearchForm({submit,isFinding}:Props) {
  // 添加一个临时的输入状态
  const [inputKeyword, setInputKeyword] = useState('')

  const platformList = [
    {
      icon: youtubeLogo,
      name: 'Youtube'
    },
    {
      icon: tiktok<PERSON>ogo,
      name: 'TikTok'
    },
    {
      icon: insLogo,
      name: 'Instagram'
    }
  ]

//   const {platform,keywords,url,description,viewCountGte} = searchFormProxyState
const [formState, setFormState] = useState(cloneDeep(DEFAULT_SEARCH_FORM))
const setGlobalFormState = useSetAtom(searchFormState)

  // 更新表单输入处理
  const handleInputChange = (field: keyof AiSearchParams, value: any) => {
    if(isFinding) return
    if(field === 'keywords'){
      value = [...formState.keywords, value]
    }
    setFormState(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const [showPlatformHint, setShowPlatformHint] = useState(false)

  const handleSubmit = () => {
    if(!formState.platform) {
      setShowPlatformHint(true)
      setTimeout(() => setShowPlatformHint(false), 3000) // 3秒后关闭动画
      return toast.error('Please select a platform')
    }
    if(!formState.keywords.length) return toast.error('Please input keywords')
    if(formState.keywords.length > 5) return toast.error('keywords cannot exceed five')
    setGlobalFormState(formState)
    $event("startSearch",{
      ...formState
    })
    formState.regions && memorySelectRegionState.setValue(formState.regions || [])
    submit(formState)
  }

  // 添加 ref
  const keywordsContainerRef = useRef<HTMLDivElement>(null)
  
  // 监听 keywords 变化，自动滚动到底部
  useEffect(() => {
    if (keywordsContainerRef.current && formState.keywords.length > 0) {
      keywordsContainerRef.current.scrollTo({
        top: keywordsContainerRef.current.scrollHeight,
        behavior: 'smooth'
      })
    }
  }, [formState.keywords])

  const [selectedRegions, setSelectedRegions] = useState<string[]>([])

  const handleRegionChange = (newRegions: string[]) => {
    setSelectedRegions(newRegions)
    handleInputChange('regions', newRegions)
  }

  return (
    <div className={clsx('space-y-6 w-[80%] max-[400px]:w-[100%]',isFinding && 'animate-pulse')}>
      {/* Social Media Icons */}
      <div className="flex items-center gap-4 justify-center">
        {platformList.map((item) => (
          <div 
            className={`flex items-center gap-2 rounded-lg px-2 py-1 box-border text-sm cursor-pointer
            ${formState.platform === item.name.toUpperCase() ? 'bg-black text-white' : 'text-black hover:bg-gray-300'}
            ${showPlatformHint ? 'animate-platform-hint' : ''}`}
            key={item.name}
            onClick={()=>handleInputChange('platform',item.name.toUpperCase())}
          >
            <img src={item.icon} className="w-6 h-6" />
            <span>{item.name}</span>
          </div>
        ))} 
      </div>

      <div className="flex-1 max-w-2xl mx-auto w-full space-y-4">
        {/* Search Section */}
        <div className="flex items-center gap-2 mb-4">
          {/* <div className="text-lg font-medium">keywords</div> */}
          <div className="flex-1 flex gap-2">
            <input 
              type="text"
              disabled={isFinding}
              placeholder="Keywords cannot exceed 5"
              value={inputKeyword}
              onChange={(e) => setInputKeyword(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && inputKeyword.trim()) {
                  if(formState.keywords.includes(inputKeyword.trim())) {
                    toast.error('Already exists')
                    return
                  }
                  handleInputChange('keywords', inputKeyword.trim())
                  setInputKeyword('') // 清空输入框
                }
              }}
              className="flex-1 h-9 px-3 rounded border border-gray-300 text-sm focus:outline-none focus:ring-1 focus:ring-black"
            />
            <button 
            disabled={isFinding}
              className="w-9 h-9 bg-black text-white flex items-center justify-center rounded"
              onClick={() => {
                if (inputKeyword.trim()) {                  
                  if(formState.keywords.includes(inputKeyword.trim())) {
                    toast.error('Already exists')
                    return
                  }
                  handleInputChange('keywords', inputKeyword.trim())
                  setInputKeyword('') // 清空输入框
                }
              }}
            >
              +
            </button>
          </div>
        </div>

        {/* Collapsible Sections with blue border */}
        <div className="border border-black rounded-xl p-2">
          <div 
            ref={keywordsContainerRef}
            className="h-[144px] overflow-y-auto scroll-smooth"
          >
            {formState.keywords.map((term, index) => (
              <button 
              disabled={isFinding}
                key={index} 
                className="w-full bg-black text-white px-4 py-2.5 text-sm text-left flex justify-between items-center rounded mb-2 last:mb-0"
              >
                {term}
                <span onClick={() => {
                    setFormState((form)=>({
                        ...form,
                        keywords:form.keywords.filter((_, i) => i !== index) 
                    }))
                  }}>−</span>
              </button>
            ))}
          </div>
        </div>

        {/* <div className="text-xs border rounded-lg px-3 py-2 flex items-center gap-1 max-[400px]:text-[10px]">
          <span>⚠</span> Fill in the 👇 to get a more accurate sorting, choose one
        </div> */}
        <div className="flex items-start gap-2">
            <span className="text-sm whitespace-nowrap w-28 pt-2 max-[400px]:text-[12px] max-[400px]:w-[95px]">Locations:</span>
            <div className="flex-1">
              <RegionSelector
                selectedRegions={selectedRegions}
                onRegionChange={handleRegionChange}
                disabled={isFinding}
              />
            </div>
          </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-sm whitespace-nowrap w-28 max-[400px]:text-[12px] max-[400px]:w-[95px]">Reference KOL:</span>
            <input 
              type="text"
              placeholder="The link of the KOL you think is most suitable for your campaign"
              value={formState.url}
              disabled={isFinding}
              onChange={(e) => handleInputChange('url', e.target.value)}
              className="flex-1 h-8 px-3 rounded border border-gray-300 text-sm focus:outline-none focus:ring-1 focus:ring-black max-[400px]:text-[12px] max-[400px]:px-1"
            />
          </div>



          {/* <div className="text-center text-sm max-[400px]:text-[12px]"> or </div>
          <div className="flex items-start gap-2">
            <span className="text-sm whitespace-nowrap w-28 pt-2 max-[400px]:text-[12px] max-[400px]:w-[95px]">KOL Profile:</span>
            <textarea 
            disabled={isFinding}
              placeholder="A paragraph describing the ideal KOL profile for your campaign&#10;e.g. You need a tech reviewer who often reviews products like phone cases and power banks"
              value={formState.description || ''}
              onChange={(e) => handleInputChange("description",e.target.value)}
              className="flex-1 h-24 px-3 py-2 rounded border border-gray-300 text-sm focus:outline-none focus:ring-1 focus:ring-black resize-none max-[400px]:text-[12px] max-[400px]:px-1"
            />
          </div> */}
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm whitespace-nowrap w-28 max-[400px]:text-[12px]  max-[400px]:w-[95px]">Avg Views {'>'}</span>
          <input 
          disabled={isFinding}
            type="number"
            value={formState.viewCountGte || 1000}
            onChange={(e) =>handleInputChange("viewCountGte",Number(e.target.value))}
            className="w-24 h-8 px-3 rounded border border-gray-300 text-sm focus:outline-none focus:ring-1 focus:ring-black max-[400px]:text-[12px] max-[400px]:px-1"
          />
        </div>

      </div>
      <button disabled={isFinding} onClick={handleSubmit} className="w-[80%] mx-auto mt-6 bg-black text-white rounded py-2.5 flex items-center justify-center">
      {isFinding && <span className="loading loading-spinner" />}
          AI Search
          <span className="text-xs ml-2 opacity-80">Estimated time: 5 minutes</span>
        </button>
    </div>
  )
}