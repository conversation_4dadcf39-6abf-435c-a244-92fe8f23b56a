import { getKOLService } from '@/services/KOLService'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { HTMLAttributes } from 'react'
import toast from 'react-hot-toast'
import { aiSearchKOLCollector } from '../models/AISearchKOLCollector'
import {
	aiSearchCollectStatisticsProxyState,
	aiSearchCollectStatisticsState,
	inAISearchCollectModeState,
	isAiSearchSendEmailEnabledState,
	searchFormState
} from '../store'
import AutoSendEmailToggleableAlert from './AutoSendEmailToggleableAlert'
import EmailTemplateSelectAlert from './EmailTemplateSelectAlert'
import OverlayComponent from './OverlayComponent'
import { getModalService } from '@/services/ModalService'

export interface KOLCollectionProps extends HTMLAttributes<HTMLDivElement> {}

const $event = createAnalysisPost("AISearchKOLCollection")
export default function AISearchKOLCollection({ ...props }: KOLCollectionProps) {
	const {
		data,
		index,
		collected: collectedKOLs,
		sentEmails,
		currentKOL,
		sourceKOL,
		sourceURL,
	} = useAtomValue(aiSearchCollectStatisticsState)
	const {
		keywords,
		platform,
		url,
		description,
		viewCountGte
	} = useAtomValue(searchFormState)
	const kolsCount = data.length
	const inQueueKOLsCount = data.length - index
	const setInCollectMode = useSetAtom(inAISearchCollectModeState)

	// const [isAutoSendEmailEnabled, setIsAutoSendEmailEnabled] = useAtom(isAiSearchSendEmailEnabledState)
    const [currentTemplateId, setCurrentTemplateId] = useStorageState(currentEmailTemplateIdState)
	
	const stopClick = useCallback(()=>{
		if(inQueueKOLsCount > 100){
			getModalService().show({
				type:"confirm",
				title:"Are you sure?",
				content:`If you stop now, all pending tasks will be lost.
				To avoid losing important information, please download the data first.`,
				confirmText:"Still Stop",
				onConfirm:()=>{
					aiSearchKOLCollector.stop().then(() => {
						setInCollectMode(false)
					})
				}
			})
		}else{
			aiSearchKOLCollector.stop().then(() => {
				setInCollectMode(false)
			})
		}
	},[inQueueKOLsCount, setInCollectMode])

	return (
		<main className="flex flex-col gap-8" css={css``}>
			<div className="flex flex-col gap-2">
				<header className="flex justify-between items-center">
					<h1 className="text-2xl font-bold text-nowrap flex-grow-0 overflow-hidden ">
						<div className=" overflow-hidden text-ellipsis">keywords</div>
						<div className="text-sm opacity-60 font-medium overflow-hidden text-ellipsis">
							{keywords.join(',')}
						</div>
					</h1>

					<div className="flex gap-2 items-center flex-wrap justify-end">
						<button
							className="btn btn-outline rounded-full btn-sm "
							onClick={async () => {
								const projectId = await currentProjectIdState.getValue()
								toast.promise(getKOLService().exportKOLs(projectId ?? ''), {
									success: 'Export Successful',
									error:"Export failed",
									loading: 'Exporting'
								})
								$event("exportKOLs",{
									projectId
								})
							}}
						>
							<FontAwesomeIcon icon="download" />
							Excel
						</button>
						<button
							className="btn btn-outline btn-error rounded-full min-w-24 btn-sm"
							onClick={stopClick}
						>
							<FontAwesomeIcon icon="stop" />
							Stop
						</button>
					</div>
				</header>
				<div className="stats shadow w-full">
					<div className="stat place-items-center">
						<div className="stat-title">Pending KOLs</div>
						<div className="stat-value">{inQueueKOLsCount}</div>
						<div className="stat-desc">Total: {kolsCount}</div>
					</div>

					<div className="stat place-items-center">
						<div className="stat-title">Collected KOLs</div>
						<div className="stat-value ">{collectedKOLs}</div>
						<div className="stat-desc ">Sent Emails: {sentEmails}</div>
					</div>
				</div>
			</div>

			<div className="flex flex-col gap-4">
				<EmailTemplateSelectAlert />
				<AutoSendEmailToggleableAlert isAiSearch={true} />
			</div>

			<section className=" w-full flex gap-1 justify-center items-center flex-wrap">
				<button
					className="btn border-1 border-black bg-white  rounded-xl w-[60%] py-3 shadow-sm"
					onClick={() => {
						aiSearchKOLCollector.dislike()
					}}
				>
					👋 No
					</button>
				<button
					className="btn border-1 border-black bg-[#FFFBEB] hover:bg-[#FEF3C7] rounded-xl w-[60%] py-3 shadow-sm"
					onClick={async () => {
						aiSearchKOLCollector.like()

						if (currentTemplateId) {
								getKOLService()
									.sendEmail(aiSearchKOLCollector.queue.data[aiSearchKOLCollector.queue.index].id)
									.then(() => {
										aiSearchCollectStatisticsProxyState.sentEmails++
										$event("autoSendEmail",{
											type:"like"
										})
									})
						}
					}}
				>
					✅ Like
					</button>

			</section>
			{inQueueKOLsCount > 0 && (
				<OverlayComponent kolCollector={aiSearchKOLCollector} />
			)}
		</main>
	)
}