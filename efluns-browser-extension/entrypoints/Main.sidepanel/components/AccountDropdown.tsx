import { getAuthService } from '@/services/AuthService'
import { CookieSessionManager } from '@/services/CookieSessionManager'
import { HTMLAttributes } from 'react'

const log = makeModuleLog('AccountDropdown')
export interface AccountDropdownProps extends HTMLAttributes<HTMLDivElement> {}

export default function AccountDropdown({ ...props }: AccountDropdownProps) {
	const [session] = useStorageState(authSessionState)
	const name = session?.user?.user_metadata?.name as string
	if (session) {
		CookieSessionManager.setSessionToCookies(session)
		log(CookieSessionManager, 'ccc')
	}
	log({ session })

	return (
		<div className="dropdown dropdown-start dropdown-top h-fit">
			<div tabIndex={0} role="button" className="btn btn-ghost btn-sm btn-circle">
				<div className="avatar placeholder">
					<div className="bg-neutral text-neutral-content w-8  rounded-full">
						<span className="text-xs">{name?.slice(0, 1)}</span>
					</div>
				</div>
			</div>
			<ul tabIndex={0} className="dropdown-content menu glass rounded-box z-[1]  p-2 shadow translate-x-[-55%]">
				{/* <li>
					<a className="" onClick={handleConfigReset}>
						Reset
					</a>
				</li> */}
				<li>
					<a className="" onClick={() => getAuthService().logout()}>
						Logout
					</a>
				</li>
			</ul>
		</div>
	)
}
