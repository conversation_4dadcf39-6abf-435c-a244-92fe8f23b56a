import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import clsx from 'clsx'
import { useAtom } from 'jotai'
import { HTMLAttributes } from 'react'
import { isAiSearchSendEmailEnabledState, isAutoSendEmailEnabledState } from '../store'

export interface AutoSendEmailToggleableAlertProps extends HTMLAttributes<HTMLDivElement> {
	isAiSearch?: boolean
}

export default function AutoSendEmailToggleableAlert({ isAiSearch = false }: AutoSendEmailToggleableAlertProps) {
	const [isEnabled, setIsEnabled] = useAtom(isAiSearch ? isAiSearchSendEmailEnabledState : isAutoSendEmailEnabledState)
	return (
		<div
			role="alert"
			className={clsx('alert flex items-center  text-start justify-between transition ', {
				[isEnabled ? 'alert-success text-white' : 'bg-base-100']: true
			})}
		>
			<div className="flex items-center gap-2">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill={isEnabled ? 'currentColor' : 'none'}
					viewBox="0 0 24 24"
					className={clsx('stroke-info shrink-0 w-6 h-6 ', {
						'stroke-success': isEnabled
					})}
				>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth="2"
						d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
					/>
				</svg>
				<div>
					<div className="font-bold max-[400px]:text-[12px]">Auto Send Email {isEnabled ? 'Enabled' : 'Disabled'}</div>
					<div className="text-xs">
						When you click “Suitable”, automatically send an email to the KOL
						<div
							className="tooltip inline underline font-semibold cursor-help"
							data-tip="When the following conditions are met, click “Suitable” to automatically send an email. 1. The “Auto Send Email” feature is enabled. 2. The current account has authorized EasyKOL to send emails. 3. The system detects the KOL's email address."
						>
							<FontAwesomeIcon
								className="mx-1"
								icon={{
									prefix: 'far',
									iconName: 'circle-question'
								}}
							/>
						</div>
					</div>
				</div>
			</div>

			<input
				type="checkbox"
				className={clsx('toggle', {
					'toggle-success': isEnabled
				})}
				checked={isEnabled}
				onChange={(e) => {
					setIsEnabled(e.target.checked)
				}}
			/>
		</div>
	)
}
