import { CreateEmailTemplatePayload, UpdateEmailTemplatePayload } from '@/@types'
import clsx from 'clsx'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import { HTMLAttributes } from 'react'

const log = makeModuleLog('EmailTemplateModal')

export interface EmailTemplateModalProps extends Omit<HTMLAttributes<HTMLDialogElement>, 'onSubmit'> {
	initialData?: UpdateEmailTemplatePayload
	onSubmit?: (data: UpdateEmailTemplatePayload | CreateEmailTemplatePayload) => Promise<void>
}

export default function EmailTemplateModal({ className, onSubmit, initialData, ...props }: EmailTemplateModalProps) {
	const ref = useRef<HTMLDialogElement | null>(null)
	const contentEditorRef = useRef<HTMLDivElement | null>(null)
	const quillRef = useRef<Quill | null>(null)

	const cleanupQuill = useCallback(() => {
		if (contentEditorRef.current) {
			contentEditorRef.current.innerHTML = ''
		}
		quillRef.current = null
	}, [])

	const handleClose = useCallback(() => {
		ref.current?.close()
	}, [])

	useEffect(() => {
		const contentEditor = contentEditorRef.current
		if (!contentEditor) return

		cleanupQuill()

		const editorContainer = document.createElement('div')
		contentEditor.appendChild(editorContainer)

		quillRef.current = new Quill(editorContainer, {
			theme: 'snow',
			placeholder: 'Email Template'
		})

		if (initialData?.content) {
			quillRef.current.root.innerHTML = initialData.content
		} else {
			quillRef.current.setText('')
		}

		return () => {
			cleanupQuill()
		}
	}, [initialData, cleanupQuill])

	return (
		<dialog className={clsx('modal', className)} ref={ref} {...props}>
			<div className="modal-box w-[90%] h-[80%]">
				<form
					className="h-full grid "
					css={css`
						grid-template-rows: auto 1fr auto;
						overflow: hidden;
					`}
					onSubmit={(e) => {
						e.preventDefault()
						const formData = new FormData(e.target as HTMLFormElement)
						const data: CreateEmailTemplatePayload = {
							name: formData.get('name')?.toString() ?? '',
							subject: formData.get('subject')?.toString() ?? '',
							content: quillRef.current?.root.innerHTML ?? '',
						}
						onSubmit?.({ ...data, ...(initialData  ? { id: initialData.id } : {}) }).then(
							() => {
								handleClose()
							}
						)
					}}
				>
					<h3 className="font-bold text-xl px-4 mb-1">
						{initialData ? 'Edit Template' : 'New Template'}
					</h3>
					<div
						className="space-y-4 p-4 grid overflow-hidden"
						css={css`
							grid-template-rows: auto auto 1fr;
						`}
					>
						<label className="block">
							<input
								type="text"
								className="input input-sm input-bordered  block w-full"
								placeholder="Template Name"
								name="name"
								defaultValue={initialData?.name}
							/>
						</label>
						<label className="block">
							<input
								type="text"
								className="input input-sm input-bordered  block w-full"
								placeholder="Email Subject"
								name="subject"
								defaultValue={initialData?.subject}
							/>
						</label>
						<label className=" block overflow-auto">
							<div
								className="flex flex-col w-full h-full"
								css={css`
									.ql-container {
										overflow: hidden;
									}
								`}
							>
								<div ref={contentEditorRef} />
							</div>
						</label>
					</div>

					<div className="modal-action mt-auto px-4 gap-2">
						{/* if there is a button in form, it will close the modal */}
						<button type="button" className="btn btn-sm rounded-full" onClick={handleClose}>
							Close
						</button>
						<button type="submit" className="btn btn-sm btn-primary  rounded-full ">
							Submit
						</button>
					</div>
				</form>
			</div>
		</dialog>
	)
}
