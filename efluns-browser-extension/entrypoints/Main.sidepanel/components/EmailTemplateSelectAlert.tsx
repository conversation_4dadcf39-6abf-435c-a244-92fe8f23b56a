import { HTMLAttributes, useState, useRef, useEffect } from 'react'
import { emailTemplatesState, currentEmailTemplateIdState } from '@/utils/storages'
import { useAtom } from 'jotai'
import { routerState } from '../store'
export interface EmailTemplateSelectAlertProps extends HTMLAttributes<HTMLDivElement> {}

export default function EmailTemplateSelectAlert({ className, ...props }: EmailTemplateSelectAlertProps) {
    const [templates] = useStorageState(emailTemplatesState)
    const [currentTemplateId, setCurrentTemplateId] = useStorageState(currentEmailTemplateIdState)
    const [isOpen, setIsOpen] = useState(false)
    const dropdownRef = useRef<HTMLDivElement>(null)
    const currentTemplate = templates.find((t) => t.id === currentTemplateId)

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false)
            }
        }
        document.addEventListener('mousedown', handleClickOutside)
        return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [])

    const [_,setRouter] = useAtom(routerState)
    const handleNewTemplate = () => {
        setRouter({
            router:'establish-contact'
        })
        const modal = document.querySelector('dialog#email-template-modal') as HTMLDialogElement | null
        console.log(modal);
        
        if (modal) {
            modal.showModal()
            setIsOpen(false)
        }
    }

    return (
        <div className="relative" ref={dropdownRef} {...props}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="w-full px-4 py-2 text-left bg-white border rounded-full flex items-center justify-between hover:bg-gray-50"
            >
                <span>{currentTemplate?.name ?? 'Auto Email Off'}</span>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className={`h-5 w-5 transition-transform ${isOpen ? 'rotate-180' : ''}`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                >
                    <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                    />
                </svg>
            </button>
            
            {isOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg">
                    <ul className="py-1">
                        <li
                            className={`px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between ${
                                currentTemplateId === null ? 'bg-[#f2f2f2]' : ''
                            }`}
                            onClick={() => {
                                setCurrentTemplateId(null)
                                setIsOpen(false)
                            }}
                        >
                            <span>Auto Email Off</span>
                            {currentTemplateId === null && (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-black-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                            )}
                        </li>
                        {templates.map((template) => (
                            <li
                                key={template.id}
                                className={`px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between ${
                                    template.id === currentTemplateId ? 'bg-[#f2f2f2]' : ''
                                }`}
                                onClick={() => {
                                    setCurrentTemplateId(template.id)
                                    setIsOpen(false)
                                }}
                            >
                                <span>{template.name}</span>
                                {template.id === currentTemplateId && (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[bg-[#f2f2f2]]" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                )}
                            </li>
                        ))}
                        <div className="divider my-0" />
                        <li 
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center gap-2"
                            onClick={handleNewTemplate}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 4v16m8-8H4"
                                />
                            </svg>
                            New Template
                        </li>
                    </ul>
                </div>
            )}
        </div>
    )
}
