import { CreateEmailTemplatePayload, EmailTemplate, UpdateEmailTemplatePayload } from '@/@types'
import { EMAIL_TEMPLATES } from '@/constants'
import { getEmailTemplateService } from '@/services/EmailTemplateService'
import { $delete, patch, post, put } from '@/utils/request'
import { IconProp } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useMutation } from '@tanstack/react-query'
import dayjs from 'dayjs'
import { HTMLAttributes, useState, useRef } from 'react'
import EmailTemplateModal from './EmailTemplateModal'
import toast from 'react-hot-toast'

const log = makeModuleLog('EmailTemplateCard')
export interface EmailTemplateTableProps extends HTMLAttributes<HTMLDivElement> {}

export default function EmailTemplateTable({ ...props }: EmailTemplateTableProps) {
	const [data] = useStorageState(emailTemplatesState)
	const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate>();
	const tbodyRef = useRef<HTMLTableSectionElement>(null);
	// useMutation to create email template
	const { mutateAsync: createEmailTemplate } = useMutation({
		mutationKey: [EMAIL_TEMPLATES],
		mutationFn: async (data: CreateEmailTemplatePayload) => post(EMAIL_TEMPLATES, data).then((res) => res.data),
		onSuccess: () => {
			getEmailTemplateService().refreshEmailTemplates().then(()=>{
				setTimeout(() => {
					tbodyRef.current?.scrollTo({
						top: tbodyRef.current.scrollHeight,
						behavior: 'smooth'
					})
				}, 100)
			})

		}
	})
	// useMutation to update email template
	const { mutateAsync: updateEmailTemplate } = useMutation({
		mutationKey: [EMAIL_TEMPLATES],
		mutationFn: async (data: UpdateEmailTemplatePayload) => patch(`${EMAIL_TEMPLATES}/${data.id}`, data).then((res) => res.data),
		onSuccess: () => getEmailTemplateService().refreshEmailTemplates()
	})

	const { mutateAsync: deleteEmailTemplate } = useMutation({
		mutationKey: [EMAIL_TEMPLATES],
		mutationFn: async (id: string) => $delete(`${EMAIL_TEMPLATES}/${id}`).then((res) => res.data),
		onSuccess: () => getEmailTemplateService().refreshEmailTemplates()
	})
	
	const showModal = useCallback(() => {
		;(document.querySelector('dialog#email-template-modal') as HTMLDialogElement | null)?.showModal()
	}, [])

	useEffect(() => {
		getEmailTemplateService().refreshEmailTemplates()
	}, [])


	useEffect(() => {
		const unsubs = onMessage('toogleEmailTemplateModal', (msg) => {
			if (msg.data) {
				showModal()
			}
		})

		return unsubs
	}, [showModal])

	return (
		<div className="w-full flex flex-col gap-4">
			<table
				className="table bg-base-100 shadow-lg"
				css={css`
					tr {
						display: table;
						table-layout: fixed;
						width: 100%;
					}
					@media (max-width: 400px) {
						th:first-child,
						td:first-child {
							width: 40%;
						}
						th:last-child,
						td:last-child {
							width: 60%;
						}
					}
				`}
			>
				{/* head */}
				<thead>
					<tr>
						<th>Name</th>
						<th>Created At</th>
					</tr>
				</thead>
				<tbody ref={tbodyRef} className=" max-h-[300px] overflow-hidden overflow-y-auto block">
					{data.map((item) => (
						<tr key={item.id} className="group">
							<td>{item.name}</td>
							<td className="text-nowrap relative">
								{dayjs(item.createdAt).format('MM/DD HH:mm')}
								<div className="absolute right-2 top-1/2 -translate-y-1/2 hidden group-hover:flex gap-2">
									<button className="btn btn-ghost btn-xs" onClick={() => {
										setSelectedTemplate(item);
										showModal();
									}}>
										<FontAwesomeIcon icon={'fa-solid fa-edit' as IconProp} />
									</button>
									<button className="btn btn-ghost btn-xs" onClick={() => {toast.promise(deleteEmailTemplate(item.id), {
											loading: 'Deleting...',
											success: 'Deleted successfully',
											error: 'Delete failed'
										})
									}}>
										<FontAwesomeIcon icon={'fa-solid fa-trash' as IconProp} />
									</button>
								</div>
							</td>
						</tr>
					))}
				</tbody>
			</table>
			<button
				className="btn btn-primary rounded-full btn-sm place-self-end"
				onClick={() => {
					setSelectedTemplate(undefined);
					showModal()
				}}
			>
				<FontAwesomeIcon icon={'fa-solid fa-plus' as IconProp} />
				Add
			</button>

			<EmailTemplateModal
				id="email-template-modal"
				initialData={selectedTemplate}
				onSubmit={async (data) => {
					if ('id' in data) {
						await updateEmailTemplate(data);
					} else {
						await createEmailTemplate(data);
					}
					setSelectedTemplate(undefined);
				}}
			/>
		</div>
	)
}
