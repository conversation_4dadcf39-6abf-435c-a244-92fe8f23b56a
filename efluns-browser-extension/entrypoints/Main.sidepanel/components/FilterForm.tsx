import { Platform } from '@/@types/platform'
import { SimilarCreateTaskParams } from '@/@types/similar'
import { UNTERMINATED_TASKS } from '@/constants/api'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import { getCommonService } from '@/services/CommonService'
import { getModalService } from '@/services/ModalService'
import { getProjectService } from '@/services/ProjectService'
import { get } from '@/utils/request'
import { currentProjectIdState, hasWarnMaxInputState, speedModeState } from '@/utils/storages'
import clsx from 'clsx'
import { useAtom } from 'jotai'
import React, { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import { browser } from 'wxt/browser'
import Similar from '../models/Similar'
import { DEFAULT_FILTER_OPTIONS, filtersState } from '../store'
import { checkFilterValid } from '../utils/checkFilterValid'
import { ModeToggle } from './ModeToggle'
import { RegionSelector } from './RegionSelector'

const log = makeModuleLog('FilterForm')
interface GetApiSearchLongCrawlerTaskIdResponse {
	statusCode: number
	error: null | string
	message: string
	data: Array<{
		id: string
		taskId: string
		type:
			| 'KEYWORD'
			| 'SIMILAR'
			| 'HASH_TAG_BREAK'
			| 'SEARCH_INPUT_BREAK'
			| 'FOLLOWERS_SIMILAR'
			| 'FOLLOWING_LIST'
			| 'AUDIENCE_ANALYSIS'
			| 'EASYKOL_TRACK'
			| 'BGM_BREAK'
			| 'WEB_LIST'
			| 'TAGGED_BREAK'
			| 'POST_AUDIENCE'
			| 'LONG_CRAWLER'
			| 'AUDIENCE_FAKE'
		reason:
			| 'SEARCH'
			| 'LIKE'
			| 'SUPERLIKE'
			| 'NEXT_PAGE'
			| 'AUDIENCE_ANALYSIS'
			| 'EASYKOL_TRACK'
			| 'AUDIENCE_FAKE'
			| 'POST_AUDIENCE'
		status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'RESULT_READY' | 'PAUSED' | 'PAUSING' | 'COMPLETING'
		meta: any
		params: any
		candidates: any
		latestPerfectFitUsers: Array<{
			id: string
			title: string | null
			description: string | null
			email: string | null
			emailSource: string | null
			historyEmails: string[]
			platformAccount: string | null
			platform: 'INSTAGRAM' | 'YOUTUBE' | 'TIKTOK' | 'TWITTER'
			createdAt: string
			updatedAt: string
			avatar: string | null
			links: string[]
			contacts: any | null
			emailUpdatedAt: string | null
			audienceAnalysis: any | null
			instagramUser: {
				id: string
				username: string
				profilePicUrl: string | null
				fullName: string | null
				followerCount: number
				averageLikeCount: number
				averageCommentCount: number
				lastPublishedTime: number
				region: string | null
				createdAt: string
				updatedAt: string
				posts: Array<{
					id: string | null
					comment_count: number
					like_count: number
					play_count: number
					created_at: number
					caption?: string
					media_type?: string
					media_url?: string
					thumbnail_url?: string
					permalink?: string
					shortcode?: string
					video_url?: string
				}>
				accountInfo?: any
				publicationStats?: any
			} | null
			ProjectKol: { attitude: 'LIKE' | 'DISLIKE' | 'NORATE' | 'SUPERLIKE' } | null
			reason?: string
		}>
		latestContentOkUsers: Array<{
			id: string
			title: string | null
			description: string | null
			email: string | null
			emailSource: string | null
			historyEmails: string[]
			platformAccount: string | null
			platform: 'INSTAGRAM' | 'YOUTUBE' | 'TIKTOK' | 'TWITTER'
			createdAt: string
			updatedAt: string
			avatar: string | null
			links: string[]
			contacts: any | null
			emailUpdatedAt: string | null
			audienceAnalysis: any | null
			instagramUser: {
				id: string
				username: string
				profilePicUrl: string | null
				fullName: string | null
				followerCount: number
				averageLikeCount: number
				averageCommentCount: number
				lastPublishedTime: number
				region: string | null
				createdAt: string
				updatedAt: string
				posts: Array<{
					id: string | null
					comment_count: number
					like_count: number
					play_count: number
					created_at: number
					caption?: string
					media_type?: string
					media_url?: string
					thumbnail_url?: string
					permalink?: string
					shortcode?: string
					video_url?: string
				}>
				accountInfo?: any
				publicationStats?: any
			} | null
			ProjectKol: { attitude: 'LIKE' | 'DISLIKE' | 'NORATE' | 'SUPERLIKE' } | null
			reason?: string
		}>
		createdAt: string
		updatedAt: string
	}>
}

interface FilterFormProps {
	disabled?: boolean
	handleFindSimilars?: (isVision?: boolean) => void
}

// 从常量类型推导出具体类型

export default function FilterForm({ disabled = false, handleFindSimilars }: FilterFormProps) {
	const [filters, setFilters] = useAtom(filtersState)
	const [activeTabURL] = useStorageState(activeTabURLState)
	const [env] = useStorageState(extensionEnvState)

	const currentKolHandler = useMemo(() => {
		if (!activeTabURL) return ''
		return activeTabURL.includes('instagram') ? extractInsKOLHandler(activeTabURL) : extractKOLHandler(activeTabURL)
	}, [activeTabURL])

	const platform = useMemo(() => {
		if (!activeTabURL) return null
		try {
			const platform = getPlatform(activeTabURL)
			return platform
		} catch (error) {
			return null
		}
	}, [activeTabURL])

	// const [isTikTok,setIsTikTok] = useState(false)
	// const [supportPage,setSupportPage] = useState<SupportPage | null>(null)
	const [formValues, setFormValues] = useState<Record<string, number | undefined>>({
		minSubscribers: filters.minSubscribers || DEFAULT_FILTER_OPTIONS.minSubscribers,
		maxSubscribers: filters.maxSubscribers || DEFAULT_FILTER_OPTIONS.maxSubscribers,
		minAverageLikeCount: filters.minAverageLikeCount || DEFAULT_FILTER_OPTIONS.minAverageLikeCount,
		maxAverageLikeCount: filters.maxAverageLikeCount || DEFAULT_FILTER_OPTIONS.maxAverageLikeCount,
		videosAverageViews: filters.videosAverageViews || DEFAULT_FILTER_OPTIONS.videosAverageViews,
		maxVideosAverageViews: filters.maxVideosAverageViews || DEFAULT_FILTER_OPTIONS.maxVideosAverageViews
	})
	const [speedMode, setSpeedMode] = useStorageState(speedModeState)
	const [mode, setMode] = useState(1)
	const [unterminatedTask, setUnterminatedTask] = useState<
		GetApiSearchLongCrawlerTaskIdResponse['data'][number] | null
	>(null)
	const minScriptionHasChanged = useRef(false)
	const [visionMap, setVisionMap] = useState({
		[Platform.YOUTUBE]: false,
		[Platform.TIKTOK]: false,
		[Platform.INS]: true,
		[Platform.TWITTER]: true
	})
	const [isPoorTags, setIsPoorTags] = useState(false)
	const modalIdRef = useRef<string | null>(null)

	const isVision = useMemo(() => {
		if (!platform) return false
		return visionMap[platform] || false
	}, [visionMap, platform])

	const setIsVision = useCallback(
		(bool: boolean) => {
			if (!platform) return
			setVisionMap({ ...visionMap, [platform]: bool })
		},
		[platform, setVisionMap, visionMap]
	)

	const updatePlatformSettings = useCallback(() => {
		if (disabled || !platform) return
		if (!minScriptionHasChanged.current) {
			if (platform === Platform.INS && formValues.minSubscribers === DEFAULT_FILTER_OPTIONS.minSubscribers) {
				setFormValues({ ...formValues, minSubscribers: 1000 })
			} else if (platform !== Platform.INS && formValues.minSubscribers === 1000) {
				setFormValues({ ...formValues, minSubscribers: DEFAULT_FILTER_OPTIONS.minSubscribers })
			}
		}
		setMode(speedMode[platform as keyof typeof speedMode])
		// setIsVision(visionMap[platform])
	}, [disabled, platform, speedMode, formValues,setFormValues])

	useEffect(() => {
		updatePlatformSettings()
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [platform])

	const handleRegionChange = (newRegions: string[]) => {
		const newFilters = { ...filters, regions: newRegions }
		setFilters(newFilters)
		// onFilterChange(newFilters);
	}

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.name === 'minSubscribers') {
			minScriptionHasChanged.current = true
		}
		const { name, value } = e.target
		setFormValues((prev) => {
			const newValue = {
				...prev,
				[name]: parseInt(value)
			}
			log("newValue", newValue)
			return newValue
		})
	}

	// 创建 vision 配置  看情况判断是否需要传 1/2
	const createVisionTooltip: () => Pick<SimilarCreateTaskParams, 'ttMode' | 'ttModeReason' | 'ytbMode'> =
		useCallback(() => {
			const obj = {
				[Platform.TIKTOK]: 'ttMode',
				[Platform.INS]: 'insMode',
				[Platform.YOUTUBE]: 'ytbMode',
				[Platform.TWITTER]: 'twitterMode'
			}
			return {
				ttMode: undefined,
				ttModeReason: undefined,
				ytbMode: undefined,
				twitterMode: undefined,
				[obj[platform!]]: isVision ? 2 : 1
			}
		}, [isVision, platform])

		const submit = useCallback(async () => {
			let shouldUseVision = isVision // 保存当前值
			const visionConfig = createVisionTooltip()
			if (platform === Platform.TIKTOK) {
				const tags = await Similar.checkTags()
				if (tags <= 5) {
					shouldUseVision = true
					setIsVision(true)
					setIsPoorTags(true)
					visionConfig.ttMode = 2
					visionConfig.ttModeReason = `tags数量为${tags}`
					toast.success('Too few tags , Successfully auto switched to Vision mode')
				}
			}
	
			// 使用函数式更新获取最新的 formValues
			setFormValues(currentFormValues => {
				log("submit currentFormValues", currentFormValues)
	
				const newFilters = {
					...filters,
					minSubscribers: currentFormValues.minSubscribers,
					maxSubscribers: currentFormValues.maxSubscribers,
					videosAverageViews: currentFormValues.videosAverageViews,
					maxVideosAverageViews: currentFormValues.maxVideosAverageViews,
					minAverageLikeCount: currentFormValues.minAverageLikeCount,
					maxAverageLikeCount: currentFormValues.maxAverageLikeCount,
					taskRound: mode,
					...visionConfig
				}
	
				log("submit newFilters", newFilters)
	
				if (!checkFilterValid(newFilters)) {
					return currentFormValues
				}
	
				setFilters(newFilters)
				if (platform) {
					setSpeedMode({ ...speedMode, [platform]: mode })
					setVisionMap({ ...visionMap, [platform]: isVision })
				}
				handleFindSimilars?.(shouldUseVision)
	
				return currentFormValues
			})
		}, [
			isVision,
			createVisionTooltip,
			platform,
			filters,
			mode,
			setFilters,
			handleFindSimilars,
			setIsVision,
			setSpeedMode,
			speedMode,
			visionMap
		])

	// const handleSimilarScoreChange = (value: number) => {
	//   const newFilters = { ...filters, similarScore: value };
	//   setFilters(newFilters);
	//   onFilterChange(newFilters);
	// };

	const handleMaxInputFocus = async (type: 'followers' | 'likes' | 'views') => {
		const hasWarned = await hasWarnMaxInputState.getValue()

		if (!hasWarned) {
			getModalService().show({
				content: [`No upper limit on ${type} is recommended.`, `As it helps us find you the most relevant creators 🥰`],
				countdown: 3,
				type: 'alert',
				confirmText: 'Got it'
			})
			await hasWarnMaxInputState.setValue(true)
		}
	}

	/** 获取当前项目下未终止的长任务 */
	const getUnterminatedTasks = async () => {
		const res = await get<GetApiSearchLongCrawlerTaskIdResponse>(UNTERMINATED_TASKS, {
			params: {
				taskType: 'LONG_CRAWLER'
			}
		})
		const projectId = await currentProjectIdState.getValue()
		const tasks: GetApiSearchLongCrawlerTaskIdResponse['data'] = res.data.data
		const task = tasks.find((item: any) => item.projectId === projectId && !item.isTerminated)
		setUnterminatedTask(task || null)
	}

	/** 跳转到 Web 端查看未终止任务 */
	const handleGoToWeb = async () => {
		if (!unterminatedTask?.id || !platform) return
		const url = `https://easykol.com/auto-task?taskId=${unterminatedTask.id}&platform=${platform}`
		await browser.tabs.create({ url })
		window.close()
	}
	useEffect(() => {
		if (mode === 4) {
			getUnterminatedTasks()
		}
	}, [mode])

	const isInsVision = mode === 4 && isVision
	const handleEndTask = async () => {
		await getProjectService().terminateProject('LONG_CRAWLER')
		setUnterminatedTask(null)
	}
	useEffect(() => {
		if (isInsVision && unterminatedTask?.id && platform === Platform.INS) {
			// Only show modal if we haven't already shown it
			if (!modalIdRef.current) {
				const modalId = getModalService().show({
					content: 'You still have an Auto Task in progress. Would you like to continue or end it?',
					type: 'warning',
					warningText: 'End Task',
					onWarning: () => {
						handleEndTask()
						if (modalIdRef.current) {
							getModalService().close(modalIdRef.current)
						}
						modalIdRef.current = null
					},
					cancelText: 'View Details',
					onCancel: () => {
						handleGoToWeb()
						if (modalIdRef.current) {
							getModalService().close(modalIdRef.current)
						}
						modalIdRef.current = null
					}
				})
				modalIdRef.current = modalId
			}
		} else {
			// Close modal if condition is no longer met
			if (modalIdRef.current) {
				getModalService().close(modalIdRef.current)
				modalIdRef.current = null
			}
		}

		// Cleanup function to close modal when component unmounts
		return () => {
			if (modalIdRef.current) {
				getModalService().close(modalIdRef.current)
				modalIdRef.current = null
			}
		}
	}, [isInsVision, unterminatedTask?.id, platform])

	return (
		<>
			<div className="form-control w-full max-w-xs">
				{/* Mode Selector */}
				<div className="space-y-2">
					<div className="flex justify-between items-center">
						<div className="flex items-center gap-2">
							<h3 className="text-base font-medium">Mode</h3>
							{platform === Platform.INS && isVision && (
								<div className="relative group ml-1">
									<span className="cursor-help  text-gray-400 text-sm">ⓘ</span>
									<div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-1 py-2 bg-black text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
										{mode === 1
											? 'Standard task usage'
											: mode === 2
												? 'It causes 2x task usage'
												: 'It causes 4x task usage'}
										<div className="absolute top-full left-1/2 -translate-x-1/2 border-4 border-transparent border-t-black" />
									</div>
								</div>
							)}
						</div>
						{platform === Platform.INS && isVision && (
							<div className="flex items-center gap-1 text-gray-400 text-[15px]">
								{[2, 4].map((option, index) => (
									<React.Fragment key={option}>
										<span
											onClick={() => setMode(option)}
											className={clsx(
												'flex-1 py-2 text-center cursor-pointer relative',
												mode === option ? 'text-black font-bold' : 'text-gray-400 hover:text-gray-700'
											)}
										>
											{option === 4 ? (
												<>
													Auto
													<span className="absolute font-normal -top-0 -right-3 bg-red-500 text-white text-[10px] px-1 rounded">
														hot
													</span>
												</>
											) : (
												`${option}X`
											)}
										</span>
										{index < 2 && <span className="text-gray-300">|</span>}
									</React.Fragment>
								))}
							</div>
						)}
					</div>

					<ModeToggle
						isVisionEnabled={isVision}
						onToggle={(bool) => setIsVision(bool || !isVision)}
						normalDisabled={platform === Platform.TWITTER || (isPoorTags && disabled)}
						normalDisabledText={
							platform === Platform.TWITTER
								? 'Twitter is not supported in Normal Mode'
								: isPoorTags
									? 'Too few tags, need visual mode.'
									: ''
						}
					/>
				</div>
				{platform !== Platform.TWITTER && (
					<>
						<label className="label">
							<span className="label-text">Regions</span>
						</label>
						<RegionSelector
							selectedRegions={filters.regions || []}
							onRegionChange={handleRegionChange}
							disabled={disabled}
						/>
					</>
				)}
				{
					<>
						<label className="label">
							<span className="label-text">Followers</span>
						</label>
						<div className="flex items-center gap-2">
							<input
								type="number"
								name="minSubscribers"
								value={formValues.minSubscribers}
								onChange={handleChange}
								placeholder="From"
								className={`input input-bordered w-full max-w-xs rounded-full ${disabled ? 'text-gray-500' : ''}`}
								disabled={disabled}
							/>
							<span className="text-gray-500">→</span>
							<input
								type="number"
								name="maxSubscribers"
								value={formValues.maxSubscribers}
								onChange={handleChange}
								onFocus={() => handleMaxInputFocus('followers')}
								placeholder="∞"
								className={`input input-bordered w-full max-w-xs rounded-full ${disabled ? 'text-gray-500' : ''}`}
								disabled={disabled}
							/>
						</div>
					</>
				}
				{platform === Platform.TWITTER ? (
					<></>
				) : platform !== Platform.INS ? (
					<>
						<label className="label">
							<span className="label-text">Avg Views</span>
						</label>
						<div className="flex items-center gap-2">
							<input
								type="number"
								name="videosAverageViews"
								value={formValues.videosAverageViews}
								onChange={handleChange}
								placeholder="From"
								className={`input input-bordered w-full max-w-xs rounded-full ${disabled ? 'text-gray-500' : ''}`}
								disabled={disabled}
							/>
							<span className="text-gray-500">→</span>
							<input
								type="number"
								name="maxVideosAverageViews"
								value={formValues.maxVideosAverageViews}
								onChange={handleChange}
								onFocus={() => handleMaxInputFocus('views')}
								placeholder="∞"
								className={`input input-bordered w-full max-w-xs rounded-full ${disabled ? 'text-gray-500' : ''}`}
								disabled={disabled}
							/>
						</div>
					</>
				) : (
					<>
						<label className="label">
							<span className="label-text">Avg Likes</span>
						</label>
						<div className="flex items-center gap-2">
							<input
								type="number"
								name="minAverageLikeCount"
								value={formValues.minAverageLikeCount || ''}
								onChange={handleChange}
								placeholder="From"
								className={`input input-bordered w-full max-w-xs rounded-full ${disabled ? 'text-gray-500' : ''}`}
								disabled={disabled}
							/>
							<span className="text-gray-500">→</span>
							<input
								type="number"
								name="maxAverageLikeCount"
								value={formValues.maxAverageLikeCount || ''}
								onChange={handleChange}
								onFocus={() => handleMaxInputFocus('likes')}
								placeholder="∞"
								className={`input input-bordered w-full max-w-xs rounded-full ${disabled ? 'text-gray-500' : ''}`}
								disabled={disabled}
							/>
						</div>
					</>
				)}
				<button
					className={clsx(
						'btn mt-3 bg-black hover:bg-[#000]/80 w-full rounded-full text-white min-w-56 truncate  flex flex-nowrap items-center justify-center gap-2', // 添加 flex 布局和对齐属性
						{
							'bg-red-500 hover:bg-red-600': env === 'beta',
							'bg-yellow-500 hover:bg-yellow-600': env === 'localhost'
						}
					)}
					onClick={submit}
					onMouseEnter={() => {
						if (disabled || platform !== Platform.YOUTUBE) return
						getCommonService().sendMessageToCurrentYoutubeContent({ type: 'setSimilarFromMode' })
					}}
					disabled={disabled}
				>
					{disabled && <span className="loading loading-spinner" />}
					<span className="truncate">
						{currentKolHandler ? `Similar From ${currentKolHandler}` : '🔍 Find Similar '}
					</span>
				</button>
				{disabled && <div className="text-center">Estimated query time: 1 minute</div>}
			</div>
		</>
	)
}
