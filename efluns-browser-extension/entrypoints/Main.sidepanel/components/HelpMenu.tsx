import { useState, useRef, useEffect } from "react"
import { HelpCircle, BookOpen } from 'lucide-react'
import { useWindowHeight } from "@/hooks/useWindowHeight"

interface HelpMenuProps {
  className?: string
}

export function HelpMenu({ className }: HelpMenuProps) {
  const [showHelpMenu, setShowHelpMenu] = useState(false)
  const helpContainerRef = useRef<HTMLDivElement>(null)
  const {windowHeight} = useWindowHeight()

  // 处理点击事件，关闭帮助菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (helpContainerRef.current && !helpContainerRef.current.contains(event.target as Node)) {
        setShowHelpMenu(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // 处理客服和教程按钮点击
  const handleCustomerService = () => {
    const url = "https://airy-update.notion.site/Unlock-the-full-EasyKOL-14ae8694012c804595fffc6afe85eefa?pvs=4"
    window.open(url, "_blank")
    // 这里可以实现联系客服的逻辑
    setShowHelpMenu(false)
  }

  const handleTutorial = () => {
    const url = "https://orbavgj2zs.feishu.cn/wiki/Ak5EwZEnViQqtakKz4nccgHinod"
    window.open(url, "_blank")
    // 这里可以实现查看教程的逻辑
    setShowHelpMenu(false)
  }

  if(windowHeight < 650) {
    return null
  }

  return (
    <div className={`flex flex-col justify-center items-center ${className} w-[calc(100%-45px)]`} ref={helpContainerRef} style={{bottom: windowHeight < 650 ? 0 : 'auto'}}>
      <div className="relative">
        {/* 按钮 */}
        <button
          className="text-gray-400 text-xs bg-transparent hover:bg-gray-50 rounded-full px-4 py-1.5 border border-gray-100 transition-colors flex items-center"
          onMouseEnter={() => setShowHelpMenu(true)}
        >
          <HelpCircle className="h-3 w-3 mr-1.5 text-gray-400" />
          Help & Tutorial
        </button>

        {/* 连接区域 - 确保鼠标可以从按钮移动到菜单 */}
        {showHelpMenu && (
          <div className="absolute w-8 h-3 -right-2" onMouseEnter={() => setShowHelpMenu(true)} />
        )}

        {/* 悬停菜单 */}
        {showHelpMenu && (
          <div
            className="absolute -right-2 bottom-0 mb-8 bg-white rounded-lg shadow-md border border-gray-100 w-40 py-1 z-10"
            onMouseEnter={() => setShowHelpMenu(true)}
            onMouseLeave={() => setShowHelpMenu(false)}
          >
            <button
              className="w-full text-left px-3 py-2 text-xs hover:bg-gray-50 text-gray-700 flex items-center"
              onClick={handleCustomerService}
            >
              <HelpCircle className="h-3 w-3 mr-2 text-gray-500" />
              <span>Live Chat to Help</span>
            </button>
            <button
              className="w-full text-left px-3 py-2 text-xs hover:bg-gray-50 text-gray-700 flex items-center"
              onClick={handleTutorial}
            >
              <BookOpen className="h-3 w-3 mr-2 text-gray-500" />
              <span>View Tutorial</span>
            </button>
          </div>
        )}
      </div>
      <div className="p-1 text-[10px] text-gray-400/70">
          v{browser.runtime.getManifest().version}
        </div>
    </div>
  )
}
