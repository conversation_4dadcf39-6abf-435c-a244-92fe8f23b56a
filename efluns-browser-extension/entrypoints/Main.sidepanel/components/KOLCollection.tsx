import { Platform } from '@/@types/platform'
import Back from '@/assets/img/back.png'
import { MyImage } from '@/components/MyImage'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import useStorageState from '@/hooks/useStorageState'
import { getKOLService } from '@/services/KOLService'
import { getModalService } from '@/services/ModalService'
import { createAnalysisPost } from '@/utils/analysisPost'
import { currentEmailTemplateIdState, currentProjectIdState } from '@/utils/storages'
import { css } from '@emotion/react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useAtomValue, useSetAtom } from 'jotai'
import { ScreenShare } from 'lucide-react'
import { HTMLAttributes, useCallback } from 'react'
import toast from 'react-hot-toast'
import { kolCollector } from '../models/KOLCollector'
import { collectStatisticsProxyState, collectStatisticsState, inCollectModeState } from '../store'
import OverlayComponent from './OverlayComponent'

export interface KOLCollectionProps extends HTMLAttributes<HTMLDivElement> {}

const $event = createAnalysisPost('KOLCollection')

export default function KOLCollection({ ...props }: KOLCollectionProps) {
	const {
		data,
		index,
		collected: collectedKOLs,
		sentEmails,
		currentKOL,
		sourceKOL,
		sourceURL,
		isQueueUpdating,
		taskAvatarList,
		backUrl
	} = useAtomValue(collectStatisticsState)
	// const [activeUrl] = useStorageState(activeTabURLState)

	const kolsCount = kolCollector.queue.filteredData.length
	const inQueueKOLsCount = kolCollector.queue.filteredData.length - kolCollector.queue.filteredIndex
	const setInCollectMode = useSetAtom(inCollectModeState)
	const platform = getPlatform(kolCollector.queue.data[0]?.url)

	// const [isAutoSendEmailEnabled, setIsAutoSendEmailEnabled] = useAtom(isAutoSendEmailEnabledState)
	const [currentTemplateId, setCurrentTemplateId] = useStorageState(currentEmailTemplateIdState)

	const avatarAnimationStyles = css`
		.avatar-wave {
			animation: wave 1s ease-in-out infinite;
		}

		@keyframes wave {
			0%,
			100% {
				transform: translateY(0);
			}
			50% {
				transform: translateY(-5px);
			}
		}
	`

	// 获取平台信息的函数
	const getPlatformFromURL = useCallback((url: string): string => {
		if (!url) return 'INSTAGRAM' // 默认平台

		if (url.includes('instagram.com')) return 'INSTAGRAM'
		if (url.includes('tiktok.com')) return 'TIKTOK'
		if (url.includes('youtube.com') || url.includes('youtu.be')) return 'YOUTUBE'
		if (url.includes('twitter.com') || url.includes('x.com')) return 'TWITTER'
		if (url.includes('facebook.com')) return 'FACEBOOK'

		return 'INSTAGRAM' // 默认返回 instagram
	}, [])

	// 去 web 端 similar 的函数
	const goToWebSimilar = useCallback(() => {
		const platform = getPlatformFromURL(sourceURL || '')
		console.log(currentProjectIdState.getValue(), 'currentProjectIdState.getValue()')
		currentProjectIdState.getValue().then((pId) => {
			const url = `https://easykol.com/search/similar?platform=${platform}&projectId=${pId}`
			$event('goToWebSimilar', { platform, url })
			window.open(url, '_blank')
		})
	}, [sourceURL, getPlatformFromURL])

	const exportKOLs = useCallback(async (isStop:boolean)=>{
		const pId = await currentProjectIdState.getValue()
		await getKOLService().exportKOLs(pId!,isStop)
		if(isStop){
			await kolCollector.stop()
			setInCollectMode(false)
		}
	},[setInCollectMode])

	const stopClick = useCallback(() => {
		if (inQueueKOLsCount > 50) {
			getModalService().show({
				type: 'confirm',
				title: 'Are you sure?',
				content: `If you stop now, all pending tasks will be lost.
				To avoid losing important information, please download the data first.`,
				confirmText: 'Still Stop',
				onConfirm: () => {
					kolCollector.stop().then(() => {
						setInCollectMode(false)
					})
				}
			})
		} else {
			kolCollector.stop().then(() => {
				setInCollectMode(false)
			})
		}
	}, [inQueueKOLsCount, setInCollectMode])

	return (
		<main className="flex flex-col gap-8" css={css``}>
			<div className="flex flex-col gap-2 bg-white rounded-2xl px-4 py-2">
				<header className="flex justify-between items-center  bg-[#f6f5f4] px-4 py-2 rounded-2xl">
					<h1 className="text-2xl font-bold text-nowrap w-[70%] overflow-hidden">
						<div className="text-left font-[900] text-[1rem] text-black">Similar From</div>
						<div id="avatarList" className="flex -space-x-2 overflow-hidden py-[4px]" css={avatarAnimationStyles}>
							{(taskAvatarList.length
								? taskAvatarList
								: [
										{ url: '/icon/defaultAvatar.png', status: 'completed' },
										{ url: '/icon/defaultAvatar.png', status: 'completed' },
										{ url: '/icon/defaultAvatar.png', status: 'completed' },
										{ url: '/icon/defaultAvatar.png', status: 'completed' },
										{ url: '/icon/defaultAvatar.png', status: 'completed' }
									]
							)
								.slice(0, 5)
								.map((avatar, index) => (
									<MyImage
										key={avatar.url}
										src={avatar.url}
										className="h-[1.7rem] w-[1.7rem] rounded-full ring-2 ring-white"
										loadingClassName={avatar.status === 'processing' ? 'avatar-wave' : ''}
										loadedClassName={avatar.status === 'processing' ? 'avatar-wave' : ''}
										style={{ animationDelay: `${index * 0.1}s` }}
										alt={`头像 ${index + 1}`}
									/>
								))}
						</div>
					</h1>
					<div className="flex gap-2 items-center flex-wrap justify-end">
						<button
							className="btn btn-outline btn-error rounded-full w-[5rem] btn-sm text-[12px] py-1 px-2 min-h-[1.5rem] h-[1.5rem]"
							onClick={stopClick}
						>
							<FontAwesomeIcon icon="stop" />
							Stop
						</button>
						<button
							className="btn btn-outline rounded-full w-[5rem] btn-sm text-[12px] py-1 px-2 min-h-[1.5rem] h-[1.5rem]"
							onClick={() => {
								$event('excel')
									currentProjectIdState.getValue().then(async (projectId) => {
										if(inQueueKOLsCount > 10){
											getModalService().show({
												type: 'confirm',
												title: 'Download Excel?',
												content: `Will mark as Like and stop task to avoid repeats. Or just download without stopping.`,
												confirmText: 'Stop & Download',
												cancelText:"Download Only",
												onConfirm: () => {
													toast.promise(exportKOLs(true),{
														loading: 'Exporting...',
														success: 'Exported And Stopped successfully',
														error: (e) => e.message
													})

												},
												onCancel: () => {
													return toast.promise(exportKOLs(false),
													{
														loading: 'Exporting...',
														success: 'Exported successfully',
														error: (e) => e.message
													})
												}
											})
										}else{
											return toast.promise(exportKOLs(false),
											{
												loading: 'Exporting...',
												success: 'Exported successfully',
												error: (e) => e.message
											})
										}
									})
							}}
						>
							<FontAwesomeIcon icon="download" />
							Excel
						</button>
					</div>
				</header>
				<div className="stats w-full">
					<div className="stat place-items-center py-0 my-4">
						<div className="stat-title">Found</div>
						<div className="stat-value">{inQueueKOLsCount}</div>
						<div className="stat-desc">Total: {kolsCount}</div>
					</div>

					<div className="stat place-items-center py-0 my-4">
						<div className="stat-title">Liked</div>
						<div className="stat-value ">{collectedKOLs}</div>
						<div className="stat-desc ">Sent Emails: {sentEmails}</div>
					</div>
				</div>
			</div>

			{
				<div className="flex flex-col gap-4">
					{/* <AutoSendEmailToggleableAlert /> */}
					<div className="w-full max-w-sm">
						<div className="bg-white rounded-full h-10 flex items-center justify-between px-4">
							<span className="text-base font-normal text-gray-600">Bio Has Email</span>
							<label
								className="relative inline-flex items-center cursor-pointer"
								onClick={(e) => {
									// 检查是否是 YouTube 平台
									if ([Platform.YOUTUBE, Platform.TWITTER].includes(platform)) {
										e.preventDefault()

										getModalService().show({
											type: 'alert',
											content: `This feature is currently unavailable on ${platform === Platform.YOUTUBE ? 'YouTube' : 'X'}.`,
											confirmText: 'OK',
											onConfirm: () => {}
										})
										return
									}
								}}
							>
								<input
									onChange={(e) => {
										kolCollector.queue.bioHasEmailOnChange(e.target.checked)
									}}
									type="checkbox"
									className="sr-only peer"
									checked={kolCollector.queue.bioHasEmail}
									disabled={kolCollector.queue.data[0]?.url?.includes('youtube')}
								/>
								<div
									className={`relative w-11 h-6 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500 ${kolCollector.queue.data[0]?.url?.includes('youtube') ? 'bg-gray-300 cursor-not-allowed' : 'bg-gray-200'}`}
								/>
							</label>
						</div>
					</div>
				</div>
			}

			<section className="w-full flex flex-col gap-4 items-center">
				{backUrl && (
					<img
						src={Back}
						className="w-4 h-4 cursor-pointer absolute translate-y-[-150%]"
						alt=""
						onClick={() => {
							kolCollector.backLastTab()
						}}
					/>
				)}

				<button
					className="btn border-1 border-black bg-white  rounded-xl w-[60%] py-3 shadow-sm"
					onClick={() => {
						kolCollector.dislike()
					}}
				>
					👋 No
				</button>

				<button
					id="like"
					className="btn border-1 border-black bg-[#FFFBEB] hover:bg-[#FEF3C7] rounded-xl w-[60%] py-3 shadow-sm"
					onClick={async () => {
						kolCollector.like()

						if (currentTemplateId) {
							getKOLService()
								.sendEmail(kolCollector.queue.data[kolCollector.queue.index].id)
								.then(() => {
									collectStatisticsProxyState.sentEmails++
									$event('autoSendEmail', {
										type: 'like'
									})
								})
						}
					}}
				>
					✅ Like
				</button>

				<button
					id="superlike"
					className="btn border-1 border-black bg-[#fffbc6] hover:bg-[#FEF3C7] rounded-xl w-[80%] py-3 shadow-sm"
					onClick={async () => {
						kolCollector.superlike()

						if (currentTemplateId) {
							getKOLService()
								.sendEmail(kolCollector.queue.data[kolCollector.queue.index].id)
								.then(() => {
									collectStatisticsProxyState.sentEmails++
									$event('autoSendEmail', {
										type: 'superlike'
									})
								})
						}
					}}
				>
					✅ SuperLike ✅
				</button>

				<button
					className="text-black underline hover:no-underline cursor-pointer bg-transparent border-none flex items-center gap-1 text-sm"
					onClick={goToWebSimilar}
					title="Go to Web Similar Search"
				>
					<ScreenShare className="w-4 h-4" />
					Switch to Fast Mode
				</button>
			</section>
			{inQueueKOLsCount > 0 && <OverlayComponent kolCollector={kolCollector} />}
		</main>
	)
}
