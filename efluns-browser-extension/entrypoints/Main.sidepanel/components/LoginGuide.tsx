import { getAuthService } from '@/services/AuthService'
import { HTMLAttributes, useEffect, useState } from 'react'

export interface LoginGuideProps extends HTMLAttributes<HTMLDivElement> {}

export default function LoginGuide({ ...props }: LoginGuideProps) {
	const [isLoginTabOpen, setIsLoginTabOpen] = useState(false)

	useEffect(() => {
		const checkLoginTab = async () => {
			try {
				const tabs = await browser.tabs.query({ url: 'https://easykol.com/login*' })
				setIsLoginTabOpen(tabs.length > 0)
			} catch (error) {
				console.error('Error checking login tab:', error)
			}
		}

		// Initial check
		checkLoginTab()

		// Listen for tab updates
		const handleTabUpdate = (tabId: number, changeInfo: any) => {
			if (changeInfo.url || changeInfo.status === 'complete') {
				checkLoginTab()
			}
		}

		// Listen for tab removal
		const handleTabRemove = () => {
			checkLoginTab()
		}

		// Listen for tab creation
		const handleTabCreate = () => {
			checkLoginTab()
		}

		browser.tabs.onUpdated.addListener(handleTabUpdate)
		browser.tabs.onRemoved.addListener(handleTabRemove)
		browser.tabs.onCreated.addListener(handleTabCreate)

		return () => {
			browser.tabs.onUpdated.removeListener(handleTabUpdate)
			browser.tabs.onRemoved.removeListener(handleTabRemove)
			browser.tabs.onCreated.removeListener(handleTabCreate)
		}
	}, [])

	return (
		<div className="text-3xl h-screen flex flex-col items-center justify-center">
			{isLoginTabOpen ? (
				<div className="flex flex-col items-center">
					<div className="loading loading-spinner loading-lg" style={{ color: '#70a3f3' }} />
					<p className="mt-4 text-base text-gray-600">Logging in...</p>
				</div>
			) : (
				<button
					className="btn btn-primary rounded-full  w-[160px]"
					onClick={() => {
						getAuthService().openLoginPage()
					}}
				>
					Login to EasyKOL
				</button>
			)}
			{/* sync cookie */}
			{/* <button
				className="btn btn-ghost text-gray-400 mt-1"
				onClick={() => {
					getEasykolCookieService().requestCookiePermission()
				}}
			>
				Sync Cookie
			</button> */}
		</div>
	)
}
