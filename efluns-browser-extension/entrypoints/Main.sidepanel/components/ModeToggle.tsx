"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Spark<PERSON> } from "lucide-react"

interface ModeToggleProps {
  isVisionEnabled: boolean
  onToggle: (isVision?:boolean) => void
  disabled?: boolean
  normalDisabled?: boolean
  tooltipContent?: string
  normalDisabledText?: string
}

export function ModeToggle({
  isVisionEnabled,
  onToggle,
  disabled = false,
  normalDisabled = false,
  tooltipContent = "If you care a lot about what's in the video, like skin tone or gender, please turn on the visual model. But there will be fewer creators.",
  normalDisabledText = "Vision mode required for this operation",
}: ModeToggleProps) {
  const [showVisionTooltip, setShowVisionTooltip] = useState(false)
  const [showNormalTooltip, setShowNormalTooltip] = useState(false)
  const visionTooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const normalTooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Handle Vision tooltip display with delay
  const handleVisionTooltipMouseEnter = () => {
    if (visionTooltipTimeoutRef.current) {
      clearTimeout(visionTooltipTimeoutRef.current)
    }
    visionTooltipTimeoutRef.current = setTimeout(() => {
      setShowVisionTooltip(true)
    }, 300) // 0.3 second delay
  }

  const handleVisionTooltipMouseLeave = () => {
    if (visionTooltipTimeoutRef.current) {
      clearTimeout(visionTooltipTimeoutRef.current)
      visionTooltipTimeoutRef.current = null
    }
    setShowVisionTooltip(false)
  }

  // Handle Normal tooltip display with delay
  const handleNormalTooltipMouseEnter = () => {
    if (normalDisabled && !disabled) {
      if (normalTooltipTimeoutRef.current) {
        clearTimeout(normalTooltipTimeoutRef.current)
      }
      normalTooltipTimeoutRef.current = setTimeout(() => {
        setShowNormalTooltip(true)
      }, 300) // 0.3 second delay
    }
  }

  const handleNormalTooltipMouseLeave = () => {
    if (normalTooltipTimeoutRef.current) {
      clearTimeout(normalTooltipTimeoutRef.current)
      normalTooltipTimeoutRef.current = null
    }
    setShowNormalTooltip(false)
  }

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      if (visionTooltipTimeoutRef.current) {
        clearTimeout(visionTooltipTimeoutRef.current)
      }
      if (normalTooltipTimeoutRef.current) {
        clearTimeout(normalTooltipTimeoutRef.current)
      }
    }
  }, [])

  return (
    <div className="relative">
      <div className="bg-[#e9e9e9] rounded-full flex items-center justify-between h-12 p-1 relative">
        {/* Normal Mode Disabled Tooltip */}
        <AnimatePresence>
          {showNormalTooltip && !disabled && (
            <motion.div
              className="absolute z-20 px-4 py-3 bg-[#1c1c1c] text-white text-sm font-medium rounded-lg shadow-lg top-full left-0 right-0 mt-2 w-full"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center">
                <Sparkles className="h-3.5 w-3.5 mr-2 text-sky-400" />
                {normalDisabledText}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Vision Mode Tooltip */}
        <AnimatePresence>
          {showVisionTooltip && !disabled && !showNormalTooltip && (
            <motion.div
              className="absolute z-20 px-4 py-3 bg-[#1c1c1c] text-white text-sm font-medium rounded-lg shadow-lg top-full left-0 right-0 mt-2 w-full"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              transition={{ duration: 0.2 }}
            >
              {tooltipContent}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Normal Button - Wrapped in a div to capture events when disabled */}
        <div
          className="flex-1 relative"
          onMouseEnter={handleNormalTooltipMouseEnter}
          onMouseLeave={handleNormalTooltipMouseLeave}
        >
          <button
            className={`w-full py-2 text-sm font-medium rounded-full transition-colors focus:outline-none ${
              !isVisionEnabled && !normalDisabled
                ? "bg-white text-black shadow-sm"
                : normalDisabled
                  ? "bg-transparent text-gray-400 cursor-not-allowed opacity-50"
                  : "bg-transparent text-gray-500 hover:bg-gray-200"
            } ${disabled ? "opacity-70 pointer-events-none" : ""}`}
            onClick={() => !normalDisabled && onToggle(false)}
            disabled={disabled || normalDisabled}
          >
            Normal
          </button>
          {normalDisabled && !disabled && <div className="absolute inset-0 cursor-help" />}
        </div>

        <div className="relative flex-1">
          <motion.button
            className={`w-full py-2 text-sm font-medium rounded-full transition-colors focus:outline-none flex items-center justify-center ${
              isVisionEnabled
                ? "bg-gradient-to-r from-sky-300 via-blue-200 to-sky-300 text-black shadow-sm relative overflow-hidden"
                : "bg-transparent text-gray-500 hover:bg-gray-200"
            } ${disabled ? "opacity-70 pointer-events-none" : ""}`}
            onClick={()=>onToggle(true)}
            onMouseEnter={handleVisionTooltipMouseEnter}
            onMouseLeave={handleVisionTooltipMouseLeave}
            disabled={disabled}
            whileHover={!disabled ? { scale: 1.02 } : {}}
            whileTap={!disabled ? { scale: 0.98 } : {}}
          >
            {isVisionEnabled && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center"
              >
                <Sparkles className="h-3.5 w-3.5 mr-1 text-sky-600 animate-pulse" />
              </motion.div>
            )}
            AI
          </motion.button>

          {isVisionEnabled && (
            <motion.div
              className="absolute inset-0 rounded-full"
              initial={{ opacity: 0 }}
              animate={{
                opacity: [0.5, 0.8, 0.5],
                backgroundPosition: ["0% 0%", "100% 0%", "0% 0%"],
              }}
              transition={{
                duration: 2,
                repeat: Number.POSITIVE_INFINITY,
                ease: "linear",
              }}
              style={{
                background:
                  "linear-gradient(90deg, rgba(56,189,248,0) 0%, rgba(56,189,248,0.3) 50%, rgba(56,189,248,0) 100%)",
                backgroundSize: "200% 100%",
                pointerEvents: "none",
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
}
