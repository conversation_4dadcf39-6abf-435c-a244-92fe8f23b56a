import { getEasykolCookieService } from "@/services/EasykolCookieService"
import clsx from "clsx"
import { Mail, Search, Hash, UserPlus, Layers, LineChart } from "lucide-react"
import AccountDropdown from "./AccountDropdown"
import { useAtom } from "jotai"
import { routerState } from "../store"

interface NarrowSidebarProps {
  className?: string
}

const routerList = [
  { path: 'find-similars', label: 'Similar',id: "similar" },
  { path: 'establish-contact', label: 'Contact',id: "email" },
]

const urlList = [
  {
    ids:["search","tags","follow"],
    url:"https://easykol.com/search",
  },
  {
    ids:["track"],
    url:"https://easykol.com/dataManagement/easykolTrack",
  }
]

export function NarrowSidebar({ className }: NarrowSidebarProps) {
  const [router,setRouter] = useAtom(routerState)
  // const navigate = useNavigate()
  // const location = useLocation()
  const activeButton = routerList.find(item => item.path === router.router)?.id
  // 确保"similar"是默认选中的状态



  const handleButtonClick = async (buttonName: string) => {
    const router = routerList.find(item => item.id === buttonName)
    if(router){
     return setRouter({router:router.path as "find-similars" | "establish-contact"})
    }

		await getEasykolCookieService().requestCookiePermission()


    const item = urlList.find(item => item.ids.includes(buttonName))
    if(item){
      return window.open(item.url, "_blank")
    }
  }

  return (
    <div className={clsx("flex flex-col h-screen bg-white border-l border-gray-100 text-gray-700 w-[45px]", className)}>
      <div className="flex flex-col items-center gap-4 py-5 flex-1">
        {/* Similar - 默认选中状态 */}
        <div className="flex flex-col items-center cursor-pointer" onClick={() => handleButtonClick("similar")}>
          <button
            className={clsx(
              "h-8 w-8 rounded-full p-2 flex items-center justify-center transition-colors",
              activeButton === "similar"
                ? "bg-black text-white"
                : "bg-transparent text-gray-500 hover:bg-gray-100 hover:text-gray-900",
            )}
          >
            <Layers className="h-4 w-4" />
          </button>
          <span
            className={clsx("text-[9px] mt-0.5 font-normal", activeButton === "similar" ? "text-black" : "text-gray-600")}
          >
            Similar
          </span>
        </div>

        {/* Email */}
        <div className="flex flex-col items-center cursor-pointer" onClick={() => handleButtonClick("email")}>
          <button
            className={clsx(
              "h-8 w-8 rounded-full p-2 flex items-center justify-center transition-colors",
              activeButton === "email"
                ? "bg-black text-white"
                : "bg-transparent text-gray-500 hover:bg-gray-100 hover:text-gray-900",
            )}
          >
            <Mail className="h-4 w-4" />
          </button>
          <span
            className={clsx("text-[9px] mt-0.5 font-normal", activeButton === "email" ? "text-black" : "text-gray-600")}
          >
            Email
          </span>
        </div>

        {/* Search */}
        <div className="flex flex-col items-center cursor-pointer" onClick={() => handleButtonClick("search")}>
          <button
            className={clsx(
              "h-8 w-8 rounded-full p-2 flex items-center justify-center transition-colors",
              activeButton === "search"
                ? "bg-black text-white"
                : "bg-transparent text-gray-500 hover:bg-gray-100 hover:text-gray-900",
            )}
            
          >
            <Search className="h-4 w-4" />
          </button>
          <span
            className={clsx("text-[9px] mt-0.5 font-normal", activeButton === "search" ? "text-black" : "text-gray-600")}
          >
            Search
          </span>
        </div>

        {/* Tags */}
        <div className="flex flex-col items-center cursor-pointer"  onClick={() => handleButtonClick("tags")}>
          <button
            className={clsx(
              "h-8 w-8 rounded-full p-2 flex items-center justify-center transition-colors",
              activeButton === "tags"
                ? "bg-black text-white"
                : "bg-transparent text-gray-500 hover:bg-gray-100 hover:text-gray-900",
            )}
           
          >
            <Hash className="h-4 w-4" />
          </button>
          <span
            className={clsx("text-[9px] mt-0.5 font-normal", activeButton === "tags" ? "text-black" : "text-gray-600")}
          >
            Tags
          </span>
        </div>

        {/* Follow */}
        <div className="flex flex-col items-center cursor-pointer" onClick={() => handleButtonClick("follow")}>
          <button
            className={clsx(
              "h-8 w-8 rounded-full p-2 flex items-center justify-center transition-colors",
              activeButton === "follow"
                ? "bg-black text-white"
                : "bg-transparent text-gray-500 hover:bg-gray-100 hover:text-gray-900",
            )}
            
          >
            <UserPlus className="h-4 w-4" />
          </button>
          <span
            className={clsx("text-[9px] mt-0.5 font-normal", activeButton === "follow" ? "text-black" : "text-gray-600")}
          >
            Follow
          </span>
        </div>

        {/* Track */}
        <div className="flex flex-col items-center cursor-pointer" onClick={() => handleButtonClick("track")}>
          <button
            className={clsx(
              "h-8 w-8 rounded-full p-2 flex items-center justify-center transition-colors",
              activeButton === "track"
                ? "bg-black text-white"
                : "bg-transparent text-gray-500 hover:bg-gray-100 hover:text-gray-900",
            )}
          >
            <LineChart className="h-4 w-4" />
          </button>
          <span
            className={clsx("text-[9px] mt-0.5 font-normal", activeButton === "track" ? "text-black" : "text-gray-600")}
          >
            Track
          </span>
        </div>
      </div>

      {/* Bottom buttons */}
      <div className="flex flex-col items-center gap-4 mb-4">
        {/* Account */}
        <div className="flex flex-col items-center">
        <AccountDropdown />
        </div>
      </div>
    </div>
  )
}
