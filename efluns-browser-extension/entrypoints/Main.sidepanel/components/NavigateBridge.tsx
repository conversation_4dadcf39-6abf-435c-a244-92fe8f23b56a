import { use<PERSON>tom, useSet<PERSON>tom } from 'jotai'
import { HTMLAttributes, useEffect } from 'react'
import { routerState } from '../store'

export interface NavigateBridgeProps extends HTMLAttributes<HTMLDivElement> {}

export default function NavigateBridge({ ...props }: NavigateBridgeProps) {
	const setRouter= useSetAtom(routerState)

	useEffect(() => {
		const unsubs = onMessage('navigateTo', (msg) => {
			setRouter({router:msg.data as "find-similars" | "establish-contact"})
		})

		return unsubs
	}, [setRouter])

	return null
}
