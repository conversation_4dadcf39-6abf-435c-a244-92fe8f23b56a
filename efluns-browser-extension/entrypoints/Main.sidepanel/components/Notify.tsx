import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function Notify() {
    const [notifies] = useStorageState(notifiesState)
    const [currentIndex, setCurrentIndex] = useState(0);
    const validNotifies = useMemo(() => notifies.filter(notify => notify.valid && notify.position === 'SIDEPANEL'), [notifies]);

    useEffect(() => {
        if (validNotifies.length === 0) {
            return
        }
        const timer = setInterval(() => {
            setCurrentIndex((prevIndex) => 
                prevIndex === validNotifies.length - 1 ? 0 : prevIndex + 1
            );
        }, 5000);

        return () => clearInterval(timer);
    }, [validNotifies.length]);

    return (
        <div className="relative h-[100px] overflow-hidden">
            <AnimatePresence mode="wait">
                {validNotifies.length > 0 ? (
                    <motion.div 
                        key={currentIndex}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.5 }}
                        className="p-3 py-1 bg-white/90 backdrop-blur-sm rounded-lg 
                        shadow-sm absolute w-full
                        border border-red-200
                        hover:shadow-md hover:border-gray-300
                        transition-all duration-200"
                        dangerouslySetInnerHTML={{ 
                            __html: validNotifies[currentIndex]?.content || ''
                        }}
                    />
                ) : (
                    <div className="h-[100px]" />
                )}
            </AnimatePresence>
        </div>
    );
}