import KOLCollector from '@/entrypoints/Main.sidepanel/models/KOLCollector';
import { inCollectModeState, inAISearchCollectModeState } from '@/entrypoints/Main.sidepanel/store';
import { getCommonService } from '@/services/CommonService';
import { useAtom } from 'jotai';
import React from 'react';
import { Tabs } from 'wxt/browser';
import AISearchKOLCollector from '../models/AISearchKOLCollector';

interface OverlayComponentProps {
    kolCollector: KOLCollector | AISearchKOLCollector
}

const OverlayComponent: React.FC<OverlayComponentProps> = ({kolCollector}) => {
    const [isOnValidTab, setIsOnValidTab] = useState(false);
    const [inCollectMode, setInCollectMode] = useAtom(inCollectModeState)
    const [activeTabUrl] = useStorageState(activeTabURLState)

    useEffect(()=>{
        if(activeTabUrl){
            const isValid =  kolCollector.queue.isCurrentTabValid(activeTabUrl);
            setIsOnValidTab(isValid);
        }
    },[activeTabUrl, kolCollector.queue])

    const handleContinue =  () => {
         kolCollector.queue.switchToValidTab();
         setIsOnValidTab(true)
    };

    // useEffect(() => {
    //     const checkTabValidity =  async(tabId: number) => {
    //         await new Promise(resolve => setTimeout(resolve, 500));
    //         const currentTab = await browser.tabs.get(tabId)
    //         if(!currentTab.active) return
    //         const isValid =  kolCollector.queue.isCurrentTabValid(currentTab?.url || currentTab?.pendingUrl || "");
    //         setIsOnValidTab(isValid);
    //     };
    

    //     const tabActivatedListener =  (activeInfo:Tabs.OnActivatedActiveInfoType) => {
    //          checkTabValidity(activeInfo.tabId);
    //     };
    
    //     browser.tabs.onActivated.addListener(tabActivatedListener);
    
    //     return () => {
    //         browser.tabs.onActivated.removeListener(tabActivatedListener);
    //     };
    // }, [kolCollector]);

    // useEffect(()=>{
    //     getCommonService().getActiveTabURL().then(url=>{
    //         setIsOnValidTab(kolCollector.queue.isCurrentTabValid(url || ""))
    //     })
        
    // },[ kolCollector.queue, kolCollector.queue.index])


    return (
        isOnValidTab ? null : <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[999]">
            <dialog className="modal" open={true}>
                <div className="modal-box w-80">
                    <h3 className="font-bold text-lg">Workflow Paused</h3>
                    <p className="py-4">Please close tabs unrelated to similarity search during the filtering process</p>
                    <div className="modal-action">
                        <form method="dialog" className="flex gap-2">
                            <button 
                                className="btn btn-primary btn-sm rounded-full" 
                                onClick={handleContinue}
                            >
                                Continue
                            </button>
                        </form>
                    </div>
                </div>
            </dialog>
        </div>
    );
};


export default OverlayComponent;