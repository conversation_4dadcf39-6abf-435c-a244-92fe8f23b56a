import { CreateProjectPayload } from '@/@types/project'
import clsx from 'clsx'
import { HTMLAttributes } from 'react'

const log = makeModuleLog('ProjectModal')

export interface ProjectModalProps extends Omit<HTMLAttributes<HTMLDialogElement>, 'onSubmit'> {
	onSubmit?: (data: CreateProjectPayload) => Promise<void>
}

export default function ProjectModal({ className, onSubmit, ...props }: ProjectModalProps) {
	const ref = useRef<HTMLDialogElement | null>(null)

	const handleClose = useCallback(() => {
		ref.current?.close()
	}, [])

	return (
		<dialog className={clsx('modal', className)} ref={ref} {...props}>
			<div className="modal-box w-[90%]">
				<form
					className="h-full grid "
					css={css`
						grid-template-rows: auto 1fr auto;
						overflow: hidden;
					`}
					onSubmit={(e) => {
						e.preventDefault()
						const formData = new FormData(e.target as HTMLFormElement)
						const data: CreateProjectPayload = {
							title: formData.get('title')?.toString() ?? '',
							description: formData.get('description')?.toString() ?? ''
						}
						onSubmit?.(data).then(() => {
							handleClose()
						})
					}}
				>
					<h3 className="font-bold text-xl px-4 mb-1">新建项目</h3>
					<div
						className="space-y-4 p-4 grid overflow-hidden"
						css={css`
							grid-template-rows: auto auto 1fr;
						`}
					>
						<label className="block">
							<input
								type="text"
								className="input input-sm input-bordered  block w-full"
								placeholder="Project Name"
								name="title"
							/>
						</label>
						<label className="block">
							<input
								type="text"
								className="input input-sm input-bordered  block w-full"
								placeholder="Description"
								name="description"
							/>
						</label>
					</div>

					<div className="modal-action mt-auto px-4 gap-2">
						{/* if there is a button in form, it will close the modal */}
						<button type="button" className="btn btn-sm rounded-full" onClick={handleClose}>
							Close
						</button>
						<button type="submit" className="btn btn-sm btn-primary  rounded-full ">
							Submit
						</button>
					</div>
				</form>
			</div>
		</dialog>
	)
}
