import { CreateProjectPayload, Project } from '@/@types/project'
import { PROJECTS } from '@/constants'
import { getProjectService } from '@/services/ProjectService'
import { post } from '@/utils/request'
import { currentProjectIdState } from '@/utils/storages'
import { IconProp } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import clsx from 'clsx'
import { HTMLAttributes, useCallback } from 'react'
import ProjectModal from './ProjectModal'
import toast from 'react-hot-toast'
import { kolCollector } from '../models/KOLCollector'
import { getModalService } from '@/services/ModalService'
import { useSetAtom } from 'jotai'
import { inCollectModeState } from '../store'
import { getKOLService } from '@/services/KOLService'

const log = makeModuleLog('ProjectSelect')
const $event = createAnalysisPost("ProjectSelect")

const projectService = getProjectService()
export interface ProjectSelectProps extends HTMLAttributes<HTMLDivElement> {}

export default function ProjectSelect({ ...props }: ProjectSelectProps) {
	const [value, setValue] = useStorageState(currentProjectIdState)
	const [data] = useStorageState(projectsState)
	const setInCollectModeState = useSetAtom(inCollectModeState)
	const selectedProject = data.find((item) => item.id === value)

	const handleProjectFormModalSubmit = useCallback(async (payload: CreateProjectPayload) => {
		if(!payload.title) {
			 toast.error("Title required")
			 return
		}
		const data:{id:string} = await post(PROJECTS, payload).then(res=>res.data)
		await getProjectService().refreshProjects()
		setValue(data.id)
		$event("createProject",{
			projectId:data.id
		})
	}, [setValue])

	const handleDeleteProject = useCallback(async (item: Project) => {
		getModalService().show({
		  type: 'confirm',
		  content: `Are you sure to delete project "${item.title}"?`,
		  async onConfirm() {
			toast.promise(projectService.deleteProject(item.id).then(() => {
			  getProjectService().refreshProjects()
			  $event("deleteProject",{
				projectId:item.id
			  })
			}), {
			  success: 'Deleted successfully',
			  error: 'Delete failed',
			  loading: 'Deleting'
			})
		  }
		})
	  }, [])

	const handleResetProject = useCallback(() => {
		getModalService().show({
		  type: 'confirm',
		  content: "Default Project can't be deleted, will be reset. Continue?",
		  async onConfirm() {
			toast.promise(projectService.resetProject(data[0]!.id).then(async() => {
				log(await currentProjectIdState.getValue())
				log(data[0]!.id)
				$event("reseDefaultProject",{
					projectId:data[0]!.id
				})
			  if((await currentProjectIdState.getValue()) === data[0]!.id){
				kolCollector.queue.cleanupTabs().then(()=>{
					setInCollectModeState(false)
				})
			  }
			  getProjectService().refreshProjects()
			}), {
			  success: 'Reset successful',
			  error: 'Reset failed',
			  loading: 'Resetting'
			})
		  }
		})
	  }, [data, setInCollectModeState])


	useEffect(() => {
		projectService.refreshProjects().catch(log)
	}, [])

	const exportKOLs = useCallback(async (projectId: Project['id']) => {
		$event("exportKOLs",{
			projectId
		})
		toast.promise(getKOLService().exportKOLs(projectId ?? ''), {
			success: 'Export Successful',
			error: (e) => e.message,
			loading: 'Exporting'
		})
	}, [])



	return (
		<div className='flex items-center gap-1'>

				<button onClick={(e) =>{e.stopPropagation(); exportKOLs(guard(selectedProject?.id,"Project id获取失败"))}}>
						<FontAwesomeIcon icon="download" />
					</button>

				<div className="dropdown dropdown-bottom  h-fit">
				<div tabIndex={0} role="button" className="btn btn-ghost btn-sm !text-[1rem] rounded-full flex-nowrap text-nowrap w-full">
					<span className="overflow-hidden overflow-ellipsis max-w-[120px]">{selectedProject?.title}</span>
					<FontAwesomeIcon icon={'fa-solid fa-angle-down' as IconProp} className="-mt-1" />
				</div>
				<ul
					tabIndex={0}
					key={selectedProject?.id}
					className="dropdown-content menu glass rounded-box z-[1] p-2 w-[200px] shadow-xl flex-nowrap translate-x-[-30px]"
				>
					<div
						className="max-h-[200px] overflow-hidden overflow-y-auto "
						css={css`
							&::-webkit-scrollbar {
								display: none;
							}
						`}
					>
						{data.map((item,index) => (
							<li
								key={item.id}
								value={item.id}
								onClick={(e) => {
									setValue(item.id)
								}}
								className="w-full group"
							>
								<div className={clsx('flex justify-between items-center w-full px-2', {
									'!bg-[#2b3440] !text-white': selectedProject?.id === item.id
								})}>
									<a
										className='text-nowrap text-ellipsis overflow-hidden flex-1'
									>
										{item.title}
									</a>

										<button
											className="btn btn-ghost btn-xs border invisible group-hover:visible h-4 leading-4 px-1"
											onClick={(e) => {
												e.stopPropagation()
												if(index === 0){
													handleResetProject()
													return 
												}
												handleDeleteProject(item)
											}}
										>
											<FontAwesomeIcon 
												icon={'fa-solid fa-xmark' as IconProp} 
												className="text-[#fff] w-4 h-4  border rounded-full"
											/>
										</button>
								</div>
							</li>
						))}
					</div>
					<div className="divider my-0" css={css``} />
					<li>
						<a
							className="btn btn-sm btn-ghost flex items-center flex-nowrap text-nowrap"
							onClick={(e) => {
								e.preventDefault()
								e.stopPropagation()
								const projectModal = document.getElementById('project-modal') as HTMLDialogElement | null
								projectModal?.showModal()
							}}
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-4 w-4 rotate-45"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
							</svg>
							New Project
						</a>
					</li>
				</ul>
			</div>
			<ProjectModal id="project-modal" onSubmit={handleProjectFormModalSubmit} />
		</div>
	)
}
