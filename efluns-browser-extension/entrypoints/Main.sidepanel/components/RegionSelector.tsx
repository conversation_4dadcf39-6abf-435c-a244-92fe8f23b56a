import React from 'react';
import { useFloating, useInteractions, useClick, useRole, useDismiss, FloatingFocusManager } from '@floating-ui/react';
import { faTimesCircle } from '@fortawesome/free-regular-svg-icons';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { REGIONS } from '@/constants/region';
import { getCountryFlag } from '@/utils/tiktok/getCountryIcon';
import { isInChina } from '@/utils/area';

type ContinentItem = typeof REGIONS[number];
type RegionItem = ContinentItem['regions'][number];

const TEXT_ALL_EN = "Global";
const TEXT_ALL_ZH = "全球";

interface RegionSelectorProps {
  selectedRegions: string[];
  onRegionChange: (regions: string[]) => void;
  disabled?: boolean;
}

export function RegionSelector({ 
  selectedRegions, 
  onRegionChange, 
  disabled = false 
}: RegionSelectorProps) {
  console.log('RegionSelector render:', { selectedRegions }) // 添加日志

  const [isOpen, setIsOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [memoryRegions] = useStorageState(memorySelectRegionState);
  const inChina = isInChina(); // 是否在中国

  const TEXT_ALL = inChina ? TEXT_ALL_ZH : TEXT_ALL_EN;

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context),
    useRole(context),
    useDismiss(context),
  ]);

  const handleRegionChange = (regionValue: string) => {
    const newSelectedRegions = selectedRegions.includes(regionValue)
      ? selectedRegions.filter(r => r !== regionValue)
      : [...selectedRegions, regionValue];
    
    onRegionChange(newSelectedRegions);
  };

  const getSelectedRegionsText = (): string => {
    if (!selectedRegions || selectedRegions.length === 0) return TEXT_ALL;
    
    const allRegions = REGIONS.flatMap((continent: ContinentItem) => 
      [...continent.regions]
    );
    
    const firstRegion = allRegions.find((region: RegionItem) => 
      region.value === selectedRegions[0]
    );
    
    if (!firstRegion) return TEXT_ALL;

    const label = inChina ? firstRegion.zh_label : firstRegion.label;
    
    return selectedRegions.length === 1 
      ? label 
      : `${label} +${selectedRegions.length - 1}`;
  };

  const handleClearRegions = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRegionChange([]);
  };

  const filteredRegions = React.useMemo(() => {
    if (!searchTerm) return REGIONS;
    
    const normalizedSearch = searchTerm.toLowerCase();
    
    return REGIONS.map(continent => ({
      ...continent,
      regions: continent.regions.filter(region =>
        region.label.toLowerCase().includes(normalizedSearch) ||
        (inChina && region.zh_label.toLowerCase().includes(normalizedSearch))
      )
    })).filter(continent => continent.regions.length > 0);
  }, [searchTerm, inChina]);

  const allRegionsCount = React.useMemo(() => 
    REGIONS.reduce((acc, continent) => acc + continent.regions.length, 0)
  , []);

  return (
    <div className="relative group">

      <button
        ref={refs.setReference}
        {...getReferenceProps()}
        className={`w-full p-2 border rounded-lg flex justify-between items-center bg-white ${disabled ? 'text-gray-500' : ''}`}
        disabled={disabled}
      >
        <span>{getSelectedRegionsText()}</span>
        <div className="flex items-center gap-2">
          {selectedRegions.length > 0 ? (
            <span 
              onClick={handleClearRegions}
              className="invisible group-hover:visible text-gray-400 hover:text-gray-600 cursor-pointer"
            >
              <FontAwesomeIcon icon={faTimesCircle} className="w-4 h-4" />
            </span>
          ) : (
            <FontAwesomeIcon 
              icon={faChevronDown} 
              className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            />
          )}
        </div>
      </button>
      
      {isOpen && (
        <FloatingFocusManager context={context} modal={false}>
          <div
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
            className="z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-80 overflow-y-auto"
          >
            <div className="sticky top-0 bg-white p-2 border-b">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={inChina ? "搜索国家..." : "Search country..."}
                className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                onClick={(e) => e.stopPropagation()}
              />
            </div>

            <div className="px-2 border-b flex justify-between items-center">

            <label
                className="flex items-center p-2 hover:bg-gray-100 cursor-pointer rounded-lg"
                onClick={() => {
                  const allRegionValues = REGIONS.reduce((acc, continent) => 
                    [...acc, ...continent.regions.map(r => r.value)], [] as string[]);
                  onRegionChange(selectedRegions.length === allRegionsCount ? [] : allRegionValues);
                }}
              >
                <span className="text-primary">{inChina ? '全选' : 'All'}</span>
              </label>
              
                <label
                  className="flex items-center p-2 hover:bg-gray-100 cursor-pointer rounded-lg"
                  onClick={() => {
                    if (memoryRegions) {
                      onRegionChange(memoryRegions);
                      setIsOpen(false);
                    }
                  }}
                >
                  <span className="text-primary">{inChina ? '上次选择' : 'Last Select'}</span>
                </label>
              
            </div>
            
            {filteredRegions.map((continent) => (
              <div key={continent.label} className="p-2">
                <div className="text-gray-500 text-sm px-2">{inChina ? continent.zh_label : continent.label}</div>
                {continent.regions.map((region) => (
                  <label
                    key={region.value}
                    className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      className="checkbox checkbox-primary checkbox-sm mr-2"
                      checked={selectedRegions.includes(region.value)}
                      onChange={() => handleRegionChange(region.value)}
                    />
                    <span>{inChina ? region.zh_label : region.label} {getCountryFlag(region.value.slice(0,2))}</span>
                  </label>
                ))}
              </div>
            ))}
          </div>
        </FloatingFocusManager>
      )}
    </div>
  );
} 