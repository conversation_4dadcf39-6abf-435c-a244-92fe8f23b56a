import JotaiDevTools from '@/components/JotaiDevTools'
import createCache from '@emotion/cache'
import { CacheProvider, Global } from '@emotion/react'
import { library } from '@fortawesome/fontawesome-svg-core'
import { far } from '@fortawesome/free-regular-svg-icons'
import { fas } from '@fortawesome/free-solid-svg-icons'
import { QueryClientProvider } from '@tanstack/react-query'
import { createRoot } from 'react-dom/client'
import toast, { Toaster } from 'react-hot-toast'
import '~/assets/index.css'
import Root from './Router.new'
import { getProjectService } from '@/services/ProjectService'
import { createAnalysisPost } from '@/utils/analysisPost'
import { showErrModal } from './utils/showErrModal'
import { generateFingerprint } from '@/utils/generateFingerPrint'
import { IGNORE_ERRORS } from '@/constants/errors'
import { initSentry } from '../KOLInfoCard.content/utils/sentry'
import { getCommonService } from '@/services/CommonService'

initSentry()
library.add(fas, far)
const analysisPost = createAnalysisPost("SidePanel")
analysisPost("open")

generateFingerprint().then((browserId)=>{
	reportUserId()//上报浏览器指纹
})

export const styleCache = createCache({
	key: 'efluns'
})
styleCache.compat = true

const container = document.getElementById('root')
const root = createRoot(container!)
const originalToast = { ...toast }

//@ts-expect-error TODO，修改toast.error,待测试
toast.error = (message: string, ...args: any[]) => {
	if(!IGNORE_ERRORS.includes(message)){
		showErrModal(message)
	}
	originalToast.dismiss()
  }


const setEnv = async (env: string) => {
	browser.storage.local.set({ env })
	await getProjectService().refreshProjects()
	browser.runtime.reload()
}
//TODO 用来设置模式的，隐藏功能
// @ts-expect-error 隐藏功能
globalThis.setEnv = setEnv

//轮询获取通知
getCommonService().pollNotifies()

Object.defineProperty(globalThis, 'beta', {
	get: async () => {
		setEnv('beta')
	}
})

Object.defineProperty(globalThis, 'prod', {
	get: async () => {
		setEnv('')
	}
})

Object.defineProperty(globalThis, 'now', {
	get: async () => {
		const { env } = await browser.storage.local.get({ env: '' })
		console.log("当前环境:", env?env:'正式环境');
	}
})

root.render(
	<CacheProvider value={styleCache}>
		<QueryClientProvider client={queryClient}>
			<Global
				styles={css`
					:root {
						--navbar-padding: 1rem;
						--page-container-padding: 0.5rem 1rem;
					}
					body {
						font-size: initial;
					}
					::-webkit-scrollbar {
						display: none; 
					}
				`}
			/>
					<Root />
			<Toaster />
			<JotaiDevTools />
		</QueryClientProvider>
	</CacheProvider>
)
