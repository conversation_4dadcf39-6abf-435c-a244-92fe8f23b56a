import { InQueueKOL } from '@/@types/kol'
import { QUERY_PROJECT_LIKES } from '@/constants'
import { get } from '@/utils/request'
import { getKOLService } from '../../../services/KOLService'
import { aiSearchCollectStatisticsProxyState, collectStatisticsProxyState, DEFAULT_COLLET_STATISTICS, DEFAULT_FILTER_OPTIONS, DEFAULT_SEARCH_FORM, filtersProxyState, searchFormProxyState } from '../store'
import Similar from './Similar'
import { getProjectService } from '@/services/ProjectService'
import { Platform } from '@/@types/platform'
import AISearchKOLQueue from './AISearchKOLQueue'
import { cloneDeep } from 'lodash-es'

const log = makeModuleLog('AISearchKOLCollector')
const $event = createAnalysisPost("AISearchKOLCollector")

export interface AISearchKOLCollectorStartOptions<TDataItem> {
	data: TDataItem[]
	onLoadMore?: () => Promise<TDataItem[]>
}
export default class AISearchKOLCollector {
	queue: AISearchKOLQueue = withProxy(new AISearchKOLQueue())
	collectorType: "KOL" | "AI" = "AI"

	constructor() {
		// this.queue.setOnAvatarUpdate((avatarUrl) => {
		// 	this.updateTaskAvatarList(avatarUrl)
		// })
	}

	async start({ data, onLoadMore }: AISearchKOLCollectorStartOptions<InQueueKOL>) {
		await this.initializeCollectedList()
		await this.queue.init(data, onLoadMore)
	}

	async stop() {
		await Similar.cancelAllTasks()
		getProjectService().terminateProject("KEYWORD")
		isWorkingState.setValue(false)
		Object.assign(aiSearchCollectStatisticsProxyState, cloneDeep(DEFAULT_COLLET_STATISTICS))
		Object.assign(searchFormProxyState,cloneDeep(DEFAULT_SEARCH_FORM))
		await this.reset()
		await this.queue.cleanupTabs()

	}

	async reset(){
		await Similar.cancelAllTasks()
		await this.queue.reset()
	}

	// updateTaskAvatarList(url:string){
	// 	aiSearchCollectStatisticsProxyState.taskAvatarList.unshift(url)
	// 	if(aiSearchCollectStatisticsProxyState.taskAvatarList.length>5){
	// 		aiSearchCollectStatisticsProxyState.taskAvatarList.pop()
	// 	}
	// }


	private async getMarkSimilarPayload() {
		if(this.queue.index >= this.queue.data.length){
			let message = "No more items. Please click 'Stop' and choose another channel to start."

			if(this.queue.isQueueUpdating()){
				message = 'Please wait 1 min, system analyzing'
			}
			throw new Error(message)
		}
		const kol = guard(this.queue.current, 'Current KOL')
		const projectId = guard(await currentProjectIdState.getValue(), 'Project ID')

		return {
			projectId,
			kolId: kol.id
		}
	}

	private async getProjectLikeCount(){
		const projectId = guard(await currentProjectIdState.getValue(), 'Project ID')
		return await get(QUERY_PROJECT_LIKES, {
			params: {
				projectId
			}
		})
	}

	private async initializeCollectedList(){
		const { data:collectedList } = await this.getProjectLikeCount().then((res) => res.data)
		aiSearchCollectStatisticsProxyState.collected = collectedList.length;
	}


	async like() {
		const payload = await this.getMarkSimilarPayload()
		Similar.rateSimilar({
			...payload,
			attitude:"LIKE"
		}).then(() => {
			aiSearchCollectStatisticsProxyState.collected++
			$event("like",{
				...payload,
				kol:this.queue.current
			})
		})
		
		const bool = this.queue.current!.platform !== Platform.TIKTOK
		await this.queue.showNext()
	}

	async dislike() {
		const payload = await this.getMarkSimilarPayload()
		Similar.rateSimilar({
			...payload,
			attitude:"DISLIKE"
		})
		$event("dislike",{
			...payload,
			kol:this.queue.current
		})
	   await this.queue.showNext()
	}

	async superlike() {
		const payload = await this.getMarkSimilarPayload()
		 Similar.rateSimilar({
			...payload,
			attitude:"SUPERLIKE"
		}).then(() => {
			aiSearchCollectStatisticsProxyState.collected++
			$event("superlike",{
				...payload,
				kol:this.queue.current
			})
		})
		await this.queue.showNext()
	}
}

export const aiSearchKOLCollector = new AISearchKOLCollector()

function withProxy(queue: AISearchKOLQueue) {
	const kolService = getKOLService()
	return new Proxy(queue, {
		set: (target, key, value) => {
			if (key === 'data') {
				aiSearchCollectStatisticsProxyState.data = value.map((item: InQueueKOL) => item.url)
				log('Set data', value)
			}

			if (key === 'index') {
				aiSearchCollectStatisticsProxyState.index = value
				log('Set index', value)
			}

			if (key === 'data' || key === 'index') {
				const url = aiSearchCollectStatisticsProxyState.data[aiSearchCollectStatisticsProxyState.index]
				kolService.getKOLHandler(url).then((handler) => {
					log('Set current KOL', handler)
					aiSearchCollectStatisticsProxyState.currentKOL = handler
				})
			}

			if (key !== 'current') {
				target[key as keyof Omit<AISearchKOLQueue, 'current'>] = value
			}

			return true
		}
	})
}
