import { InQueueKOL } from '@/@types/kol'
import { compact, uniq } from 'lodash-es'
import { getCommonService } from '../../../services/CommonService'
import Similar from './Similar'
import { Tabs } from 'wxt/browser'
import { SimilarCreateTaskParams } from '@/@types/similar'
import { aiSearchCollectStatisticsProxyState } from '../store'

const log = makeModuleLog('AIsearchKOLQueue')
export default class AISearchKOLQueue {
	data: InQueueKOL[] = []
	index = 0
	lastProjectTabs:Tabs.Tab[] = []//永远都应该是当前工作区打开的两个tab
	private currentAvatarUrl:string | undefined

	private onLoadMore: (() => Promise<InQueueKOL[]>) | undefined
	private updateQueuePromises: Promise<void>[] = [];

	private onAvatarUpdate?: (avatarUrl: string) => void;

	setOnAvatarUpdate(callback: (avatarUrl: string) => void) {
		this.onAvatarUpdate = callback;
	}

	async init(data: InQueueKOL[], onLoadMore?: () => Promise<InQueueKOL[]>) {
		this.index = 0
		this.data = uniq(data)
		this.onLoadMore = onLoadMore

		await this.prepareNextGroupTabsAndActivateFirst()
	}

	get current(): InQueueKOL | undefined {
		return this.data[this.index]
	}

	private append(raw: InQueueKOL[]) {
		this.data = uniq(this.data.concat(...raw))

		if (this.data.length === 0) {
			throw new Error('No more data')
		}
	}

	private async prepareNextGroupTabsAndActivateFirst() {
		const tabs = []

		for (const item of this.data.slice(this.index, this.index + 2)) {
			const tab = await getCommonService().openTabIfNotExist(item.url, {
				active: false
			})

			tabs.push(tab)
		}

		this.lastProjectTabs = tabs
	
		if (tabs[0]?.id) {
			await getCommonService().activateTab(tabs[0].id)
		}
		return tabs
	}

	async reset(){
		await this.cleanLastProjectTabs()
		this.index = 0
		this.data = []
		this.lastProjectTabs = []
		this.currentAvatarUrl = undefined
		this.updateQueuePromises = []
	}

	 async cleanLastProjectTabs(){
		for (const tab of this.lastProjectTabs) {
			if (tab.id) {
				try {
					await browser.tabs.remove(tab.id);
				} catch (error) {
					// 忽略已经关闭的标签页错误
				}
			}
		}
	}

	private async cleanupPreviousTab() {
		const commonService = getCommonService()
		const kol = guard(this.current, 'Current KOL')

		await commonService.closeTabByURL(kol.url)
	}

	async cleanupTabs() {
		if(this.data.length === 0) return
		const tabs = await browser.tabs.query({
			url: this.data.map((item) => item.url)
		})
		await browser.tabs.remove(compact(tabs.map((item) => item.id)))
	}

	// private async updateQueue() {
		// const {data:updatedQueue} = await Similar.searchUnionSimilars(taskId)
		// const currentIndex = this.index
		// this.data = uniq([...this.data.slice(0, currentIndex+1), ...updatedQueue.filter(item=>item.id !== this.current?.id)])
	// }

	private async updateQueue(params:SimilarCreateTaskParams){
		// 当有新的promise加入时设置为true
		aiSearchCollectStatisticsProxyState.isQueueUpdating = true
		
		const updateTask = async () => {
			try {
				const {data:updatedQueue} = await Similar.findSimilars(params)
				const currentIndex = this.index
				const isLast = this.index >= this.data.length
				this.data = uniq([...this.data.slice(0, currentIndex+2), ...updatedQueue.filter(item=>!this.lastProjectTabs.some(tab=>tab.url === item.url))])
				if(isLast){
					await this.prepareNextGroupTabsAndActivateFirst()  
				}
			} finally {
				// 只有当所有promise都完成时才设置为false
				if(this.updateQueuePromises.length === 1) { // 因为当前这个promise还没被移除
					aiSearchCollectStatisticsProxyState.isQueueUpdating = false
				}
			}
		};

		const promise = updateTask().finally(() => {
			const index = this.updateQueuePromises.indexOf(promise);
			if (index > -1) {
				this.updateQueuePromises.splice(index, 1);
			}
		});

		this.updateQueuePromises.push(promise);

		return promise;
	}

	isQueueUpdating(): boolean {
		return this.updateQueuePromises.length > 0;
	}

	private async getAvatarUrl(){
		return await getCommonService().sendMessageToCurrentContent({type:'getAvatarUrl',timestamp: Date.now()})
	}

	async showNext() {
		log("currentAvatarUrl:"+this.currentAvatarUrl);
		//清理tab
		await this.cleanupPreviousTab()
		this.index++
		await this.prepareNextGroupTabsAndActivateFirst()  
	}

	 isCurrentTabValid(url:string): boolean {
		return !this.current || url === this.current.url
	}


	async switchToValidTab() {
		if (this.lastProjectTabs.length > 0 && this.lastProjectTabs[0].id) {
			try {
				await getCommonService().activateTab(this.lastProjectTabs[0].id);
			} catch (error) {
				await this.prepareNextGroupTabsAndActivateFirst()
			}
		}
	}
}
