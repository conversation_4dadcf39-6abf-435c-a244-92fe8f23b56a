import { InQueueKOL } from '@/@types/kol'
import { QUERY_PROJECT_LIKES } from '@/constants'
import { get } from '@/utils/request'
import { getKOLService } from '../../../services/KOLService'
import { collectStatisticsProxyState,DEFAULT_COLLET_STATISTICS,DEFAULT_FILTER_OPTIONS,filtersProxyState } from '../store'
import KOLQueue from './KOLQueue'
import Similar from './Similar'
import { getProjectService } from '@/services/ProjectService'
import { cloneDeep } from 'lodash-es'
import { isWorkingState } from '@/utils/storages'
import { getCommonService } from '@/services/CommonService'
import { ModalProps } from '@/@types'
import { getModalService } from '@/services/ModalService'

const log = makeModuleLog('KOLCollector')
const $event = createAnalysisPost("KOLCollector")

export interface KOLCollectorStartOptions<TDataItem> {
	data: TDataItem[]
	onLoadMore?: () => Promise<TDataItem[]>
}
export default class KOLCollector {
	queue: KOLQueue = withProxy(new KOLQueue())
	collectorType: "KOL" | "AI" = "KOL"

	private limitModal:ModalProps | null = null

	private showLimitModal(){
		if(this.limitModal){
			getModalService().show(this.limitModal)
			throw new Error("Limit modal")
		}
	}

	setLimitModal(modal:ModalProps){
		this.limitModal = modal
	}

	constructor() {
		this.queue.setOnAvatarUpdate((avatarUrl) => {
			this.updateTaskAvatarList(avatarUrl)
		})
		onMessage("getTikTokKOLOriginalFrom",({data:url})=>{
			const kol = this.queue.data.find(item=>item.url === url)
			if(kol && kol.commonFields){
				return kol.commonFields
			}
			return ""
		})
	}

	async start({ data, onLoadMore }: KOLCollectorStartOptions<InQueueKOL>) {
		await this.initializeCollectedList()
		await this.queue.init(data, onLoadMore)
	}

	async stop(onlyStop?:boolean) {
		await Similar.cancelAllTasks()
		!onlyStop && getProjectService().terminateProject()
		isWorkingState.setValue(false)
		Object.assign(collectStatisticsProxyState, cloneDeep(DEFAULT_COLLET_STATISTICS))
		if(!onlyStop){
			Object.keys(filtersProxyState).forEach(key => delete filtersProxyState[key as keyof typeof filtersProxyState]);
			Object.assign(filtersProxyState, cloneDeep(DEFAULT_FILTER_OPTIONS));
		} 
		await this.queue.cleanupTabs()
		await this.reset()
		this.limitModal = null
	}

	async reset(){
		await this.queue.reset()
	}

	updateTaskAvatarList(url:string){
		collectStatisticsProxyState.taskAvatarList = [
			{ url, status: 'processing' },
			...collectStatisticsProxyState.taskAvatarList.slice(0, 4)
		  ];
	}


	private async getMarkSimilarPayload() {
		this.showLimitModal()
		if(this.queue.index >= this.queue.data.length){
			let message = "No more items. Please click 'Stop' and choose another channel to start."

			if(this.queue.isQueueUpdating()){
				message = 'Please wait 1 min, system analyzing'
			}
			throw new Error(message)
		}
		const kol = guard(this.queue.current, 'Current KOL')
		const projectId = guard(await currentProjectIdState.getValue(), 'Project ID')

		return {
			projectId,
			kolId: kol.id
		}
	}

	private async getProjectLikeCount(){
		const projectId = guard(await currentProjectIdState.getValue(), 'Project ID')
		return await get(QUERY_PROJECT_LIKES, {
			params: {
				projectId
			}
		})
	}

	private async initializeCollectedList(){
		const { data:collectedList } = await this.getProjectLikeCount().then((res) => res.data)
		collectStatisticsProxyState.collected = collectedList.length;
	}


	async like() {
		const payload = await this.getMarkSimilarPayload()
		 Similar.rateSimilar({
			...payload,
			attitude:"LIKE"
		}).then(() => {
			collectStatisticsProxyState.collected++
			$event("like",{
				...payload,
				kol:this.queue.current
			})
		})
		
		await this.queue.showNext(false,{
			...filtersProxyState,
			projectId:payload.projectId,
			source:this.queue.current!.platformAccount,
			platform:this.queue.current!.platform,
			reason:"LIKE"
		})
	}

	async dislike() {
		const payload = await this.getMarkSimilarPayload()
		 Similar.rateSimilar({
			...payload,
			attitude:"DISLIKE"
		})
		$event("dislike",{
			...payload,
			kol:this.queue.current
		})
	   await this.queue.showNext(false,{
		...filtersProxyState,
		projectId:payload.projectId,
		source:collectStatisticsProxyState.sourceKOL!,
		platform:this.queue.current!.platform,
		reason:"DISLIKE"
	})
	
	}



	async superlike() {
		const payload = await this.getMarkSimilarPayload()
		await Similar.rateSimilar({
			...payload,
			attitude:"SUPERLIKE"
		}).then(() => {
			collectStatisticsProxyState.collected++
			$event("superlike",{
				...payload,
				kol:this.queue.current
			})
		})
		await this.queue.showNext(true,{
			...filtersProxyState,
			projectId:payload.projectId,
			source:this.queue.current!.platformAccount,
			platform:this.queue.current!.platform,
			reason:"SUPERLIKE"
		})
	}

	async backLastTab(){
		const backUrl = collectStatisticsProxyState.backUrl
		collectStatisticsProxyState.backUrl = null
		if(backUrl){
			this.queue.index --
			this.queue.clearCurrentAvatarUrl()
			await this.checkSessionPermission()
			getCommonService().restoreSessionByURL(backUrl)
		}
	}

	async checkSessionPermission(){
		const permissions = await browser.permissions.contains({permissions:['sessions']})
		if(!permissions){
			const res = await browser.permissions.request({permissions:['sessions']})
			if(!res){
				throw new Error('请先授权会话管理权限')
			}
		}
		return true
	}
}

export const kolCollector = new KOLCollector()

function withProxy(queue: KOLQueue) {
	const kolService = getKOLService()
	return new Proxy(queue, {
		set: (target, key, value) => {
			if (key === 'data') {
				collectStatisticsProxyState.data = value.map((item: InQueueKOL) => item.url)
				log('Set data', value)
			}

			if (key === 'index') {
				collectStatisticsProxyState.index = value
				log('Set index', value)
			}

			if (key === 'data' || key === 'index') {
				const url = collectStatisticsProxyState.data[collectStatisticsProxyState.index]
				kolService.getKOLHandler(url).then((handler) => {
					log('Set current KOL', handler)
					collectStatisticsProxyState.currentKOL = handler
				})
			}

			if (key !== 'current') {
				target[key as keyof Omit<KOLQueue, 'current' | 'index' | 'filteredData' | 'filteredIndex'>] = value
			}

			return true
		},
		get: (target, key) => {
			return target[key as keyof KOLQueue]
		}
	})
}
