import { InQueueKOL } from '@/@types/kol'
import { compact, uniq } from 'lodash-es'
import { getCommonService } from '../../../services/CommonService'
import Similar from './Similar'
import { Tabs } from 'wxt/browser'
import { SimilarCreateTaskParams } from '@/@types/similar'
import { collectStatisticsProxyState } from '../store'
import { createFlyingAvatar } from '@/utils/flyingAvatar'
import { Platform } from '@/@types/platform'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import { getTwitterService } from '@/services/TwitterService'

const log = makeModuleLog('KOLQueue')
const DEFAULT_AVATAR_URL = "/icon/defaultAvatar.png"
export default class KOLQueue {
	data: InQueueKOL[] = []
	index = 0
	bioHasEmail:boolean = false
	private currentAvatarUrl:string | undefined

	get filteredData():InQueueKOL[]{
		return this.bioHasEmail ? this.data.filter(kol => kol.email) : this.data
	}

	get filteredIndex():number{
		return this.bioHasEmail ? this.filteredData.findIndex(kol => kol.url === this.current?.url) : this.index
	}

	private findNextValidIndex(startIndex: number): number {
		if (!this.bioHasEmail) {
			return startIndex;
		}
		
		let nextIndex = startIndex;
		while (nextIndex < this.data.length && !this.data[nextIndex].email) {
			nextIndex++;
		}
		return nextIndex;
	}

	private isChangeBioHasEmail = false
	async bioHasEmailOnChange(newValue: boolean) {
		if(this.bioHasEmail === newValue) return
		this.isChangeBioHasEmail = true
		await this.cleanupTabs()
		this.bioHasEmail = newValue;
		await this.prepareNextGroupTabsAndActivateFirst();
		this.isChangeBioHasEmail = false
	}

	clearCurrentAvatarUrl(){
		this.currentAvatarUrl = undefined
	}

	private onLoadMore: (() => Promise<InQueueKOL[]>) | undefined
	private updateQueuePromises: Promise<void>[] = [];

	private onAvatarUpdate?: (avatarUrl: string) => void;

	setOnAvatarUpdate(callback: (avatarUrl: string) => void) {
		this.onAvatarUpdate = callback;
	}

	async init(data: InQueueKOL[], onLoadMore?: () => Promise<InQueueKOL[]>) {
		this.index = 0
		this.data = uniq(data)
		this.onLoadMore = onLoadMore

		await this.prepareNextGroupTabsAndActivateFirst()
	}

	get current(): InQueueKOL | undefined {
		return this.data[this.index]
	}


	private async prepareNextGroupTabsAndActivateFirst() {
		const tabs = []
		let nextIndex = this.index
		let count = 0

		// 如果 bioHasEmail 开启，找到下一个有 email 的 KOL
		if (this.bioHasEmail) {
			nextIndex = this.findNextValidIndex(nextIndex)
		}

		// 最多准备两个标签页
		while (count < 2 && nextIndex < this.data.length) {
			const item = this.data[nextIndex]
			// 如果 bioHasEmail 开启，跳过没有 email 的 KOL
			if (this.bioHasEmail && !item.email) {
				nextIndex++
				continue
			}
			
			const tab = await getCommonService().openTabIfNotExist(item.url, {
				active: false
			})
			tabs.push(tab)
			nextIndex++
			count++
		}
	
		if (tabs[0]?.id) {
			await getCommonService().activateTab(tabs[0].id)
		}
		return tabs
	}

	async reset(){
		this.index = 0
		this.data = []
		this.currentAvatarUrl = undefined
		this.updateQueuePromises = []
		this.bioHasEmail = false
	}

	private async cleanupPreviousTab(shouldCreateNewTask: boolean) {
		const commonService = getCommonService()
		const kol = guard(this.current, 'Current KOL')

		await commonService.closeTabByURL(kol.url)
		if(!shouldCreateNewTask){
			collectStatisticsProxyState.backUrl = kol.url
		}else{
			collectStatisticsProxyState.backUrl = null
		}
	}

	async cleanupTabs() {
		if(this.data.length === 0) return
		const tabs = await browser.tabs.query({
			url: this.data.map((item) => item.url)
		})
		
		// 检查当前所有标签页
		const allTabs = await browser.tabs.query({})
		// 如果删除这些标签页后就没有标签页了，先创建一个新标签页
		if (allTabs.length === tabs.length) {
			await getCommonService().openTab({})
		}
		
		await browser.tabs.remove(compact(tabs.map((item) => item.id)))
	}

	// private async updateQueue() {
		// const {data:updatedQueue} = await Similar.searchUnionSimilars(taskId)
		// const currentIndex = this.index
		// this.data = uniq([...this.data.slice(0, currentIndex+1), ...updatedQueue.filter(item=>item.id !== this.current?.id)])
	// }

	private async updateQueue(params:SimilarCreateTaskParams){
		// 当有新的promise加入时设置为true
		collectStatisticsProxyState.isQueueUpdating = true
		const currentAvatarUrl = this.currentAvatarUrl;
		
		const updateTask = async () => {
			try {
				const {data:updatedQueue} = await Similar.findSimilars(params)
				const currentIndex = this.index
				const isLast = this.index >= this.data.length
				const uniqueItems = new Map();
				this.data.slice(0, currentIndex+2).forEach(item => {
					uniqueItems.set(item.url, item);
				});
				updatedQueue.forEach(item => {
						uniqueItems.set(item.url, item);
				});
				this.data = Array.from(uniqueItems.values());
				if(isLast){
					await this.prepareNextGroupTabsAndActivateFirst()  
				}

			} finally {
				// 更新avatar状态
				if (currentAvatarUrl) {
					const avatarIndex = collectStatisticsProxyState.taskAvatarList.findLastIndex(
						avatar => avatar.url === currentAvatarUrl
					);
					if (avatarIndex !== -1) {
						collectStatisticsProxyState.taskAvatarList[avatarIndex].status = 'completed';
					}
				}
				// 只有当所有promise都完成时才设置为false
				if(this.updateQueuePromises.length === 1) { // 因为当前这个promise还没被移除
					collectStatisticsProxyState.isQueueUpdating = false
				}
			}
		};

		const promise = updateTask().finally(() => {
			const index = this.updateQueuePromises.indexOf(promise);
			if (index > -1) {
				this.updateQueuePromises.splice(index, 1);
			}
		});

		this.updateQueuePromises.push(promise);

		return promise;
	}

	isQueueUpdating(): boolean {
		return this.updateQueuePromises.length > 0;
	}

	private async getAvatarUrl():Promise<string | undefined>{
		const url = await browser.tabs.query({active:true,currentWindow:true}).then(tabs=>tabs[0].url)
		if(getPlatform(url!) === Platform.TWITTER){
			const twitterInfo = guard(await getTwitterService().getTwitterProfilePage(),'No Twitter Profile Page')
			return twitterInfo?.mainEntity?.image?.contentUrl || DEFAULT_AVATAR_URL
		}
		return await getCommonService().sendMessageToCurrentContent({type:'getAvatarUrl',timestamp: Date.now()}) as string | undefined
	}

	async showNext(shouldCreateNewTask: boolean,params:SimilarCreateTaskParams) {
		log("currentAvatarUrl:"+this.currentAvatarUrl);

		//如果需要创建任务了，但是依然没有头像，则需要获取头像
		if(shouldCreateNewTask && !this.currentAvatarUrl){
			this.currentAvatarUrl = await this.getAvatarUrl()
		}

		//清理tab
		if(this.index >= this.data.length-1){
			await browser.tabs.create({})
		}
		await this.cleanupPreviousTab(shouldCreateNewTask)
		
		// 使用 findNextValidIndex 来获取下一个有效的索引
		this.index = this.findNextValidIndex(this.index + 1);
		
		// 如果找不到有效的 KOL，可以在这里处理边界情况
		if (this.index >= this.data.length) {
			return;
		}

		//创建任务，有头像则更新头像队列
		if(shouldCreateNewTask){
			const avatarUrl = this.currentAvatarUrl = this.currentAvatarUrl || DEFAULT_AVATAR_URL
			const avatarListElement = document.getElementById('avatarList')
			const sourceElement = params.reason==='SUPERLIKE'? document.getElementById('superlike'): params.reason === 'LIKE' ? document.getElementById('like') :undefined
			if (avatarListElement && sourceElement) {
                createFlyingAvatar(DEFAULT_AVATAR_URL, avatarListElement, sourceElement)
			}
			this.onAvatarUpdate?.(avatarUrl)
			this.updateQueue(params)
		}

		//准备下一个tab，每次切换 tab以后都会获取当前头像
		if (this.index < this.data.length) {
			await this.prepareNextGroupTabsAndActivateFirst()  
			this.currentAvatarUrl = undefined
			this.currentAvatarUrl = await this.getAvatarUrl()
			log("newAvatarUrl:"+this.currentAvatarUrl);
		}
	}

	 isCurrentTabValid(url:string): boolean {
		if(this.isChangeBioHasEmail) return true
		return !this.current || url.split('/').slice(0,4).join('/') === this.current.url.split('/').slice(0,4).join('/')
	}


	async switchToValidTab() {
		if(!this.current) return
		const currentTab = await getCommonService().getTabsByURL(this.current.url)
		if (currentTab.length > 0 && currentTab[0].id) {
			try {
				await getCommonService().activateTab(currentTab[0].id);
			} catch (error) {
				await this.prepareNextGroupTabsAndActivateFirst()
			}
		}else{
			await this.prepareNextGroupTabsAndActivateFirst()
		}
	}
}
