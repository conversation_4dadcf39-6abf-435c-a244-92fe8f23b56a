import { TaskStatus } from '@/@types'
import { InQueueKOL, UNION_KOL } from '@/@types/kol'
import { Platform } from '@/@types/platform'
import { SimilarCreateTaskParams, SimilarCreatedResult, SimilarFilters, SimilarTaskResultItem } from '@/@types/similar'
import {
	AI_SEARCH_CREATE,
	CREATE_SIMILARS_TASK,
	GET_SIMILAR_TASK,
	RATE_SIMILAR,
	SEARCH_LASTEST_TASKS,
	UNION_SEARCH_SIMILARS,
	UNION_SEARCH_SIMILARS_BY_TIKTOK
} from '@/constants'
import { CREATE_LONG_TASK } from '@/constants/api'
import { ERRORS } from '@/constants/errors'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import { getModalService } from '@/services/ModalService'
import { getProjectService } from '@/services/ProjectService'
import { checkIsPostPlatform, getCurrentPostHandler } from '@/utils/getPostHander'
import { get, post } from '@/utils/request'
import { currentProjectIdState } from '@/utils/storages'
import { getCountryCode, getCountryFlag } from '@/utils/tiktok/getCountryIcon'
import { GuestTwitterGraphQLUserUseCase } from '@/utils/twitter/twitterApi/useCases'
import { compact } from 'lodash-es'
import { getCommonService } from '../../../services/CommonService'
import { getKOLService } from '../../../services/KOLService'
import { collectStatisticsProxyState } from '../store'

let apiType = 'REMIX'
// @ts-expect-error 作为隐藏功能，不进行类型检查
globalThis.changeType = (type: string) => {
	apiType = type
}

const log = makeModuleLog('Similar')

export default class Similar {
	private static tasks: Map<string, AbortController> = new Map()

	static resetAbortController(taskId: string) {
		const controller = this.tasks.get(taskId)
		if (controller) {
			controller.abort()
		}
		const newController = new AbortController()
		this.tasks.set(taskId, newController)
		return newController
	}

	static cancelCurrentTask(taskId: string) {
		const controller = this.tasks.get(taskId)
		if (controller) {
			controller.abort()
			this.tasks.delete(taskId)
		}
	}

	static cancelAllTasks() {
		for (const controller of this.tasks.values()) {
			controller.abort()
		}
		this.tasks.clear()
	}

	private static toInQueueKOLs(similarTaskResults: SimilarTaskResultItem[], platform: Platform): InQueueKOL[] {
		const data = similarTaskResults.map((info) => {
			let url: string

			switch (platform) {
				case Platform.YOUTUBE:
					url = `https://www.youtube.com/channel/${info?.platformAccount.replace('channel/', '')}/videos`
					break
				case Platform.TIKTOK:
					url = `https://www.tiktok.com/@${info?.platformAccount}`
					break
				case Platform.INS:
					url = `https://www.instagram.com/${info?.platformAccount + (collectStatisticsProxyState.isInsReelMode ? '/reels/' : '')}`
					break
				case Platform.TWITTER:
					url = `https://x.com/${info?.platformAccount}`
					break
			}

			return {
				...info,
				url
			} as InQueueKOL
		})

		return data
	}

	//根据 taskId 轮询任务状态
	private static async querySimilarTask(taskId: string, source: string) {
		const controller = this.resetAbortController(taskId)

		try {
			return await this.pollTaskResult(taskId, source, controller.signal)
		} catch (error) {
			if (error instanceof Error && error.name === 'AbortError') {
				throw new Error(ERRORS.CENCEL_TASKS)
			}
			throw error
		} finally {
			this.tasks.delete(taskId)
		}
	}

	private static async pollTaskResult(taskId: string, source: string, signal: AbortSignal) {
		let similars: SimilarTaskResultItem[] = []
		// let source: string | null = null

		// eslint-disable-next-line no-constant-condition
		while (true) {
			if (signal.aborted) {
				throw new Error(ERRORS.CENCEL_TASKS)
			}

			const task = await get<any>(GET_SIMILAR_TASK, {
				params: {
					id: taskId
				},
				signal
			}).then((res) => res.data)

			similars = task.data ?? []
			// source = task.params.source
			if (task.status === TaskStatus.FAILED) {
				throw new Error(task.message)
			}
			if (task.status === TaskStatus.COMPLETED) {
				break
			}
			await sleep(5000)
		}

		return {
			similars,
			source
		}
	}

	// 创建一个找相似任务
	static async createTask(params: SimilarCreateTaskParams): Promise<{ taskId: string }> {
		const controller = new AbortController()
		// if(params.platform === Platform.TWITTER){
		// 	const twitterInfo = guard(await getTwitterService().getTwitterProfilePage(),'No Twitter Profile Page')
		// 	const connectTimeline = await new GuestTwitterGraphQLUserConnectionsUseCase(guard(twitterInfo?.mainEntity?.identifier, 'No Twitter User ID'), 20).fetchUserConnections()
		// 	const twitterUserNames = connectTimeline?.data?.connect_tab_timeline?.timeline?.instructions?.at(-1)?.entries?.[1]?.content?.items?.map((e:any)=>e?.item?.itemContent?.user_results?.result?.core?.screen_name).filter(Boolean) || []
		// 	params.twitterUserNames = twitterUserNames
		// }

		//有一些默认数据，所以做处理，tt 的不需要最小粉丝数
		if (params.platform === Platform.TIKTOK) {
			params.minAverageLikeCount = undefined
			params.maxAverageLikeCount = undefined
		} else if (params.platform === Platform.INS) {
			// ins 不需要视频播放量和最小订阅数
			params.videosAverageViews = undefined
		} else if (params.platform === Platform.YOUTUBE) {
			params.minAverageLikeCount = undefined
			params.maxAverageLikeCount = undefined
			// youtube 如果 source 中是 channel，则这段过滤
			params.source = params.source.replace('channel/', '')
		}
		//如果国家是空数组，就不用传
		if (!params.regions?.length) {
			params.regions = undefined
		}
		const { id: taskId } = await post(CREATE_SIMILARS_TASK, params, {
			signal: controller.signal
		}).then((res) => res.data.data)

		this.tasks.set(taskId, controller)
		return { taskId }
	}

	//找相似
	static async findSimilars(similarParams: Omit<SimilarCreateTaskParams, 'projectId'>) {
		const { source, platform, reason, allowList, banList, kolDescription, ...filters } = similarParams
		const projectId = await currentProjectIdState.getValue()

		if (!projectId) {
			log('Cannot find project id')
			throw new Error('Cannot find project id')
		}

		const params: SimilarCreateTaskParams = {
			projectId,
			...filters,
			allowList,
			banList,
			kolDescription,
			source,
			platform,
			reason
		}

		const { taskId } = await this.createTask(params)

		log('taskId', taskId)
		await this.querySimilarTask(taskId, source as string)

		const { data: inQueueKOLs } = await this.searchUnionSimilars(taskId, platform)

		// const inQueueKOLs = compact(this.toInQueueKOLs(similarKOLs, platform))
		return {
			data: inQueueKOLs,
			source
		}
	}

	//查找当前 project 的所有任务记录
	static async searchTasks(taskType: 'SIMILAR' | 'KEYWORD' = 'SIMILAR') {
		const projectId = await currentProjectIdState.getValue()
		const tasks = await get<{ data: SimilarCreatedResult }>(SEARCH_LASTEST_TASKS, {
			params: {
				projectId,
				taskType
			}
		}).then((res) => {
			return res.data.data
		})
		return tasks
	}

	//已经拥有已完成的 taskId，根据 taskId 查找任务
	static async searchUnionSimilars(taskId: string, platform: Platform, isAISearch: boolean = false) {
		const projectId = await currentProjectIdState.getValue()

		if (!projectId) {
			log('Cannot find project id')
			throw new Error('Cannot find project id')
		}

		let response

		if (platform === Platform.TIKTOK) {
			response = await get<{ data: UNION_KOL[]; message: string }>(UNION_SEARCH_SIMILARS_BY_TIKTOK, {
				params: {
					projectId,
					taskType: isAISearch ? 'KEYWORD' : 'SIMILAR'
				}
			})
		} else {
			response = await post<{ data: UNION_KOL[]; message: string }>(UNION_SEARCH_SIMILARS, {
				taskId,
				type: apiType
			})
		}

		const { data, message } = response.data

		if (!data || data.length === 0) {
			throw new Error(message)
		}

		// const url = guard(await getCommonService().getActiveTabURL(), 'Current Tab URL')
		// const platform = await getPlatformService().getPlatform(url)

		const inQueueKOLs = compact(this.toInQueueKOLs(data, platform as Platform))

		return {
			data: inQueueKOLs
		}
	}

	//写个专门用于 twitter 获取 source,filter 的函数，并顺便把头像也获取了
	static async getTwitterParams(url?: string) {
		url = url || guard(await getCommonService().getActiveTabURL(), 'Current Tab URL')
		const source = guard(extractTwitterKOLHandler(url), 'No SupportPage')
		const user = await new GuestTwitterGraphQLUserUseCase(source!).fetchUser()
		console.log('user', user)
		const avatar = user?.data?.user?.result?.avatar?.image_url
		// const restId = user?.data?.user?.result?.rest_id
		// const connectTimeline = await new GuestTwitterGraphQLUserConnectionsUseCase(restId!, 20).fetchUserConnections()
		collectStatisticsProxyState.taskAvatarList.push({ url: avatar || '/icon/defaultAvatar.png', status: 'completed' })
		// console.log("connectTimeline",connectTimeline)
		// const twitterUserNames = connectTimeline?.data?.connect_tab_timeline?.timeline?.instructions?.at(-1)?.entries?.at(-1)?.content?.items?.map((e:any)=>e?.item?.itemContent?.user_results?.result?.core?.screen_name).filter(Boolean) || []
		return await this.findSimilars({ platform: Platform.TWITTER, source, reason: 'SEARCH' })
	}

	static async getCurrentParams(source?: string): Promise<{ platform: Platform; source: string }> {
		const url = guard(await getCommonService().getActiveTabURL(), 'Current Tab URL')
		if (!source) {
			if (checkIsPostPlatform(url)) {
				source = await getCurrentPostHandler()
				log('获取到 source', source)
			} else {
				source = guard(await getKOLService().getKOLHandler(url), 'Source KOL Handler')
			}
		}
		const platform = getPlatform(url)
		if (platform === Platform.INS && url.includes('/reels')) {
			collectStatisticsProxyState.isInsReelMode = true
		}
		return { platform, source }
	}

	//根据当前 url 查找任务
	static async findSimilarByCurrentSource(filters: SimilarFilters, historySource?: string) {
		// const url = guard(await getCommonService().getActiveTabURL(), 'Current Tab URL')
		const { platform, source } = await this.getCurrentParams(historySource)
		const aiFilterParams = await getProjectService().getAiFilterParams()
		const params = { ...filters, platform, source, reason: 'SEARCH', ...aiFilterParams }
		// 如果 taskRound === 4，调用长任务爬取服务
		if (params.taskRound === 4) {
			const projectId = await currentProjectIdState.getValue()
			if (!projectId) {
				throw new Error('Cannot find project id')
			}

			try {
				// 准备长任务请求参数
				const longCrawlerRequest = {
					platform: platform as 'INSTAGRAM' | 'TIKTOK' | 'YOUTUBE',
					projectId,
					filters: {
						kolDescription: aiFilterParams.kolDescription || '',
						followerRange:
							params.minSubscribers || params.maxSubscribers
								? {
										min: params.minSubscribers,
										max: params.maxSubscribers
									}
								: undefined,
						regions: params.regions || [],
						averageLikeCount:
							params.minAverageLikeCount || params.maxAverageLikeCount
								? {
										min: params.minAverageLikeCount,
										max: params.maxAverageLikeCount
									}
								: undefined
					},
					seedUsers: [source],
					numberOfRuns: 4 // 默认值，可根据需求调整
				}

				log('Creating long crawler task with params:', longCrawlerRequest)
				log('Platform:', platform, 'Source:', source, 'ProjectId:', projectId)

				// 直接调用 API，不通过 proxy service
				const response = await post<{
					statusCode: number
					error: null | string
					message: string
					data: {
						id: string
						projectId: string
						status: string
						[key: string]: any
					}
				}>(CREATE_LONG_TASK, longCrawlerRequest)

				log('API response:', response)

				if (!response || !response.data || !response.data.data) {
					throw new Error('Invalid response structure from long crawler API')
				}

				const taskResponse = response.data.data

				log('Task response:', taskResponse)

				if (taskResponse && taskResponse.id) {
					const url = `https://easykol.com/auto-task?taskId=${taskResponse.id}&platform=${platform}`
					browser.tabs.create({ url: url })
					window.close()
					// 返回空结果，避免后续流程执行
					return null
				} else {
					throw new Error('Failed to create long crawler task: no task ID returned')
				}
			} catch (error) {
				log('Create long crawler task error:', error)
				if (error instanceof Error) {
					log('Error message:', error.message)
					log('Error stack:', error.stack)
				}
				// 重新抛出错误，让上层处理
				throw error
			}
		}

		if (filters?.regions?.includes('ru') && platform === Platform.TIKTOK) {
			getModalService().show({
				type: 'alert',
				content: [
					`Creators in Russia use VPNs to access TikTok due to the war, so their location data is unreliable.`,
					`The Russia 🇷🇺 country filter won't work.`
				],
				confirmText: 'Got'
			})
		}

		console.log(params, 'params')

		return await this.findSimilars(params as any)
	}

	static async checkRegion(platform: Platform, source: string, filters: SimilarFilters) {
		if (!filters.regions?.length) {
			return true
		}

		const region = (await getCommonService().sendMessageToCurrentContent(
			{ type: 'getInfoCardRegion', timestamp: Date.now() },
			true
		)) as string
		log('获取到 region', region)
		const countryCode = getCountryCode(region)
		// if(!countryCode){
		// const res = await getKOLService().getKOLRegion({
		// 	platform,
		// 	handler:source
		// })
		// countryCode = getCountryCode(res.region)
		// }

		if (countryCode && filters.regions?.length && !filters.regions.includes(countryCode.toLowerCase())) {
			const chooseRegions = filters.regions
			const selectRegions = chooseRegions.map((e) => getCountryFlag(e)).join(' ')
			const kolRegion = getCountryFlag(countryCode)
			getModalService().show({
				type: 'alert',
				title: 'For Better Results',
				content: [
					`Cross-country results are unreliable`,
					`This creator is from ${kolRegion}`,
					`Please choose one from ${selectRegions}${chooseRegions.length > 3 ? '...' : ''}`
				],
				confirmText: 'Got'
			})
			return false
		}

		return true
	}

	static async checkTags() {
		const tags = (await getCommonService().sendMessageToCurrentContent(
			{ type: 'GET_TAGS', timestamp: Date.now() },
			true
		)) as number
		log('获取到 tags', tags)
		return tags
	}

	//检查当前 project 的已完成任务，并根据已完成任务查找相似 kol
	static async checkAndProcessTasks(
		isAISearch: boolean = false
	): Promise<{ data: InQueueKOL[]; params: SimilarCreateTaskParams }> {
		const task = await this.searchTasks(isAISearch ? 'KEYWORD' : 'SIMILAR')

		// 如果有已完成的任务，则获取已完成的列表
		const { data } = await this.searchUnionSimilars(task.id, task.params.platform, isAISearch)
		if (data.length === 0) {
			throw new Error('No suitable influencer found. Try loosening views or country')
		}
		return {
			data,
			params: task.params
		}
	}

	static rateSimilar(params: { projectId: string; kolId: string; attitude: 'LIKE' | 'DISLIKE' | 'SUPERLIKE' }) {
		return post(RATE_SIMILAR, params)
	}

	//new AI 搜索功能
	private static async createAISearchTask(params: AiSearchParams): Promise<{ taskId: string }> {
		if (!params.projectId) {
			const projectId = await currentProjectIdState.getValue()

			if (!projectId) {
				log('Cannot find project id')
				throw new Error('Cannot find project id')
			}
			params.projectId = projectId
		}
		if (!this.tasks.size) {
			this.cancelAllTasks()
		}

		const { data } = await post<AISearchResponse>(AI_SEARCH_CREATE, params)
		this.tasks.set(data.id, new AbortController())

		return { taskId: data.id }
	}

	static async aiSearch(params: AiSearchParams) {
		const { taskId } = await this.createAISearchTask(params)
		log('taskId', taskId)
		await this.querySimilarTask(taskId, '')

		const { data: inQueueKOLs } = await this.searchUnionSimilars(taskId, params.platform as Platform, true)
		log('inQueueKOLs', inQueueKOLs)

		return {
			data: inQueueKOLs
		}
	}
}
