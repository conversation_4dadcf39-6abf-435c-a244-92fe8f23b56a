import { HTMLAttributes } from 'react'
import EmailTemplateTable from '../components/EmailTemplateTable'
import EmailTemplateSelectAlert from '../components/EmailTemplateSelectAlert'

export interface EstablishContactPageProps extends HTMLAttributes<HTMLDivElement> {}

export default function EstablishContactPage({ ...props }: EstablishContactPageProps) {
	return (
		<div
			css={css`
				padding: 1rem;
			`}
		>
			<section className="mb-4">
				<header className="flex justify-between items-center mb-[30px]">
					<div className="flex gap-2 items-center text-2xl ">Email Template</div>
				</header>
				<div className="w-full  mb-4">
					<EmailTemplateSelectAlert />
				</div>

				<EmailTemplateTable />
			</section>
		</div>
	)
}
