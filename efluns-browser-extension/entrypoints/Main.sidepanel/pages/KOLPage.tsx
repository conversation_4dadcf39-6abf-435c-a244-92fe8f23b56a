import { CREATE_SIMILARS_TASK } from '@/constants'
import { isUserConsentedState, isWorkingState } from '@/utils/storages'

import { InQueueKOL } from '@/@types/kol'
import { Platform } from '@/@types/platform'
import { SimilarFilters } from '@/@types/similar'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import { getCommonService } from '@/services/CommonService'
import { getEasykolCookieService } from '@/services/EasykolCookieService'
import { getModalService } from '@/services/ModalService'
import { getTwitterService } from '@/services/TwitterService'
import { createAnalysisPost } from '@/utils/analysisPost'
import getSupportPage from '@/utils/getSupportPage'
import { isTwitterKolPage } from '@/utils/twitter/isTwitterKolPage'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useMutation } from '@tanstack/react-query'
import { useAtom } from 'jotai'
import { cloneDeep } from 'lodash-es'
import { HTMLAttributes, useCallback, useEffect, useRef } from 'react'
import toast from 'react-hot-toast'
import AIFeatureUserConsentModal from '../components/AIFeatureUserConsentModal'
import FilterForm from '../components/FilterForm'
import { HelpMenu } from '../components/HelpMenu'
import KOLCollection from '../components/KOLCollection'
import Notify from '../components/Notify'
import ProjectSelect from '../components/ProjectSelect'
import { kolCollector } from '../models/KOLCollector'
import Similar from '../models/Similar'
import {
	DEFAULT_COLLET_STATISTICS,
	DEFAULT_FILTER_OPTIONS,
	collectStatisticsProxyState,
	collectStatisticsState,
	filtersState,
	inCollectModeState
} from '../store'

const log = makeModuleLog('KOLPage')
const $event = createAnalysisPost('SimilarPage')
export interface AppProps extends HTMLAttributes<HTMLDivElement> {}

export default function KOLPage({ ...props }: AppProps) {
	const userConsentModalRef = useRef<HTMLDialogElement>(null)
	const [inCollectMode, setInCollectMode] = useAtom(inCollectModeState)
	const [activeTabURL] = useStorageState(activeTabURLState)
	const [collectStatics, setCollectStatistics] = useAtom(collectStatisticsState)
	const [filters, setFilters] = useAtom(filtersState)
	// const [filters, setFilters] = useState({});//筛选查询项
	const [isCheckingTask, setIsCheckingTask] = useState(false) //检查当前是否发起过任务的状态
	const hasRunCheckExistingTask = useRef(false) //检查任务是否运行过
	const [checkingTaskFailed, setCheckingTaskFailed] = useState(false) //检查任务是否全部为 failed
	const [projectId] = useStorageState(currentProjectIdState)
	const currentProjectRef = useRef<string | null>(null)

	const [visionUrl, setVisionUrl] = useState<string | null>(null)

	const [showAIFilter, setShowAIFilter] = useState(false)

	const handleSimilarResult = useCallback(
		async (data: InQueueKOL[], source: string) => {
			isWorkingState.setValue(true) // 进入工作状态
			setCollectStatistics((prev) => ({ ...prev, sourceKOL: source }))

			await kolCollector.start({
				data
				// onLoadMore: loadMore
			})
			setInCollectMode(true)
			setIsCheckingTask(false)
		},
		[setCollectStatistics, setInCollectMode]
	)

	const getAvatar = useCallback(async (avatar?: string) => {
		const avatarUrl =
			avatar ||
			(await getCommonService().sendMessageToCurrentContent({ type: 'getAvatarUrl', timestamp: Date.now() })) ||
			'/icon/defaultAvatar.png'
		kolCollector.updateTaskAvatarList(avatarUrl as string)
	}, [])

	const {
		mutateAsync: findSimilar,
		isPending: isFinding,
		reset: resetFindSimilar
	} = useMutation({
		mutationKey: [CREATE_SIMILARS_TASK],
		mutationFn: async ({
			currentSource,
			elseFilters
		}: {
			currentSource?: string
			elseFilters?: Partial<SimilarFilters>
		}) => {
			try {
				//TODO 如果是 twitter，则请求接口获取 source 和头像
				const isTwitter = getPlatform(activeTabURL!) === Platform.TWITTER
				const twitterInfo = isTwitter
					? guard(await getTwitterService().getTwitterProfilePage(), 'No Twitter Profile Page')
					: undefined
				getAvatar(twitterInfo?.mainEntity?.image?.contentUrl)

				const newFilters = { ...filters, ...(elseFilters || {}) }
				if (isTwitter) {
					currentSource = twitterInfo?.mainEntity?.additionalName
				}

				const res = await Similar.findSimilarByCurrentSource(newFilters, currentSource)
				if (res) {
					const { data, source } = res
					await handleSimilarResult(data, source)
				}
			} catch (err) {
				if (err instanceof Error && err?.message) {
					toast.error(err.message)
				} else {
					toast.error('Find Similar failed')
				}
				setCollectStatistics((prev) => ({ ...prev, taskAvatarList: [] }))

				log('Find Similar By CurrentSource Err', err)
			}
		}
	})

	const { mutateAsync: checkCurrentRegion, isPending: isCheckingCurrentRegion } = useMutation({
		mutationKey: ['CHECK_CURRENT_REGION'],
		mutationFn: async () => {
			const { platform, source } = await Similar.getCurrentParams()
			return Similar.checkRegion(platform, source, filters)
		}
	})

	const { mutateAsync: openAIFilter, isPending: isOpenAIFilter } = useMutation({
		mutationKey: ['OPEN_AI_FILTER'],
		mutationFn: async (isVision?: boolean) => {
			console.log('isVision', isVision)

			return getCommonService().sendMessageToCurrentContent(
				{ type: 'showAIFilter', isVision, timestamp: Date.now() },
				true
			)
		}
	})

	const handleFindSimilars = useCallback(
		async (isVision?: boolean) => {
			const currentTabUrl = await browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => tabs[0].url)
			const isTwitter = currentTabUrl && isTwitterKolPage(currentTabUrl)
			if (isTwitter) {
				await getEasykolCookieService().requestCookiePermission()
				// findSimilar({})
				// return
			}
			if (
				!currentTabUrl ||
				(!checkIsPostPlatform(currentTabUrl) && !(await getSupportPage(currentTabUrl)) && !isTwitter)
			) {
				return getModalService().show({
					title: 'Something wrong',
					content: 'Please open a creator’s homepage on the left before clicking Similar.',
					type: 'alert',
					confirmText: 'Got'
				})
			}

			if (currentTabUrl.match(/https:\/\/www\.youtube\.com\/[^/]+\/shorts/)) {
				return getModalService().show({
					title: 'Something wrong',
					content:
						"We don't support YouTube Shorts creators. Please pick a YouTuber who makes regular long videos instead.",
					type: 'alert',
					confirmText: 'Got it'
				})
			}

			// const supportPage = guard(await getSupportPage(currentTabUrl), 'Support Page')

			if (!isTwitter) {
				memorySelectRegionState.setValue(filters.regions || [])

				const isRegionValid = await checkCurrentRegion()
				if (!isRegionValid) {
					return
				}
			}

			if (isVision) {
				const res = await openAIFilter(isVision)
				if (res) {
					if (isVision) {
						setVisionUrl(currentTabUrl)
					}
					setShowAIFilter(true) //打开蒙层
					return
				}
			} else {
				let ytbIds: string[] = []
				if (activeTabURL?.includes('youtube.com')) {
					const tabId = await getCommonService().getActiveTabId()
					ytbIds = await sendMessage('getYoutubeVideoIds', undefined, tabId)
				}
				findSimilar({ elseFilters: { ytbVideoIds: ytbIds } })
				return
			}

			const isConsented = await isUserConsentedState.getValue()
			if (!isConsented) {
				userConsentModalRef.current?.showModal()
			} else {
				findSimilar({})
			}
		},
		[activeTabURL, checkCurrentRegion, filters.regions, findSimilar, openAIFilter]
	)

	const renderFindSimilarButton = useCallback(
		() => (
			<>
				{isCheckingTask ? (
					<div className="flex items-center text-center absolute left-0 top-0 w-full h-[100vh] justify-center px-1">
						<span className="loading loading-spinner loading-sm" />
						<span>Searching for unfinished tasks, please wait...</span>
					</div>
				) : (
					<>
						{window.innerHeight > 604 && (
							<div className="w-full max-w-[300px]">
								<Notify />
							</div>
						)}
						{getBrowserType() === 'Edge' && (
							<div className="text-center text-[red] border rounded-lg px-2 py-1">
								⚠️This function may have compatibility issues in Microsoft Edge, please use Google Chrome
							</div>
						)}
						<FilterForm
							disabled={isFinding || isCheckingCurrentRegion || isOpenAIFilter}
							handleFindSimilars={handleFindSimilars}
						/>
						<AIFeatureUserConsentModal
							ref={userConsentModalRef}
							onSubmit={() => {
								findSimilar({})
							}}
						/>
						<div className="text-center text-xs text-gray-500 fixed	 bottom-2 left-0 w-full">
							<HelpMenu />
						</div>
					</>
				)}
				{checkingTaskFailed && (
					<div
						className="absolute bottom-4 right-4 tooltip tooltip-left cursor-pointer"
						data-tip="Failed to retrieve unfinished tasks"
					>
						<FontAwesomeIcon icon="exclamation-triangle" className="text-yellow-500 text-2xl" />
					</div>
				)}
			</>
		),
		[
			isCheckingTask,
			isFinding,
			isCheckingCurrentRegion,
			handleFindSimilars,
			checkingTaskFailed,
			findSimilar,
			isOpenAIFilter
		]
	)

	const checkExistingTask = useCallback(async () => {
		if (hasRunCheckExistingTask.current) return
		hasRunCheckExistingTask.current = true

		const projectId = await currentProjectIdState.getValue()
		const projects = await projectsState.getValue()
		const currentProject = projects.find((item) => item.id === projectId)

		// 如果没有项目 ID 或者项目创建时间在 30 秒内，则不检查任务
		if (!projectId || new Date(currentProject?.createdAt || 0).getTime() > Date.now() - 1000 * 30) {
			setIsCheckingTask(false)
			return
		}

		currentProjectRef.current = projectId // 设置当前项目 ID

		try {
			// const currentTabUrl = await getCommonService().getActiveTabURL() || "";
			// const supportPage = await getSupportPage(currentTabUrl)
			// if(!supportPage){
			// 	return setIsCheckingTask(false)
			// }
			const projectIdCurrent = await currentProjectIdState.getValue()
			if (!projectIdCurrent) {
				throw new Error('Cannot find project ID')
			}

			Similar.checkAndProcessTasks()
				.then(async (result) => {
					// 检查项目是否仍然是当前项目
					if (currentProjectRef.current !== projectIdCurrent) {
						// 项目已切换，忽略结果
						return
					}
					const { data, params } = result
					try {
						setFilters(params)
						setCollectStatistics((prev) => ({ ...prev, sourceKOL: params.source }))
						await handleSimilarResult(data, params.source)
						getModalService().show({
							content: 'You still have a “Find Similar” task in progress. Would you like to continue or end it?',
							type: 'warning',
							warningText: 'End Task',
							onWarning: () => {
								kolCollector.stop().then(() => {
									setInCollectMode(false)
								})
							},
							cancelText: 'Continue'
						})
					} catch (err) {
						toast.error('Failed to find similar influencers')
						console.error(err)
						setIsCheckingTask(false)
					}
				})
				.catch((err) => {
					if (typeof err === 'string' && err === 'Failed') {
						setCheckingTaskFailed(true)
					}

					setIsCheckingTask(false)
				})
		} catch (err) {
			toast.error('Check task failed')
			console.error(err)
			setIsCheckingTask(false)
		}
	}, [handleSimilarResult, setCollectStatistics, setFilters])

	const renderStartButton = useCallback(() => {
		if (!activeTabURL) {
			return null
		}

		return renderFindSimilarButton()
	}, [activeTabURL, renderFindSimilarButton])

	const resetState = useCallback(async () => {
		await kolCollector.stop(true)
		setInCollectMode(false)
		setIsCheckingTask(true)
		setCheckingTaskFailed(false)
		resetFindSimilar()
		setFilters(cloneDeep(DEFAULT_FILTER_OPTIONS))
		hasRunCheckExistingTask.current = false
		setCollectStatistics(cloneDeep(DEFAULT_COLLET_STATISTICS))
		currentProjectRef.current = null // 重置当前项目 ID
		checkExistingTask()
	}, [checkExistingTask, resetFindSimilar, setCollectStatistics, setFilters, setInCollectMode])

	useEffect(() => {
		if (projectId) {
			isWorkingState.setValue(false)
			resetState()
		}
	}, [projectId])

	const AIFilterGreyBg = () => {
		return (
			<div className="fixed top-0 left-0 w-full h-full bg-[#000]/50 z-50">
				{visionUrl && activeTabURL !== visionUrl && (
					<div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[999]">
						<dialog className="modal" open={true}>
							<div className="modal-box w-80">
								<h3 className="font-bold text-lg">Workflow Paused</h3>
								<p className="py-4">Please close tabs unrelated to similarity search during the filtering process</p>
								<div className="modal-action">
									<form method="dialog" className="flex gap-2">
										<button
											className="btn btn-primary btn-sm rounded-full"
											onClick={() => {
												getCommonService().activeTabUrl(visionUrl!)
											}}
										>
											Continue
										</button>
									</form>
								</div>
							</div>
						</dialog>
					</div>
				)}
			</div>
		)
	}

	const handleSubmitAIFilter = useCallback(
		async ({ cancel, ytbIds }: { cancel?: boolean; ytbIds: string[] }) => {
			// 收到就消息就关闭蒙层，无论是否是取消消息
			setShowAIFilter(false)
			if (cancel) return
			if (isFinding) return
			const elseFilters: Partial<SimilarFilters> = {}
			if (ytbIds.length > 0) {
				elseFilters.ytbVideoIds = ytbIds
			}
			log('提交 AI 筛选')
			await kolCollector.stop(true)
			await setInCollectMode(false)

			findSimilar({ currentSource: collectStatisticsProxyState.sourceKOL || undefined, elseFilters })
		},
		[findSimilar, isFinding, setInCollectMode]
	)

	useEffect(() => {
		const submitAIFilter = onMessage('submitAIFilter', async ({ data: { cancel, ytbIds } }) => {
			setVisionUrl(null)
			// 收到就消息就关闭蒙层，无论是否是取消消息
			handleSubmitAIFilter({ cancel, ytbIds })
		})
		return submitAIFilter
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	return (
		<>
			<div
				css={css`
					padding: 1rem;
					position: relative;
				`}
				className="h-full"
			>
				<div className="w-full flex justify-around mb-5">
					<ProjectSelect />
				</div>

				{inCollectMode ? (
					<>
						<KOLCollection />
					</>
				) : (
					<div className="flex flex-col gap-8 items-center justify-center min-h-full">{renderStartButton()}</div>
				)}
			</div>
			{showAIFilter && <AIFilterGreyBg />}
		</>
	)
}
