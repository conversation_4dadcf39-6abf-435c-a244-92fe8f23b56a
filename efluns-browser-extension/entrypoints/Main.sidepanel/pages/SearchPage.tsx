import { useMutation } from '@tanstack/react-query';
import AISearchForm from '../components/AISearchForm'
import Similar from '../models/Similar';
import { AI_SEARCH_CREATE } from '@/constants';
import { useAtom, useSetAtom } from 'jotai';
import { aiSearchCollectStatisticsState, DEFAULT_COLLET_STATISTICS, inAISearchCollectModeState, searchFormState } from '../store';
import { InQueueKOL } from '@/@types/kol';
import toast from 'react-hot-toast';
import { aiSearchKOLCollector } from '../models/AISearchKOLCollector';
import AISearchKOLCollection from '../components/AISearchKOLCollection';
import { useCallback, useEffect, useRef, useState } from 'react';
import { cloneDeep } from 'lodash-es';

const log = makeModuleLog('SearchPage')

export default function SearchPage() {

    const [inCollectMode, setInCollectMode] = useAtom(inAISearchCollectModeState)
    const setCollectStatistics = useSetAtom(aiSearchCollectStatisticsState)
    const currentProjectRef = useRef<string | null>(null)
    const [projectId] = useStorageState(currentProjectIdState)

    const [isCheckingTask, setIsCheckingTask] = useState(false);//检查当前是否发起过任务的状态
    const hasRunCheckExistingTask = useRef(false);//检查任务是否运行过

    const { mutateAsync: handleSearch, isPending: isFinding } = useMutation({
        mutationKey: [AI_SEARCH_CREATE],
        mutationFn: async (params: Omit<AiSearchParams, "projectId">) => {
            try {
                const { data } = await Similar.aiSearch(params)
                await handleSimilarResult(data, "")
            } catch (err) {
				if(err instanceof Error && err?.message){
					toast.error(err.message)
				}else{
					toast.error('AI search failed')
				}            
                log('Find AIsearch Err', err)
            }
        }
    })

    const handleSimilarResult = useCallback(async (data: InQueueKOL[], source: string) => {
        setCollectStatistics((prev) => ({ ...prev, sourceKOL: source }))

        await aiSearchKOLCollector.start({
            data,
            // onLoadMore: loadMore
        })
        setInCollectMode(true)
    }, [setCollectStatistics, setInCollectMode])

    const checkExistingTask = useCallback(async () => {
        if (hasRunCheckExistingTask.current) return;
        hasRunCheckExistingTask.current = true;

        const projectId = await currentProjectIdState.getValue();
        const projects = await projectsState.getValue();
        const currentProject = projects.find(item => item.id === projectId);
        
        // 如果没有项目ID或者项目创建时间在30秒内,则不检查任务
        if (!projectId || new Date(currentProject?.createdAt || 0).getTime() > Date.now() - 1000 * 30) {
            setIsCheckingTask(false);
            return;
        }

        currentProjectRef.current = projectId;

        try {
            const projectIdCurrent = await currentProjectIdState.getValue();
            if (!projectIdCurrent) {
                throw new Error('Cannot find project ID');
            }

            Similar.checkAndProcessTasks(true)
                .then(async (result) => {
                    // 检查项目是否仍然是当前项目
                    if (currentProjectRef.current !== projectIdCurrent) {
                        // 项目已切换，忽略结果
                        return;
                    }
                    const { data } = result;
                    await handleSimilarResult(data, "");
                })
                .finally(() => {
                    setIsCheckingTask(false);
                });
        } catch (err) {
            toast.error('Check task failed');
            console.error(err);
            setIsCheckingTask(false);
        }
    }, [handleSimilarResult]);

    // 重置状态
    const resetState = useCallback(async () => {
        await aiSearchKOLCollector.reset()
        setInCollectMode(false)
        setIsCheckingTask(true)
        setCollectStatistics(cloneDeep(DEFAULT_COLLET_STATISTICS))
        hasRunCheckExistingTask.current = false
        currentProjectRef.current = null
        checkExistingTask()
    }, [setInCollectMode, setCollectStatistics, checkExistingTask])

    useEffect(() => {
        resetState()
    }, [projectId])

    return (
        <div className="w-full max-w-3xl mx-auto p-4 h-full">
            {inCollectMode ? 
                <AISearchKOLCollection />
             : 
                <div className="h-full flex flex-col gap-8 items-center justify-center">
                    {isCheckingTask ? (
                        <div className="flex text-center  fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[90%]">
                            <span className="loading loading-spinner loading-sm mr-2"/>
                            <span>Searching for unfinished tasks, please wait...</span>
                        </div>
                    ) : (
                        <div className=" flex flex-col gap-8 items-center justify-center">
                            <AISearchForm submit={handleSearch} isFinding={isFinding}/>
                        </div>
                    )}
                </div>
            }
        </div>
    )
}