import { SimilarFilters } from '@/@types/similar'
import { atom } from 'jotai'
import { atomWithProxy } from 'jotai-valtio'
import { cloneDeep } from 'lodash-es'
import { proxy } from 'valtio'

export const isAutoSendEmailEnabledState = atom<boolean>(false)
export const isAiSearchSendEmailEnabledState = atom<boolean>(false)

export const inCollectModeState = atom<boolean>(false)
export const inAISearchCollectModeState = atom<boolean>(false)

export interface CollectStatistics {
	data: string[]
	index: number
	collected: number
	currentKOL: string | null
	sourceKOL: string | null
	sourceURL: string | null
	sentEmails: number
	isQueueUpdating: boolean
	taskAvatarList: {url: string; status: 'completed' | 'processing'}[],
	isInsReelMode: boolean,
	backUrl:string | null
}
export const DEFAULT_COLLET_STATISTICS: CollectStatistics = {
	data: [],
	index: 0,
	collected: 0,
	currentKOL: null,
	sourceKOL: null,
	sourceURL: null,
	sentEmails: 0,
	isQueueUpdating: false,
	taskAvatarList: [],
	isInsReelMode: false,
	backUrl:null
}

export const DEFAULT_SEARCH_FORM:Omit<AiSearchParams,"projectId"> = {
	platform:"",
	keywords:[],
	url:"",
	description:"",
	viewCountGte:1000
}

export const DEFAULT_FILTER_OPTIONS:SimilarFilters = {
	regions:[],
	minSubscribers: 10000,
	videosAverageViews: 1000,
	minAverageLikeCount:50,

	// similarScore: 0.7
}


//collect全局状态
export const collectStatisticsProxyState = proxy<CollectStatistics>(
	cloneDeep(DEFAULT_COLLET_STATISTICS) as CollectStatistics
)
export const collectStatisticsState = atomWithProxy(collectStatisticsProxyState)


//copy一份  是 AI搜索的全局状态
//collect全局状态
export const aiSearchCollectStatisticsProxyState = proxy<CollectStatistics>(
	cloneDeep(DEFAULT_COLLET_STATISTICS) as CollectStatistics
)
export const aiSearchCollectStatisticsState = atomWithProxy(aiSearchCollectStatisticsProxyState)


// filterForm全局状态
export const filtersProxyState = proxy<SimilarFilters>(
	cloneDeep(DEFAULT_FILTER_OPTIONS) as SimilarFilters
)

// 使用 atomWithProxy 创建 Jotai atom
export const filtersState = atomWithProxy(filtersProxyState)

// searchForm全局状态
export const searchFormProxyState = proxy<Omit<AiSearchParams,"projectId">>(
	cloneDeep(DEFAULT_SEARCH_FORM) as Omit<AiSearchParams,"projectId">
)
export const searchFormState = atomWithProxy(searchFormProxyState)


export const routerProxyState = proxy<{router:"find-similars" | "establish-contact"}>({router:"find-similars"})
export const routerState = atomWithProxy(routerProxyState)
