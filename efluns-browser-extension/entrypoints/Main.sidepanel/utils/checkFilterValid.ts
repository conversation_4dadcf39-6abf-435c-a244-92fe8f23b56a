import { SimilarFilters } from "@/@types/similar";
import { showErrModal } from "./showErrModal";

export const checkFilterValid = (filters: SimilarFilters) => {
   try {
    if(filters.averageLikes && filters.maxAverageLikes && filters.averageLikes > filters.maxAverageLikes){
        throw new Error('averageLikes must be less than maxAverageLikes')
    }else if(filters.maxSubscribers && filters.minSubscribers && filters.maxSubscribers < filters.minSubscribers){
        throw new Error('maxSubscribers must be greater than minSubscribers')
    }else if(filters.maxVideosAverageViews && filters.videosAverageViews && filters.maxVideosAverageViews < filters.videosAverageViews){
        throw new Error('maxVideosAverageViews must be greater than videosAverageViews')
    }
    return true

   } catch (error) {
    showErrModal(error instanceof Error ? error.message : 'Something wrong')
   }
   return false
}