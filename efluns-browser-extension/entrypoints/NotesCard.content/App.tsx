import { motion, useAnimationControls, useDragControls } from 'framer-motion'
import { startTransition, useEffect } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import { Toaster } from 'react-hot-toast'
import FallbackCard from './components/FallbackCard'
import NotesCard from './components/NotesCard'
import useSupportPageInfo from './hooks/useSupportPageInfo'

export default function App() {
	const dragControls = useDragControls()
	const { isInYoutubeKOLPage, isInTikTokKOLPage, isInInsKOLPage } = useSupportPageInfo()
	const containerAnimateControls = useAnimationControls()

	useEffect(() => {
		startTransition(() => {
			containerAnimateControls.start({
				opacity: 1,
				y: 0,
				transition: {
					duration: 0.3,
					ease: 'easeOut'
				}
			})
		})
	}, [containerAnimateControls])
	const isTikTokKOLPage =
		window.location.href.startsWith('https://www.tiktok.com/') && !window.location.href.includes('tiktok.com/@')
	if (isTikTokKOLPage) {
		return null
	}

	return (
		<>
			<Toaster
				position="top-center"
				toastOptions={{
					duration: 1500,
					style: {
						background: '#333',
						color: '#fff',
						fontSize: '16px',
						padding: '12px 24px',
						borderRadius: '8px',
						maxWidth: '500px'
					},
					success: {
						style: {
							background: '#10b981',
							color: '#fff'
						}
					},
					error: {
						style: {
							background: '#ef4444',
							color: '#fff'
						}
					}
				}}
			/>
			<motion.div
				drag
				dragControls={dragControls}
				dragMomentum={false}
				dragElastic={0}
				dragListener={false}
				className="z-[9000] fixed"
				initial={{
					opacity: 0,
					left: 2,
					y: -20
				}}
				animate={containerAnimateControls}
			>
				<ErrorBoundary
					fallbackRender={({ error, resetErrorBoundary }) => (
						<FallbackCard>
							<div className="text-red-500 text-sm">错误：{error.message}</div>
							<button className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm" onClick={resetErrorBoundary}>
								重试
							</button>
						</FallbackCard>
					)}
				>
					<NotesCard key={window.location.href} dragControls={dragControls} />
				</ErrorBoundary>
			</motion.div>
		</>
	)
}
