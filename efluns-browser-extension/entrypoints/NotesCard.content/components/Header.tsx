import { Minus, Settings } from 'lucide-react'
import { HeaderProps } from './types'

export default function Header({ dragControls, onMinimize }: HeaderProps) {
	return (
		<div
			className="flex items-center justify-between cursor-move p-6 pb-2"
			onPointerDown={(e) => dragControls.start(e)}
		>
			<div className="flex w-full justify-between items-center gap-2">
				<MinimizeButton onClick={onMinimize} />
				<button
					onClick={() => window.open('https://easykol.com/records', '_blank')}
					className="p-1 rounded-full cursor-pointer hover:bg-gray-100 transition-colors"
					title="查看记录"
				>
					<div className="relative group">
						<Settings className="text-gray-600 w-5 h-5 hover:text-gray-800" />
						<div className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
							creator list
							<div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-l-transparent border-r-transparent border-t-gray-800" />
						</div>
					</div>
				</button>
			</div>
		</div>
	)
}

function MinimizeButton({ onClick }: { onClick: () => void }) {
	return (
		<div className="relative mt-[2px] group">
			<button onClick={onClick} className="p-2 rounded-full hover:bg-gray-100 transition-colors">
				<Minus className="w-5 h-5 text-gray-600" />
			</button>
			<div className="absolute top-[-12px] mb-2 left-[-2px] bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
				最小化
				<div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-l-transparent border-r-transparent border-t-gray-800" />
			</div>
		</div>
	)
}