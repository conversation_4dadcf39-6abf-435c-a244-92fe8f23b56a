import { MemoSectionProps } from './types'

export default function MemoSection({ memo, onMemoChange }: MemoSectionProps) {
	return (
		<div className="relative bg-gradient-to-b from-white to-[#ebf7fa] rounded-xl p-6 pt-0">
			<div className="bg-white rounded-lg overflow-hidden border border-blue-100">
				<textarea
					value={memo}
					onChange={(e) => onMemoChange(e.target.value)}
					onKeyDown={(e) => e.stopPropagation()}
					onKeyUp={(e) => e.stopPropagation()}
					placeholder="Write memo ..."
					className="w-full h-36 bg-white text-base p-2 text-gray-700 resize-none focus:outline-none placeholder-gray-400 border-none"
				/>
			</div>
		</div>
	)
}