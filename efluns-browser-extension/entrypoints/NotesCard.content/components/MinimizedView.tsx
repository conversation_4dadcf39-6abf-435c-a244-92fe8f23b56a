import { motion } from 'framer-motion'
import MiniNotes from '~/assets/miniNote.png'
import { ANIMATION_CONFIG } from './constants'

interface MinimizedViewProps {
	onClick: () => void
}

export default function MinimizedView({ onClick }: MinimizedViewProps) {
	return (
		<motion.div
			initial={{ scale: 0.8, opacity: 0.8 }}
			animate={{ scale: 1, opacity: 1 }}
			transition={ANIMATION_CONFIG.spring}
			className="cursor-pointer w-16"
			onClick={onClick}
			title="展开笔记"
			style={{ aspectRatio: '138 / 99' }}
		>
			<img src={MiniNotes} alt="展开笔记" style={{ width: '100%', height: '100%' }} />
		</motion.div>
	)
}