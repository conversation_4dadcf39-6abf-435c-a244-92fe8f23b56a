import useStorageState from '@/hooks/useStorageState'
import { getTagService } from '@/services/TagService'
import { makeModuleLog } from '@/utils/log'
import { notesCardMinimizedState } from '@/utils/storages'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useState } from 'react'
import { useDebounce } from 'react-use'
import { Toaster } from 'sonner'
import { Tag, useNotesStorage } from '../hooks/useNotesStorage'
import Header from './Header'
import MemoSection from './MemoSection'
import MinimizedView from './MinimizedView'
import { ANIMATION_CONFIG, DEBOUNCE_DELAYS } from './constants'
import { useKolData } from './hooks/useKolData'
import { useTagsManagement } from './hooks/useTagsManagement'
import { useUserPermissions } from './hooks/useUserPermissions'
import ActiveTags from './tags/ActiveTags'
import AvailableTags from './tags/AvailableTags'
import { NoteData, NotesCardProps } from './types'

const log = makeModuleLog('NotesCard')

export default function NotesCard({ dragControls }: NotesCardProps) {
	// UI 状态
	const [isMinimized, setIsMinimized] = useStorageState(notesCardMinimizedState)
	const [isExpanded] = useState(true)

	// 业务数据
	const [currentNote, setCurrentNote] = useState<NoteData>({ memo: '', tags: [] })
	console.log(window.location.href, 'window.location.href')

	// Custom Hooks
	const { getCurrentNote, saveCurrentNote } = useNotesStorage()
	const { kolId } = useKolData()
	const { isEnterpriseUser, isAdmin } = useUserPermissions()
	const {
		availableTags,
		isAddingTag,
		newTagName,
		setNewTagName,
		isCreatingTag,
		deletingTagId,
		handleAddTag,
		handleDeleteTag,
		handleStartAddTag,
		handleCancelAdd,
		setIsAddingTag,
		setAvailableTags
	} = useTagsManagement(kolId)

	// 初始化
	useEffect(() => {
		const init = async () => {
			log('Initializing NotesCard for URL:', window.location.href)

			// Reset state when URL changes
			setCurrentNote({ memo: '', tags: [] })

			// 加载当前笔记（从本地存储）
			const note = getCurrentNote(window.location.href)
			log('Local note loaded:', note)

			if (note) {
				// 只加载本地存储的笔记内容，标签会从服务器加载
				setCurrentNote({ memo: note.content, tags: [] })
			}
		}
		init()
	}, [window.location.href])

	// 加载 KOL 数据
	useEffect(() => {
		if (!kolId) {
			log('No kolId, skipping KOL data load')
			return
		}

		const loadKolData = async () => {
			try {
				log('Loading KOL data for ID:', kolId)
				const tagService = getTagService()
				const serverData = await tagService.getKolNotesAndTags(kolId)
				log('Server data received:', serverData)

				if (!serverData) {
					log('No server data returned')
					return
				}

				const activeTags = serverData.tags.map((tag) => ({
					...tag,
					emoji: '',
					isActive: true
				}))
				const inactiveTags = availableTags
					.filter((t) => !serverData.tags.some((st) => st.id === t.id))
					.map((t) => ({ ...t, isActive: false }))

				log('Setting tags - active:', activeTags.length, 'inactive:', inactiveTags.length)
				setCurrentNote((prev) => ({
					memo: serverData.note || '',
					tags: [...activeTags, ...inactiveTags]
				}))
			} catch (error) {
				console.error('获取 KOL 笔记和标签失败：', error)
			}
		}
		loadKolData()
	}, [kolId, availableTags])

	// 自动保存到本地存储
	useDebounce(
		() => {
			const hasContent = currentNote.memo || currentNote.tags.some((t) => t.isActive)
			if (window.location.href && hasContent) {
				saveCurrentNote(window.location.href, currentNote.memo, currentNote.tags)
			}
		},
		DEBOUNCE_DELAYS.local,
		[currentNote]
	)

	// 自动同步笔记到服务器
	useDebounce(
		async () => {
			if (!kolId || !currentNote.memo) return

			try {
				const tagService = getTagService()
				await tagService.updateKolNotes(kolId, currentNote.memo)
			} catch (error) {
				console.error('同步笔记失败：', error)
			}
		},
		DEBOUNCE_DELAYS.server,
		[currentNote.memo, kolId]
	)

	// 处理标签点击
	const handleTagClick = async (tagId: string) => {
		log('handleTagClick - tagId:', tagId, 'kolId:', kolId)

		// 如果没有 kolId，说明页面还没加载完成，不允许操作
		if (!kolId) {
			log('Cannot handle tag click - KOL ID not loaded yet')
			return
		}

		const existingTag = currentNote.tags.find((t) => t.id === tagId)
		const isActivating = existingTag ? !existingTag.isActive : true
		log('Tag state - isActivating:', isActivating, 'existingTag:', existingTag)

		// 更新本地状态
		setCurrentNote((prev) => {
			if (existingTag) {
				// 切换现有标签的激活状态
				return {
					...prev,
					tags: prev.tags.map((tag) => (tag.id === tagId ? { ...tag, isActive: !tag.isActive } : tag))
				}
			} else {
				// 从可用标签中添加新标签
				const tagToAdd = availableTags.find((t) => t.id === tagId)
				if (tagToAdd) {
					return {
						...prev,
						tags: [...prev.tags, { ...tagToAdd, isActive: true }]
					}
				}
			}
			return prev
		})

		// 同步到服务器
		if (kolId) {
			try {
				const tagService = getTagService()
				const newActiveTags = isActivating
					? [...currentNote.tags.filter((t) => t.isActive && t.id !== tagId).map((t) => t.id), tagId]
					: currentNote.tags.filter((t) => t.isActive && t.id !== tagId).map((t) => t.id)

				await tagService.updateKolTags(kolId, newActiveTags)
			} catch (error) {
				console.error('同步标签失败：', error)
				// 回滚本地状态
				setCurrentNote((prev) => {
					if (existingTag) {
						return {
							...prev,
							tags: prev.tags.map((tag) => (tag.id === tagId ? { ...tag, isActive: existingTag.isActive } : tag))
						}
					}
					return prev
				})
			}
		}
	}

	// 处理添加新标签
	const handleAddTagWrapper = async () => {
		const newTag = await handleAddTag(currentNote.tags)
		if (newTag) {
			setCurrentNote((prev) => ({
				...prev,
				tags: [
					...prev.tags,
					{
						...newTag,
						isActive: true
					}
				]
			}))
			const newTagToAdd: Tag = {
				id: newTag.id,
				name: newTag.name,
				emoji: '',
				color: newTag.color,
				isActive: true
			}
			setAvailableTags((prev) => [...prev, newTagToAdd])
		}
	}

	// 处理删除标签
	const handleDeleteTagWrapper = async (tagId: string) => {
		const success = await handleDeleteTag(tagId)
		if (success) {
			setCurrentNote((prev) => ({
				...prev,
				tags: prev.tags.filter((t) => t.id !== tagId)
			}))
		}
	}

	// 处理键盘事件
	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			handleAddTagWrapper()
		} else if (e.key === 'Escape') {
			setIsAddingTag(false)
			setNewTagName('')
		}
	}

	// 计算派生状态
	const activeTags = currentNote.tags.filter((tag) => tag.isActive)
	const inactiveTags = currentNote.tags.filter((tag) => !tag.isActive)
	const unselectedTags = availableTags.filter((tag) => !currentNote.tags.some((t) => t.id === tag.id))
	log('unselectedTags', unselectedTags)
	// 处理开始添加标签
	const handleStartAddTagWrapper = () => {
		if (isEnterpriseUser && !isAdmin) {
			return
		}
		handleStartAddTag()
	}

	// 最小化视图
	if (isMinimized) {
		return <MinimizedView onClick={() => setIsMinimized(false)} />
	}

	// 主视图
	return (
		<motion.div
			initial={{ scale: 0.8, opacity: 0 }}
			animate={{ scale: 1, opacity: 1 }}
			exit={{ scale: 0.8, opacity: 0 }}
			transition={ANIMATION_CONFIG.spring}
			className="w-52 relative bg-white rounded-xl shadow-lg border border-gray-200  font-sans"
			onKeyDown={(e) => e.stopPropagation()}
			onKeyUp={(e) => e.stopPropagation()}
			onKeyPress={(e) => e.stopPropagation()}
		>
			<Header dragControls={dragControls} onMinimize={() => setIsMinimized(true)} />

			<AnimatePresence>
				{isExpanded && (
					<motion.div
						initial={{ opacity: 0, scaleY: 0 }}
						animate={{ opacity: 1, scaleY: 1 }}
						exit={{ opacity: 0, scaleY: 0 }}
						transition={ANIMATION_CONFIG.expand}
						style={{ originY: 0 }}
						// className="overflow-hidden"
					>
						{(!activeTags || activeTags?.length <= 0) && <div className="w-[80%] h-[0.5px] bg-[#e2e9fd] mx-auto" />}
						{activeTags.length > 0 && (
							<>
								<ActiveTags isEnterpriseUser={isEnterpriseUser} tags={activeTags} onTagClick={handleTagClick} />
								<div className="w-[80%] h-[0.5px] bg-[#e2e9fd] mx-auto" />
							</>
						)}

						<AvailableTags
							inactiveTags={inactiveTags}
							unselectedTags={unselectedTags}
							onTagClick={handleTagClick}
							onDeleteTag={handleDeleteTagWrapper}
							isAddingTag={isAddingTag}
							onStartAddTag={handleStartAddTagWrapper}
							newTagName={newTagName}
							onTagNameChange={setNewTagName}
							onKeyPress={handleKeyPress}
							onAddTag={handleAddTagWrapper}
							isCreatingTag={isCreatingTag}
							deletingTagId={deletingTagId}
							onCancelAdd={handleCancelAdd}
							isEnterpriseUser={isEnterpriseUser}
							isAdmin={isAdmin}
						/>

						<MemoSection
							isEnterpriseUser={isEnterpriseUser}
							memo={currentNote.memo}
							onMemoChange={(memo) => setCurrentNote((prev) => ({ ...prev, memo }))}
						/>
					</motion.div>
				)}
			</AnimatePresence>
			<Toaster position="top-center" richColors />
		</motion.div>
	)
}
