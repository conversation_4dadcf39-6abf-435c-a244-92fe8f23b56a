import { Platform } from '@/@types/platform'
import { getKolsIdService } from '@/services/KolsId'
import { getPlatformService } from '@/services/PlatformService'
import { makeModuleLog } from '@/utils/log'
import { useEffect, useState } from 'react'

const log = makeModuleLog('useKolData')

export function useKolData() {
	const [kolId, setKolId] = useState<string | null>(null)
	const [isLoading, setIsLoading] = useState(true)

	useEffect(() => {
		const fetchKolId = async () => {
			setIsLoading(true)
			setKolId(null)
			const platform = await getPlatformService().getCurrentPlatform()
			try {
				let id: string | null = null
				if (platform === Platform.YOUTUBE) {
					// TODO: 缓存获取 channelId 的逻辑
					// const routeChannelId = window.location.href.startsWith('https://www.youtube.com/channel/')
					// 	? window.location.href.split('/')?.[4]
					// 	: null
					// id = routeChannelId || (await getCurrentPageChannelId())
					// log(id, window.location.href, 'id')
					// if (!id) {
					// 	setIsLoading(false)
					// 	return
					// }
					const isChannelId = window.location.href.startsWith('https://www.youtube.com/channel/')
					id = window.location.href.split('/')?.[isChannelId ? 4 : 3]
				} else {
					id = window.location.href.split('/')?.[3]?.replace('@', '')
				}

				const kolsIdService = getKolsIdService()
				const res = await kolsIdService.getKolsId(platform as any, id)
				setKolId(res.kolId)
			} catch (error) {
				log('Error fetching KOL info:', error)
			} finally {
				setIsLoading(false)
			}
		}
		fetchKolId()
	}, [window.location.href])

	return { kolId, isLoading }
}
