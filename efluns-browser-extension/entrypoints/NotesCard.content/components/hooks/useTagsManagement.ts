import { useState, useEffect } from 'react'
import { getTagService } from '@/services/TagService'
import { sendMessage } from '@/utils/message'
import { Tag } from '../../hooks/useNotesStorage'
import { makeModuleLog } from '@/utils/log'
import { TAG_COLORS } from '../constants'
import toast from 'react-hot-toast'
import { tagsCacheState } from '@/utils/storages'

const log = makeModuleLog('useTagsManagement')

export function useTagsManagement(kolId: string | null) {
	const [availableTags, setAvailableTags] = useState<Tag[]>([])
	const [isAddingTag, setIsAddingTag] = useState(false)
	const [newTagName, setNewTagName] = useState('')
	const [isCreatingTag, setIsCreatingTag] = useState(false)
	const [deletingTagId, setDeletingTagId] = useState<string | null>(null)

	useEffect(() => {
		const initTags = async () => {
			try {
				// 先从缓存加载标签数据
				const cachedData = await tagsCacheState.getValue()
				if (cachedData?.tags) {
					setAvailableTags(cachedData.tags)
				}

				// 然后获取最新的服务器数据
				const tagService = getTagService()
				const serverTags = await tagService.getTagsList()
				const formattedTags: Tag[] = serverTags.map((tag) => ({
					id: tag.id,
					name: tag.name,
					emoji: '',
					color: tag.color,
					isActive: false
				}))

				// 比较缓存数据和服务器数据是否不同
				const isCacheDifferent = !cachedData?.tags || 
					cachedData.tags.length !== formattedTags.length ||
					!cachedData.tags.every((cachedTag, index) => {
						const serverTag = formattedTags[index]
						return cachedTag.id === serverTag.id && 
							   cachedTag.name === serverTag.name && 
							   cachedTag.color === serverTag.color
					})

				// 如果数据不同，更新缓存和状态
				if (isCacheDifferent) {
					setAvailableTags(formattedTags)
					await tagsCacheState.setValue({
						tags: formattedTags,
						cachedAt: new Date().toISOString()
					})
				}
			} catch (error) {
				log('Failed to get tags list:', error)
			}
		}
		initTags()
	}, [])

	const handleAddTag = async (currentNoteTags: Tag[]) => {
		const trimmedName = newTagName.trim()
		if (!trimmedName) return null

		const isDuplicate = availableTags.some((tag) => tag.name.toLowerCase() === trimmedName.toLowerCase())
		if (isDuplicate) {
			return null
		}

		setIsCreatingTag(true)

		try {
			const tagService = getTagService()
			const randomColor = TAG_COLORS[Math.floor(Math.random() * TAG_COLORS.length)]
			const createdTag = await tagService.createTag(trimmedName, randomColor)

			if (!createdTag) return null

			const newTag: Tag = {
				id: createdTag.id,
				name: createdTag.name,
				emoji: '',
				color: createdTag.color,
				isActive: true
			}

			setAvailableTags((prev) => [...prev, newTag])
			
			// 更新缓存
			const updatedTags = [...availableTags, newTag]
			await tagsCacheState.setValue({
				tags: updatedTags,
				cachedAt: new Date().toISOString()
			})

			if (kolId) {
				try {
					const activeTagIds = [...currentNoteTags.filter((t) => t.isActive).map((t) => t.id), newTag.id]
					await tagService.updateKolTags(kolId, activeTagIds)
				} catch (error) {
					log('Failed to associate tag to KOL:', error)
				}
			}

			setNewTagName('')
			setIsAddingTag(false)
			return newTag
		} catch (error: any) {
			if (error.message === 'Authorization header is required') {
				await sendMessage('openSidePanel', void 0)
			}
			toast.error('Failed to add tag' + error.message)
			return null
		} finally {
			setIsCreatingTag(false)
		}
	}

	const handleDeleteTag = async (tagId: string) => {
		const tagToDelete = availableTags.find((t) => t.id === tagId)
		if (!tagToDelete) {
			return false
		}

		const confirmed = confirm(`确定要删除标签 "${tagToDelete.name}" 吗？`)
		if (!confirmed) return false

		setDeletingTagId(tagId)

		try {
			const tagService = getTagService()
			const success = await tagService.deleteTag(tagId)

			if (success) {
				setAvailableTags((prev) => prev.filter((t) => t.id !== tagId))
				
				// 更新缓存
				const updatedTags = availableTags.filter((t) => t.id !== tagId)
				await tagsCacheState.setValue({
					tags: updatedTags,
					cachedAt: new Date().toISOString()
				})
				
				return true
			}
			return false
		} catch (error) {
			log('Failed to delete tag:', error)
			return false
		} finally {
			setDeletingTagId(null)
		}
	}

	const handleStartAddTag = () => {
		setIsAddingTag(true)
	}

	const handleCancelAdd = () => {
		if (!newTagName.trim() && !isCreatingTag) {
			setIsAddingTag(false)
		}
	}

	return {
		availableTags,
		isAddingTag,
		newTagName,
		setNewTagName,
		isCreatingTag,
		deletingTagId,
		handleAddTag,
		handleDeleteTag,
		handleStartAddTag,
		handleCancelAdd,
		setIsAddingTag,
		setAvailableTags
	}
}