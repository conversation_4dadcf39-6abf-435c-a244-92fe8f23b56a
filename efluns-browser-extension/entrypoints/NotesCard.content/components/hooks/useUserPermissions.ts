import { useEffect, useState } from 'react'
import { getUserService } from '@/services/UserService'
import { authSessionState } from '@/utils/storages'
import { makeModuleLog } from '@/utils/log'
import { UserPermissions } from '../types'

const log = makeModuleLog('useUserPermissions')

export function useUserPermissions() {
	const [permissions, setPermissions] = useState<UserPermissions>({
		isEnterpriseUser: false,
		isAdmin: false
	})
	const [isLoading, setIsLoading] = useState(true)

	useEffect(() => {
		const initUserPermissions = async () => {
			try {
				const session = await authSessionState.getValue()
				if (!session) {
					setIsLoading(false)
					return
				}

				const userService = getUserService()
				const userInfo = await userService.getUserInfo()
				if (!userInfo) {
					setIsLoading(false)
					return
				}

				const [isEnterprise, isAdmin] = await Promise.all([
					userService.isEnterpriseUser(userInfo),
					userService.isAdmin(userInfo)
				])

				setPermissions({ isEnterpriseUser: isEnterprise, isAdmin })
			} catch (error) {
				log('Failed to init user permissions:', error)
			} finally {
				setIsLoading(false)
			}
		}
		initUserPermissions()
	}, [])

	return { ...permissions, isLoading }
}