import { X } from 'lucide-react'
import { ActiveTagsProps } from '../types'

export default function ActiveTags({ isEnterpriseUser, tags, onTagClick }: ActiveTagsProps) {
	return (
		<div className="p-6 py-3 pt-2 cursor-pointer">
			<div className="flex flex-wrap gap-2">
				{tags.map((tag) => (
					<div
						key={tag.updatedBy?.email || tag.id}
						className="relative group px-2 py-1 rounded-full text-base font-medium bg-white/50 text-white flex items-center gap-2 transition-all duration-200"
						style={{ backgroundColor: tag.color }}
					>
						<span>{tag.emoji}</span>
						<span>{tag.name}</span>
						<button
							onClick={() => onTagClick(tag.id)}
							className="ml-1 p-0.5 rounded-full hover:bg-black/20 transition-colors"
						>
							<X className="w-3 h-3" />
						</button>
						{tag.updatedBy?.email && isEnterpriseUser && (
							<div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
								FROM: {tag.updatedBy.email}
								<div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-l-transparent border-r-transparent border-t-gray-800" />
							</div>
						)}
					</div>
				))}
			</div>
		</div>
	)
}