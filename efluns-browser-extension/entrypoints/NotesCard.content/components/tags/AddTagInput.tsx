import { Loader2 } from 'lucide-react'
import { AvailableTagsProps } from '../types'

interface AddTagInputProps extends Partial<AvailableTagsProps> {
	newTagName: string
	onTagNameChange: (name: string) => void
	onKeyPress: (e: React.KeyboardEvent) => void
	onAddTag: () => void
	isCreatingTag: boolean
	onCancelAdd: () => void
}

export default function AddTagInput({
	newTagName,
	onTagNameChange,
	onKeyPress,
	onAddTag,
	isCreatingTag,
	onCancelAdd
}: AddTagInputProps) {
	return (
		<div className="flex items-center gap-1">
			<input
				type="text"
				value={newTagName}
				onChange={(e) => onTagNameChange(e.target.value)}
				onKeyDown={(e) => {
					onKeyPress(e)
				}}
				onKeyUp={(e) => e.stopPropagation()}
				onBlur={onCancelAdd}
				placeholder="标签名"
				className="px-4 py-2 text-base border border-gray-300 rounded-md w-28 focus:outline-none focus:ring-2 focus:ring-blue-500"
				autoFocus
				disabled={isCreatingTag}
			/>
			<button
				onClick={onAddTag}
				className="p-1.5 rounded-full text-green-600 hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed"
				disabled={isCreatingTag || !newTagName?.trim()}
			>
				{isCreatingTag ? (
					<Loader2 className="h-5 w-5 animate-spin" />
				) : (
					<svg
						xmlns="http://www.w3.org/2000/svg"
						className="h-5 w-5"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
					</svg>
				)}
			</button>
		</div>
	)
}
