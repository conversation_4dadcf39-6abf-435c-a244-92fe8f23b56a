import { Plus } from 'lucide-react'
import { AvailableTagsProps } from '../types'
import AddTagInput from './AddTagInput'
import TagButton from './TagButton'

export default function AvailableTags(props: AvailableTagsProps) {
	const allTags = [...props.inactiveTags, ...props.unselectedTags]

	return (
		<div className="p-5 pt-3 cursor-pointer">
			<div className="flex flex-wrap gap-2">
				{allTags.map((tag) => (
					<TagButton
						key={tag.id}
						tag={tag}
						isDeleting={props.deletingTagId === tag.id}
						onTagClick={props.onTagClick}
						onDeleteTag={props.onDeleteTag}
						isEnterpriseUser={props.isEnterpriseUser}
						isAdmin={props.isAdmin}
					/>
				))}

				{props.isAddingTag ? (
					<AddTagInput {...props} />
				) : (
					<div className="relative group">
						<button
							onClick={props.onStartAddTag}
							className="px-2 py-1 rounded-full text-base text-gray-600 bg-gray-100 hover:bg-gray-200 transition-colors flex items-center gap-1.5"
						>
							<Plus className="w-5 h-5" />
							Add Tag
						</button>
						{props.isEnterpriseUser && !props.isAdmin && (
							<div className="absolute bottom-full mb-2 left-0 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
								{`Ask admin to add tags in "Record > Manage Tags"`}
								<div className="absolute top-full left-2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-l-transparent border-r-transparent border-t-gray-800" />
							</div>
						)}
					</div>
				)}
			</div>
		</div>
	)
}
