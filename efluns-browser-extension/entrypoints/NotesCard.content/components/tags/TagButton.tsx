import { useState } from 'react'
import { Loader2 } from 'lucide-react'
import { TagButtonProps } from '../types'

export default function TagButton({ tag, isDeleting, onTagClick }: TagButtonProps) {
	const [isHovered, setIsHovered] = useState(false)

	return (
		<div className="relative group">
			<button
				onClick={() => onTagClick(tag.id)}
				disabled={isDeleting}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				className={`px-2 py-1 rounded-full text-base justify-center font-medium transition-all duration-200 flex items-center gap-2 ${
					isDeleting ? 'opacity-50 cursor-not-allowed' : ''
				}`}
				style={{
					backgroundColor: isHovered ? tag.color : '#f3f4f6',
					color: isHovered ? 'white' : '#374151'
				}}
			>
				<span>{tag.emoji}</span>
				<span>{tag.name}</span>
				{isDeleting && <Loader2 className="w-4 h-4 animate-spin" />}
			</button>
		</div>
	)
}