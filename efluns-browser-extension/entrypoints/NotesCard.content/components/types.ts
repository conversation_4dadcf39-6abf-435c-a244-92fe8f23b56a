import { Tag } from '../hooks/useNotesStorage'
import type { DragControls } from 'framer-motion'

export interface NotesCardProps {
	dragControls: DragControls
	isMinimized?: boolean
	onMinimizeChange?: (minimized: boolean) => void
}

export interface NoteData {
	memo: string
	tags: Tag[]
}

export interface HeaderProps {
	dragControls: DragControls
	onMinimize: () => void
}

export interface ActiveTagsProps {
	isEnterpriseUser: boolean
	tags: Tag[]
	onTagClick: (id: string) => void
}

export interface AvailableTagsProps {
	inactiveTags: Tag[]
	unselectedTags: Tag[]
	onTagClick: (id: string) => void
	onDeleteTag: (id: string) => void
	isAddingTag: boolean
	onStartAddTag: () => void
	newTagName: string
	onTagNameChange: (name: string) => void
	onKeyPress: (e: React.KeyboardEvent) => void
	onAddTag: () => void
	isCreatingTag: boolean
	deletingTagId: string | null
	onCancelAdd: () => void
	isEnterpriseUser: boolean
	isAdmin: boolean
}

export interface MemoSectionProps {
	memo: string
	onMemoChange: (memo: string) => void
	isEnterpriseUser: boolean
	textareaRef?: React.RefObject<HTMLTextAreaElement>
}

export interface TagButtonProps {
	tag: Tag
	isDeleting: boolean
	onTagClick: (id: string) => void
	onDeleteTag: (id: string) => void
	isEnterpriseUser: boolean
	isAdmin: boolean
}

export interface UserPermissions {
	isEnterpriseUser: boolean
	isAdmin: boolean
}