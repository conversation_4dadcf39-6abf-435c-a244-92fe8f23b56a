@import url('~/assets/index.css');

/* NotesCard Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

#efluns-notes-app-root {
	color: #000;
}
#efluns-notes-app-root > div:first-child {
	width: 20%;
	top: 30% !important;
}
#efluns-notes-app-root.youtube-notes-card > div:first-child {
	width: 20% !important;
	top: 0 !important;
	left: -4% !important;
	scale: 0.625 ;
}
a {
	color: #000;
}

* {
	box-sizing: border-box;
}

body {
	font-family: 'Inter', sans-serif;
	margin: 0;
	padding: 0;
}

/* Custom scrollbar for textarea */
textarea::-webkit-scrollbar {
	width: 4px;
}

textarea::-webkit-scrollbar-track {
	background: #f1f5f9;
	border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb {
	background: #cbd5e1;
	border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb:hover {
	background: #94a3b8;
}

/* Focus styles */
.focus-ring:focus {
	outline: 2px solid #3b82f6;
	outline-offset: 2px;
}
