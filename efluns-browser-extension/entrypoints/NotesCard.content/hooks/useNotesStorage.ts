import useStorageState from '@/hooks/useStorageState'
import { notesState, type NotesItem, type NotesTag } from '@/utils/storages'
import { useCallback } from 'react'

export type Note = NotesItem
export type Tag = NotesTag

export function useNotesStorage() {
	const [note, setNote] = useStorageState(notesState)

	const getCurrentNote = useCallback(
		(url: string): Note | null => {
			return note.find((note) => note.url === url) || null
		},
		[note]
	)

	const saveCurrentNote = useCallback(
		async (url: string, content: string, tags: Tag[]) => {
			if (!url) return

			await setNote((prevNotes) => {
				const existingNoteIndex = prevNotes.findIndex((note) => note.url === url)
				const now = new Date().toISOString()
				const updatedNotes = [...prevNotes]

				if (existingNoteIndex >= 0) {
					// 更新现有笔记
					updatedNotes[existingNoteIndex] = {
						...updatedNotes[existingNoteIndex],
						content,
						tags,
						updatedAt: now
					}
				} else {
					// 创建新笔记
					const newNote: Note = {
						id: Date.now().toString(),
						url,
						content,
						tags,
						createdAt: now,
						updatedAt: now
					}
					updatedNotes.push(newNote)
				}

				return updatedNotes
			})
		},
		[setNote]
	)

	const deleteNote = useCallback(
		async (url: string) => {
			await setNote((prevNote) => prevNote.filter((note) => note.url !== url))
		},
		[setNote]
	)

	return {
		note,
		getCurrentNote,
		saveCurrentNote,
		deleteNote
	}
}
