import getSupportPage, { SupportPage } from '@/utils/getSupportPage'
import { useEffect, useState } from 'react'

export default function useSupportPageInfo() {
	const [supportPage, setSupportPage] = useState<SupportPage | null>(null)
	const [currentUrl, setCurrentUrl] = useState(window.location.href)

	// Monitor URL changes
	useEffect(() => {
		const checkUrlChange = () => {
			const newUrl = window.location.href
			if (newUrl !== currentUrl) {
				setCurrentUrl(newUrl)
			}
		}

		// Check URL on popstate events (browser back/forward)
		window.addEventListener('popstate', checkUrlChange)

		// Check URL periodically for programmatic navigation
		const intervalId = setInterval(checkUrlChange, 500)

		return () => {
			window.removeEventListener('popstate', checkUrlChange)
			clearInterval(intervalId)
		}
	}, [currentUrl])

	// Update support page when URL changes
	useEffect(() => {
		const getCurrentPage = async () => {
			const page = await getSupportPage(currentUrl)
			setSupportPage(page)
		}

		getCurrentPage()
	}, [currentUrl])

	return {
		isInYoutubeKOLPage: supportPage === SupportPage.YOUTUBE_KOL,
		isInTikTokKOLPage: supportPage === SupportPage.TIKTOK_KOL,
		isInInsKOLPage: supportPage === SupportPage.INS_KOL,
		supportPage
	}
}
