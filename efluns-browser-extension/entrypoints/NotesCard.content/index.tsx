import { Platform } from '@/@types/platform'
import { getPlatformService } from '@/services/PlatformService'
import { waitFor } from '@/utils/DOM'
import getSupportPage, { SupportPage } from '@/utils/getSupportPage'
import { makeModuleLog } from '@/utils/log'
import { sleep } from '@/utils/promise'
import type { EmotionCache } from '@emotion/cache'
import createCache from '@emotion/cache'
import { CacheProvider } from '@emotion/react'
import { Root, createRoot } from 'react-dom/client'
import '../../assets/content.css'
import App from './App'
import globalStyles from './global.css?inline'

const YOUTUBE_CONTAINER_SELECTOR = 'body'
const TIKTOK_CONTAINER_SELECTOR = 'body'
const INS_CONTAINER_SELECTOR = 'body'

const ANCHOR_ID = 'efluns-notes-anchor'
const APP_ROOT_ID = 'efluns-notes-app-root'
const log = makeModuleLog('NotesCard')

async function getContainer(): Promise<HTMLElement> {
	const supportPage = await getSupportPage(window.location.href)
	log(supportPage)

	let selector: string
	switch (supportPage) {
		case SupportPage.YOUTUBE_KOL:
			selector = YOUTUBE_CONTAINER_SELECTOR
			break
		case SupportPage.TIKTOK_KOL:
			selector = TIKTOK_CONTAINER_SELECTOR
			break
		case SupportPage.INS_KOL:
			selector = INS_CONTAINER_SELECTOR
			break
		default:
			// 默认注入到 body 中，或者可以抛出错误
			selector = 'body'
	}

	return waitFor(selector, { suppressLog: true })
}

async function getAnchor(): Promise<HTMLElement> {
	const container = await getContainer()
	let anchor = document.getElementById(ANCHOR_ID)
	const platform = await getPlatformService().getPlatform(window.location.href)
	const initPosition: Record<string, { left: string; top: string }> = {
		[Platform.YOUTUBE]: {
			left: '0',
			top: '132px'
		},
		[Platform.TIKTOK]: {
			left: '0',
			top: '438px'
		},
		[Platform.INS]: {
			left: '0',
			top: '376px'
		}
	}
	if (!anchor) {
		anchor = document.createElement('div')
		anchor.id = ANCHOR_ID
		anchor.style.position = 'fixed'
		anchor.style.left = initPosition?.[platform]?.left ?? '0'
		anchor.style.top = initPosition?.[platform]?.top ?? '0'
		anchor.style.zIndex = '9000'
		container.appendChild(anchor)
	}

	return anchor
}

let styleCache: EmotionCache | null = null
function prepareAppEnv(anchor: HTMLElement): Promise<{
	appRoot: HTMLElement
	styleCache: EmotionCache
}> {
	return new Promise((resolve, reject) => {
		const createShadowDOM = () => {
			const appRoot = document.createElement('div')
			const isYoutube = window.location.href.includes('youtube.com')
			if (isYoutube) {
				appRoot.classList.add('youtube-notes-card')
			}
			appRoot.id = APP_ROOT_ID

			/** Inject styles into shadow dom */
			const basicStyleElement = document.createElement('style')
			basicStyleElement.innerHTML = globalStyles

			const emotionStyleElement = document.createElement('style')
			styleCache = createCache({
				key: ANCHOR_ID,
				container: emotionStyleElement
			})
			styleCache.compat = true

			const html = document.createElement('html')
			const head = document.createElement('head')
			const body = document.createElement('body')

			// 获取当前页面的 font-size
			// 不同的页面 html 的 font-size 不一样，所以需要根据页面来调整，font-size 为 10 的时候展示最佳
			const currentFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)
			const targetFontSize = 10 // 目标 font-size

			// 计算缩放比例
			const scale = targetFontSize / currentFontSize

			// 应用缩放
			if (scale !== 1) {
				const scaleStyle = document.createElement('style')
				scaleStyle.textContent = `
					#${APP_ROOT_ID}>div{
						scale: ${scale};
						transform-origin: left top;
					}
				`
				head.appendChild(scaleStyle)
			}

			head.appendChild(basicStyleElement)
			head.appendChild(emotionStyleElement)
			body.appendChild(appRoot)
			html.appendChild(head)
			html.appendChild(body)
			return html
		}

		if (anchor.shadowRoot === null) {
			anchor.attachShadow({ mode: 'open' })
		}

		// Prevent Instagram's keyboard events from interfering with the notes card
		if (anchor.shadowRoot) {
			anchor.shadowRoot.addEventListener('keydown', (e: Event) => e.stopPropagation(), true)
			anchor.shadowRoot.addEventListener('keyup', (e: Event) => e.stopPropagation(), true)
		}

		let html: HTMLHtmlElement | null = null
		if (anchor.shadowRoot?.childElementCount === 0) {
			html = createShadowDOM()
			anchor.shadowRoot?.appendChild(html)
		} else {
			html = anchor.shadowRoot?.querySelector('html') ?? null
		}

		const appRoot = (html?.querySelector(`#${APP_ROOT_ID}`) as HTMLElement | null) ?? null

		if (!appRoot) {
			reject(new Error('appRoot not found'))
			return
		}
		if (!styleCache) {
			reject(new Error('styleCache not found'))
			return
		}

		resolve({
			appRoot,
			styleCache
		})
	})
}

let root: Root | null = null
async function renderApp() {
	log('renderApp')
	try {
		// 检查当前页面是否为支持的页面类型
		const supportPage = await getSupportPage(window.location.href)
		if (!supportPage) {
			log('Current page is not supported, skipping render')
			return
		}

		const anchor = await getAnchor()
		const { appRoot, styleCache } = await prepareAppEnv(anchor)

		if (!root) {
			root = createRoot(appRoot)
		}

		root.render(
			<CacheProvider value={styleCache}>
				<App />
			</CacheProvider>
		)
	} catch (error) {
		console.error('Efluns NotesCard: Failed to render app.', error)
	}
}

export default defineContentScript({
	matches: ['https://www.youtube.com/*', 'https://www.tiktok.com/*', 'https://www.instagram.com/*'],
	runAt: 'document_end',
	main() {
		renderApp()

		window.navigation?.addEventListener('navigate', () => {
			if (root) {
				root.unmount()
				root = null
			}
		})

		window.navigation?.addEventListener('navigatesuccess', () => {
			sleep(1000).then(async () => {
				// 检查新页面是否为支持的页面类型
				const supportPage = await getSupportPage(window.location.href)
				if (supportPage) {
					renderApp()
				} else {
					log('New page is not supported, skipping render')
				}
			})
		})
	}
})
