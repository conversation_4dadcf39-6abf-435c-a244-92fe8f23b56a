import getSupportPage from '@/utils/getSupportPage'
import { guard } from '@/utils/guard'
import { onMessage } from '@/utils/message'
import { HTMLAttributes, useEffect } from 'react'
import { getKOLHelper } from './utils'
import VideoSearchCard from '../KOLInfoCard.content/components/VideoSearchCard'

const log = makeModuleLog('TikTokServiceApp')

export interface AppProps extends HTMLAttributes<HTMLDivElement> {}

export default function App({ ...props }: AppProps) {
	const [isSearchCardOpen, setIsSearchCardOpen] = useState(false)
	useEffect(() => {
		const unsubs = onMessage('collectTikTokKOLs', async (msg) => {
			const isLoadMore = msg.data
			const doc = window.document
			const supportPage = guard(await getSupportPage(window.location.href), 'supportPage')
			const kolHelper = guard(getKOLHelper(supportPage), 'kolHelper')
			log('start collecting', { supportPage, kolHelper, isLoadMore })

			if (isLoadMore) {
				kolHelper.loadMore(doc)
				await sleep(3000)
			}

			return kolHelper.extract(doc)
		})

		return unsubs
	}, [])

	return <div className='absolute right-full mr-[10px] z-[9]' style={{background:'var(--brand-tiktok-background)'}}>
		<VideoSearchCard
			isOpen={isSearchCardOpen}
			onToggle={() => {setIsSearchCardOpen(!isSearchCardOpen)}} 
		/>
	</div>
}
