import queryClient from '@/utils/queryClient'
import createCache from '@emotion/cache'
import { CacheProvider } from '@emotion/react'
import { QueryClientProvider } from '@tanstack/react-query'
import { createRoot } from 'react-dom/client'
import '~/assets/index.css'
import App from './App'
import { tiktokRefreshBtnClick } from '../TiktokEarlyScript.content/utils'

async function patchPageElements() {
	await sleep(3000)
	const element = await waitFor('#main-content-others_homepage > div > div:nth-child(2) > div:nth-child(2)')

	if (element.textContent?.startsWith('Playlists')) {
		element.style.display = 'none'
	}
}

export default defineContentScript({
	matches: ['https://www.tiktok.com/search*', 'https://www.tiktok.com/tag/*', 'https://www.tiktok.com/@*'],
	runAt: 'document_end',
	cssInjectionMode: 'ui',
	async main(ctx) {
		const log = makeModuleLog('TikTokService')
		log('✅ TikTok Service is loading')
		// tiktokRefreshBtnClick()


		const buttonsContainer = await waitFor('.TUXSegmentedControl')

		const ui = await createShadowRootUi(ctx, {
			name: 'efluns-tiktok-service',
			position: 'inline',
			anchor: buttonsContainer,
			append: 'first',
			onMount: (body) => {
				// Create a root on the UI container and render a component
				const app = document.createElement('div')
				body.appendChild(app)
				const root = createRoot(app)

				const emotionStyleElement = document.createElement('style')
				const head = body.previousSibling
				head?.appendChild(emotionStyleElement)

				const styleCache = createCache({
					key: 'efluns-kol-collector',
					container: emotionStyleElement
				})

				root.render(
					<CacheProvider value={styleCache}>
						<QueryClientProvider client={queryClient}>
							<App />
						</QueryClientProvider>
					</CacheProvider>
				)

				return root
			},
			onRemove: (root) => {
				root?.unmount()
			}
		})

		// Call mount to add the UI to the DOM
		ui.mount()

		patchPageElements()

		log('✅ TikTok Service is runing')
		
	}
})
