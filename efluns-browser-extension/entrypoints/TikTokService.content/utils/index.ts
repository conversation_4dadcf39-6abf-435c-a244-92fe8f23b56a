import { SupportPage } from '@/utils/getSupportPage'

export function loadMoreSearchResultPageKOLs(doc: Document) {
	doc.documentElement.scrollTo({ top: doc.documentElement.scrollHeight })
}

export function extractSearchResultPageKOLs(doc: Document) {
	return Array.from(doc.querySelectorAll('[data-e2e="search-card-user-link"]')).map(
		(item) => window.location.origin + item.getAttribute('href')
	)
}

export function loadMoreTagPageKOLs(doc: Document): void {
	doc.documentElement.scrollTo({ top: doc.documentElement.scrollHeight })
}

export function extractTagPageKOLs(doc: Document): string[] {
	return Array.from(doc.querySelectorAll('[data-e2e="challenge-item-avatar"]')).map(
		(item) => window.location.origin + item.getAttribute('href')
	)
}

export interface KOLHelper {
	loadMore(doc: Document): void
	extract(doc: Document): string[]
}

const KOL_HELPER_MAP = {
	[SupportPage.TIKTOK_SEARCH]: {
		loadMore: loadMoreSearchResultPageKOLs,
		extract: extractSearchResultPageKOLs
	},
	[SupportPage.TIKTOK_TAG]: {
		loadMore: loadMoreTagPageKOLs,
		extract: extractTagPageKOLs
	}
} as Record<SupportPage, KOLHelper>

export function getKOLHelper(page: SupportPage): KOLHelper | undefined {
	return KOL_HELPER_MAP[page]
}
