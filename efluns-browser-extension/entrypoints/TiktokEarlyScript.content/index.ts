import { TiktokChannelData } from "@/@types";
import { parseTiktokVideoDuration, sendTiktokVideoData } from "./utils";
import "@/assets/tiktok.css";
import { observeTiktokCards, observeTiktokTags } from "./utils/changeTiktokCardDataStyle";
import { testAdDescription } from "@/utils/regular";

export default defineContentScript({
    matches: ['https://www.tiktok.com/*'],
    runAt: 'document_start',
    world: 'MAIN',
    main() {
        console.log('TikTok Early Script is loading');
        let cleanup: (() => void) | undefined;
        let cleanupTag: (() => void) | undefined;

        // 等待 document.body 可用
        const startObserver = () => {
            if (document.body) {
                cleanup = observeTiktokCards();
                cleanupTag = observeTiktokTags();
            } else {
                document.addEventListener('DOMContentLoaded', () => {
                    cleanup = observeTiktokCards();
                    cleanupTag = observeTiktokTags();
                }, { once: true });
            }
        };
        
        startObserver();
        
        // 拦截原始的fetch函数
        const originalFetch = window.fetch;
        window.fetch = async function(...args) {
            const [resource] = args;
            // 调用原始fetch
            const response = await originalFetch.apply(this, args);
            
            // 检查URL是否匹配目标API
            const isSupportPage = location.href.startsWith('https://www.tiktok.com/@')
            const isSupportTagPage = location.href.startsWith('https://www.tiktok.com/tag/')

            
            // @ts-expect-error 这里的类型中 resource里有url
            // tt博主首页
            if (resource?.url?.includes('https://www.tiktok.com/api/post/item_list/') && isSupportPage) {
                // 克隆响应以避免消耗原始响应
                const clone = response.clone();
                
                try {
                    const data = await clone.json();
                    if (data && data.itemList) {
                        const datas = data.itemList.map((e:any)=>({
                            id:e.id,
                            ...e.stats,
                            created_time:e.createTime,
                            duration:parseTiktokVideoDuration(e?.video?.duration),isPinned:e.isPinnedItem,
                            is_paid_partnership:!!e?.adLabelVersion || testAdDescription(e?.desc),
                            is_shop:e?.isECVideo,
                            description:e?.contents?.[0]?.desc,
                            userList: e?.contents?.flatMap(
                                (contentItem: any) => contentItem?.textExtra
                                  ?.filter((extraItem: any) => extraItem?.type === 0 && extraItem.userUniqueId)
                                  .map((extraItem: any) => extraItem.userUniqueId) || []
                              ) || []
                        }))
                        // collectTiktokData(datas)

                        //排除掉置顶的，但是没必要了，因为后面会筛选时间
                        // const tiktokVideoData:TiktokCardData[] = data.itemList.filter((e:any)=>!e.isPinnedItem
                        //     ).map((e:any)=>({id:e.id,...e.stats,created_time:e.createTime}))

                        const tiktokChennelData:TiktokChannelData = {
                            videos: datas,
                            // @ts-expect-error 获取url中的cursor
                            isFirst:new URL(resource.url).searchParams.get('cursor') === "0"
                        }
                        sendTiktokVideoData(tiktokChennelData,window.location.href);
                        // console.log('itemList:', tiktokChennelData);
                    }
                } catch (error) {
                    console.error('解析响应时出错:', error);
                }
            }
            // @ts-expect-error 这里的类型中 resource里有url //tt tag页面
            else if(resource?.url?.includes('https://www.tiktok.com/api/challenge/item_list/') && isSupportTagPage){
                const clone = response.clone();
                try {
                    const data = await clone.json();
                    if (data && data.itemList) {
                        const datas = data.itemList.map((e:any)=>({
                            id:e.id,
                            ...e.stats,
                            created_time:e.createTime,
                            duration:parseTiktokVideoDuration(e?.video?.duration),isPinned:e.isPinnedItem,
                            is_paid_partnership:!!e?.adLabelVersion || testAdDescription(e?.desc),
                            is_shop:e?.isECVideo
                        }))
                        // collectTiktokData(datas)

                        //排除掉置顶的，但是没必要了，因为后面会筛选时间
                        // const tiktokVideoData:TiktokCardData[] = data.itemList.filter((e:any)=>!e.isPinnedItem
                        //     ).map((e:any)=>({id:e.id,...e.stats,created_time:e.createTime}))

                        const tiktokChennelData:TiktokChannelData = {
                            videos: datas,
                            // @ts-expect-error 获取url中的cursor
                            isFirst:new URL(resource.url).searchParams.get('cursor') === "0"
                        }
                        sendTiktokVideoData(tiktokChennelData,window.location.href);
                        console.log('tagList:', tiktokChennelData);
                    }
                } catch (error) {
                    console.error('解析响应时出错:', error);
                }
            }
            
            return response;
        };
        return () => {
            cleanup?.();
            cleanupTag?.();
        };
    }
})

