import { Tiktok<PERSON>ardData, TiktokChannelData } from "@/@types";
import { formatToTime } from "@/utils/day"
import { getTiktokDataById, getTiktokVideoData } from ".";
import { findParentElement } from "@/utils/DOM";

const log = makeModuleLog("tiktokDataCollect")


function formatNumber(num: number): string {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

const ICONS = {
    play:`<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.5446 11.085L5.92771 1.25462C5.56918 0.972722 5.08087 0.920508 4.67071 1.11995C4.26036 1.31936 4 1.73551 4 2.19163V21.8523C4 22.3085 4.26036 22.7246 4.67071 22.924C4.83606 23.0045 5.01427 23.0439 5.19141 23.0439C5.45352 23.0439 5.71373 22.9575 5.92771 22.7893L20.5446 12.959C20.8321 12.733 21 12.3876 21 12.022C21 11.6563 20.8322 11.311 20.5446 11.085ZM6.38315 19.4007V4.64328L17.8797 12.022L6.38315 19.4007Z" fill="white"/>
</svg>`,
    like:`<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.63813 3.20144C6.59863 3.20144 5.57364 3.49452 4.66565 4.05029C1.85545 5.7728 0.866469 9.61848 2.49875 12.6197C2.80417 13.1817 3.35116 13.8567 4.0535 14.5879C4.75193 15.3151 5.58794 16.0817 6.45558 16.8267C8.19068 18.3166 10.037 19.7074 11.1279 20.5061L11.1282 20.5064C11.3896 20.6982 11.6967 20.7986 12.0193 20.7986C12.3412 20.7986 12.649 20.6985 12.9161 20.5055C14.0071 19.7067 15.8529 18.3162 17.5875 16.8267C18.4552 16.0817 19.2912 15.3151 19.9896 14.5879C20.692 13.8567 21.2389 13.1818 21.5443 12.6198C23.1722 9.61839 22.183 5.7726 19.3728 4.05018C18.4648 3.49448 17.4399 3.20144 16.4005 3.20144C14.843 3.20144 13.3771 3.82992 12.275 4.9555L12.0193 5.21661L11.7636 4.9555C10.6615 3.82992 9.19558 3.20144 7.63813 3.20144ZM4.29084 3.45423C5.30783 2.83175 6.46207 2.5 7.63813 2.5C9.27603 2.5 10.8198 3.11753 12.0193 4.22035C13.2187 3.11753 14.7626 2.5 16.4005 2.5C17.5765 2.5 18.7308 2.83175 19.7477 3.45423C22.894 5.38255 23.9662 9.64164 22.171 12.951C21.8204 13.5962 21.2214 14.3252 20.5057 15.0704C19.7861 15.8197 18.9318 16.6023 18.0543 17.3558C16.2992 18.8628 14.4363 20.2659 13.3389 21.0694L13.3373 21.0706C12.9508 21.3503 12.4961 21.5 12.0193 21.5C11.5429 21.5 11.0872 21.3505 10.7039 21.0692C9.60646 20.2657 7.74373 18.8627 5.98881 17.3558C5.11136 16.6023 4.25705 15.8197 3.53742 15.0704C2.82175 14.3253 2.2229 13.5965 1.87227 12.9513M4.29084 3.45423C1.14464 5.38254 0.0723718 9.64183 1.87227 12.9513ZM5.01286 4.58776C5.81169 4.09935 6.71614 3.83892 7.63813 3.83892C9.25547 3.83892 10.7518 4.61084 11.7587 5.93387L11.7598 5.93521L12.0193 6.27963L12.2788 5.93522L12.2798 5.93387C13.2869 4.61069 14.7878 3.83892 16.4005 3.83892C17.3222 3.83892 18.2269 4.09921 19.0217 4.58807C21.575 6.1517 22.4306 9.62171 20.9632 12.3176C20.6344 12.9218 20.0348 13.6271 19.3199 14.351C18.5995 15.0805 17.7407 15.8499 16.8729 16.5833C15.1369 18.0503 13.3486 19.3869 12.522 19.9936L12.5202 19.9949C12.2225 20.2105 11.8115 20.2105 11.5138 19.9949L11.5125 19.9939C10.6855 19.3893 8.89718 18.0537 7.16114 16.5866C6.29328 15.8532 5.43451 15.0835 4.71407 14.3535C3.99918 13.629 3.39969 12.923 3.0709 12.3177M5.38771 5.18379C3.1737 6.54377 2.39555 9.59436 3.69727 11.9858C3.97689 12.5007 4.51817 13.1494 5.22349 13.8641C5.92338 14.5734 6.76459 15.3279 7.62366 16.0539C9.34084 17.5051 11.1135 18.8291 11.9349 19.4297C11.982 19.4635 12.0514 19.4637 12.0986 19.4301C12.9205 18.8269 14.6932 17.5017 16.4104 16.0505C17.2695 15.3245 18.1107 14.5703 18.8106 13.8615C19.516 13.1473 20.0572 12.4997 20.3368 11.9859C21.6387 9.594 20.86 6.53918 18.6465 5.18388L18.6458 5.18349C17.9607 4.76201 17.1857 4.54036 16.4005 4.54036C15.026 4.54036 13.7305 5.19651 12.8489 6.35437C12.8487 6.3546 12.8485 6.35483 12.8484 6.35506L12.0193 7.45533L11.1902 6.35506C11.19 6.35481 11.1898 6.35457 11.1897 6.35432C10.308 5.19634 9.01694 4.54036 7.63813 4.54036C6.85318 4.54036 6.07794 4.76187 5.38771 5.18379ZM5.01286 4.58776L5.01225 4.58813ZM5.01225 4.58813C2.45944 6.15598 1.60335 9.62136 3.0709 12.3177Z" fill="white"/>
<path d="M12.0182 21.1209C11.6196 21.1209 11.2391 20.9963 10.9176 20.7605C8.73434 19.1634 3.51636 15.1906 2.20734 12.7838C0.495192 9.63856 1.52339 5.59906 4.49473 3.77951C5.45498 3.19227 6.54206 2.88086 7.64726 2.88086C9.30052 2.88086 10.8541 3.54818 12.0182 4.736C13.1823 3.54818 14.7359 2.88086 16.3892 2.88086C17.4944 2.88086 18.5815 3.19227 19.5417 3.77951C22.513 5.59906 23.5412 9.63856 21.8336 12.7838C20.5246 15.1906 15.3066 19.1634 13.1234 20.7605C12.7973 20.9963 12.4168 21.1209 12.0182 21.1209ZM7.64726 4.21549C6.79571 4.21549 5.95776 4.45573 5.21492 4.9095C2.83694 6.3687 2.02163 9.61631 3.40313 12.1521C4.61703 14.3854 10.0796 18.4872 11.7238 19.6883C11.8959 19.8129 12.136 19.8129 12.3081 19.6883C13.9523 18.4827 19.4149 14.381 20.6288 12.1521C22.0103 9.61631 21.195 6.36425 18.817 4.9095C18.0787 4.45573 17.2407 4.21549 16.3892 4.21549C14.899 4.21549 13.5039 4.9273 12.5618 6.16406L12.0182 6.88476L11.4747 6.16406C10.5325 4.9273 9.14199 4.21549 7.64726 4.21549Z" fill="white"/>
</svg>`,
    comment:`<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.2965 21.1588L20.2182 17.1373C20.1563 16.9119 20.1873 16.6689 20.2933 16.4568C21.2346 14.5698 21.5484 12.3159 20.9695 9.96492C20.1829 6.78308 17.6993 4.22876 14.5262 3.38469C13.6601 3.15931 12.8072 3.04883 11.9808 3.04883C6.05017 3.04883 1.41882 8.61263 3.01858 14.804C3.76101 17.6676 6.81911 20.6329 9.69603 21.3312C10.4738 21.5212 11.2383 21.6096 11.9808 21.6096C13.5761 21.6096 15.0698 21.1986 16.3823 20.4915C16.5282 20.412 16.6917 20.3678 16.8508 20.3678C16.9303 20.3678 17.0098 20.3766 17.0894 20.3987L21.0048 21.4461C21.0269 21.4505 21.049 21.4549 21.0667 21.4549C21.217 21.4549 21.3363 21.3135 21.2965 21.1588ZM18.9808 17.4688L19.5907 19.7447L17.4208 19.1657C17.2352 19.1171 17.0452 19.0906 16.8508 19.0906C16.4795 19.0906 16.1039 19.1878 15.768 19.369C14.5881 20.0054 13.3154 20.328 11.9808 20.328C11.3223 20.328 10.655 20.244 9.99654 20.0849C7.56154 19.4928 4.87907 16.8766 4.26038 14.4814C3.61517 11.9801 4.12338 9.41252 5.65244 7.43712C7.18149 5.46173 9.48833 4.3304 11.9808 4.3304C12.7144 4.3304 13.4612 4.42763 14.1992 4.62649C16.9259 5.35125 19.0427 7.51667 19.7277 10.2743C20.2094 12.2187 20.0105 14.1588 19.1488 15.8867C18.9013 16.3816 18.8394 16.9429 18.9808 17.4688Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.35972 14.7152C1.81948 8.75394 6.27448 3.40489 11.9775 3.40489C12.7718 3.40489 13.5943 3.51103 14.4324 3.72903C17.4781 4.53959 19.8651 6.99362 20.6208 10.0506C21.1792 12.3184 20.8758 14.4857 19.9718 16.298C19.8305 16.5806 19.7847 16.9132 19.8716 17.2307C19.8717 17.2311 19.8718 17.2314 19.8719 17.2318L20.8956 21.0497L17.1813 20.0562C17.1807 20.0561 17.1802 20.0559 17.1796 20.0557C17.0647 20.024 16.9531 20.0123 16.8475 20.0123C16.6243 20.0123 16.403 20.0737 16.2095 20.1791C14.9461 20.8595 13.5104 21.2541 11.9775 21.2541C11.2635 21.2541 10.5273 21.1691 9.77718 20.9859L9.77665 20.9857C8.41549 20.6554 6.986 19.7797 5.80195 18.6252C4.61778 17.4706 3.71043 16.0677 3.35972 14.7152ZM11.9775 4.68646C9.59523 4.68646 7.39222 5.76674 5.93048 7.65518C4.47131 9.5403 3.9828 11.9938 4.60155 14.3927C4.88987 15.5089 5.67088 16.7075 6.6926 17.705C7.71393 18.7023 8.93902 19.4627 10.0771 19.7395C10.7101 19.8924 11.3492 19.9725 11.9775 19.9725C13.2528 19.9725 14.4675 19.6648 15.5959 19.0562C15.9816 18.8481 16.415 18.7352 16.8475 18.7352C17.0746 18.7352 17.2949 18.7662 17.5077 18.8219L17.5093 18.8223L19.0845 19.2426L18.634 17.5614C18.4691 16.9484 18.5423 16.2978 18.8273 15.7279C19.6495 14.079 19.8413 12.2261 19.3791 10.3603C18.725 7.72715 16.7044 5.66167 14.1045 4.97061L14.1034 4.9703C13.3954 4.77953 12.6797 4.68646 11.9775 4.68646Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.9775 2.69336C5.81931 2.69336 1.01163 8.47186 2.67084 14.8933C3.06259 16.4043 4.05555 17.9161 5.30522 19.1346C6.55491 20.3531 8.09273 21.3091 9.60831 21.6771C10.4138 21.8739 11.2067 21.9656 11.9775 21.9656C13.635 21.9656 15.1863 21.5385 16.5478 20.805L16.5494 20.8041C16.6474 20.7507 16.7527 20.7238 16.8475 20.7238C16.9004 20.7238 16.9473 20.7297 16.9909 20.7418L20.9206 21.793L20.9318 21.7952L20.9349 21.7959C20.9511 21.7992 21.0074 21.811 21.0634 21.811C21.4352 21.811 21.7381 21.4607 21.6378 21.0705L20.5586 17.0455L20.558 17.0434C20.5215 16.9106 20.5378 16.7572 20.6084 16.6159C21.5869 14.6543 21.9109 12.3138 21.3116 9.87983C20.4941 6.57341 17.9143 3.91899 14.6144 3.04117L14.6125 3.04069C13.7187 2.8081 12.8357 2.69336 11.9775 2.69336ZM11.9775 3.40489C6.27448 3.40489 1.81948 8.75394 3.35972 14.7152C3.71043 16.0677 4.61778 17.4706 5.80195 18.6252C6.986 19.7797 8.41549 20.6554 9.77665 20.9857L9.77718 20.9859C10.5273 21.1691 11.2635 21.2541 11.9775 21.2541C13.5104 21.2541 14.9461 20.8595 16.2095 20.1791C16.403 20.0737 16.6243 20.0123 16.8475 20.0123C16.9531 20.0123 17.0647 20.024 17.1796 20.0557L17.1813 20.0562L20.8956 21.0497L19.8719 17.2318L19.8716 17.2307C19.7847 16.9132 19.8305 16.5806 19.9718 16.298C20.8758 14.4857 21.1792 12.3184 20.6208 10.0506C19.8651 6.99362 17.4781 4.53959 14.4324 3.72903C13.5943 3.51103 12.7718 3.40489 11.9775 3.40489Z" fill="white"/>
</svg>`,
    collect:`<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.4466 2.41602H4.55946C3.97461 2.41602 3.49609 2.84701 3.49609 3.37969V21.043C3.49609 21.5757 3.84169 21.7113 4.27235 21.3384L11.5724 15.0333C11.7425 14.8881 12.0243 14.8881 12.1944 15.0285L19.7284 21.3529C20.1591 21.7161 20.51 21.5757 20.51 21.043V3.37969C20.51 2.84701 20.0368 2.41602 19.4466 2.41602ZM18.9149 18.6992L13.2738 13.9631C12.8803 13.6338 12.3859 13.4692 11.8861 13.4692C11.3757 13.4692 10.8706 13.6387 10.4771 13.9777L5.09115 18.6314V3.86879H18.9149V18.6992Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.14844 3.37105C3.14844 2.61695 3.81306 2.07031 4.54522 2.07031H19.46C20.1986 2.07031 20.8568 2.61798 20.8568 3.37105V21.0544C20.8568 21.3728 20.7495 21.7264 20.4166 21.8587C20.1027 21.9834 19.7764 21.8303 19.5307 21.6233L11.9852 15.2941C11.9367 15.2549 11.8324 15.2552 11.785 15.295L4.47272 21.6057C4.23036 21.8154 3.90548 21.9777 3.58957 21.8565C3.25235 21.727 3.14844 21.3688 3.14844 21.0544V3.37105ZM4.54522 2.74238C4.1055 2.74238 3.81132 3.05864 3.81132 3.37105V21.0544C3.81132 21.1423 3.82426 21.195 3.83482 21.2219C3.83899 21.2208 3.84382 21.2194 3.84935 21.2176C3.89095 21.2035 3.95681 21.1686 4.04243 21.0945L11.3563 14.7824L11.3577 14.7812C11.6513 14.5308 12.111 14.5315 12.4035 14.7728L12.4056 14.7745L19.9542 21.1062C20.0388 21.1776 20.1041 21.2107 20.1454 21.2238C20.1542 21.2266 20.1613 21.2283 20.1669 21.2293C20.1782 21.2045 20.1939 21.1502 20.1939 21.0544V3.37105C20.1939 3.05762 19.9039 2.74238 19.46 2.74238H4.54522ZM4.74645 3.52465H19.2588V19.4223L13.0648 14.2262C12.7346 13.9501 12.3152 13.8085 11.8854 13.8085C11.4449 13.8085 11.017 13.9547 10.6884 14.2376L4.74645 19.3677V3.52465ZM5.40933 4.19671V17.9127L10.2593 13.7253C10.7191 13.3296 11.3032 13.1364 11.8854 13.1364C12.457 13.1364 13.0285 13.3246 13.4866 13.7077L18.5959 17.9939V4.19671H5.40933Z" fill="white"/>
</svg>`,
    er:`<svg width="16" height="10" viewBox="0 0 24 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.799988 0.75V18.25H10.2922V15.9771H3.06317V10.6194H9.71356V8.35498H3.06317V3.02295H10.2043V0.75H0.799988ZM12.1574 0.75V18.25H14.4206V11.6277H17.4968C17.5457 11.6277 17.5943 11.6273 17.6426 11.6265L20.6901 18.25H23.2683L19.9485 11.1774C20.1022 11.11 20.2494 11.0351 20.3898 10.9526C21.1613 10.5026 21.7375 9.87313 22.1184 9.06421C22.5041 8.24959 22.697 7.3068 22.697 6.23584C22.697 5.15918 22.5065 4.21069 22.1257 3.39038C21.7448 2.56437 21.1687 1.91781 20.3972 1.45068C19.6306 0.983561 18.6662 0.75 17.5041 0.75H12.1574ZM14.4206 9.35474H17.2917C18.0241 9.35474 18.6198 9.23226 19.0788 8.98731C19.5427 8.74235 19.882 8.38631 20.0969 7.91919C20.3117 7.45207 20.4191 6.89095 20.4191 6.23584C20.4191 5.57503 20.3093 5.00252 20.0896 4.51831C19.8747 4.0341 19.5354 3.66382 19.0715 3.40747C18.6125 3.14543 18.0095 3.0144 17.2624 3.0144H14.4206V9.35474Z" fill="white"/>
</svg>`
};

function createElementStr(tiktokCardData: TiktokCardData) {
    return `
        <div class="__video__stat __efluns_er">${ICONS.er}${computeEr(tiktokCardData)}</div>
        <div class="__video__stat_list">
            <span class="__video__stat __efluns_plays">${ICONS.play}${formatNumber(tiktokCardData.playCount)}</span>
            <span class="__video__stat __efluns_likes">${ICONS.like}${formatNumber(tiktokCardData.diggCount)}</span>
            <span class="__video__stat __efluns_comments">${ICONS.comment}${formatNumber(tiktokCardData.commentCount)}</span>
            
       </div>
    `;
}

function computeEr(tiktokCardData: TiktokCardData) {
    const er = (tiktokCardData.diggCount + tiktokCardData.commentCount) / tiktokCardData.playCount * 100;
    return er.toFixed(1) + '%';
}

function createCreatedTime(tiktokCardData: TiktokCardData) {
    return `<div class="__efluns_created_time"><span>${formatTimeAgo(tiktokCardData.created_time)}</span></div>`
}

function createDayTime(tiktokCardData: TiktokCardData) {
    return `<span class="__efluns_duraction">${tiktokCardData.duration}</span>`
}

function createPostTags(str:string){
    return `<div class="__efluns_post_tags">${str}</div>`
}

// 修改：监听DOM变化并注入数据
export function observeTiktokCards() {
    const observer = new MutationObserver((mutations) => {
        mutations.forEach(mutation => {
            mutation.addedNodes.forEach(node => {
                if (node instanceof HTMLElement) {
                    const cards = node.querySelectorAll('[data-e2e=user-post-item-list] [href*="/video/"]:not(:has(._video__stat_list))');
                    cards.forEach(async (card) => {
                        const href = (card as HTMLElement).getAttribute('href');
                        if (!href) return;
                        
                        const videoId = href.split('/video/').pop()?.split('?')[0];
                        if (!videoId) return;
                        
                        const data = getTiktokDataById(videoId);
                        if (data) {
                            updateCardStyle(card as HTMLElement, data);
                        
                            // const tags = data?.description
                            // if(tags){
                            //     const parent = findParentElement(card,"[class*=DivItemContainer]")
                            //     if(parent){
                            //         parent.insertAdjacentHTML('beforeend', createPostTags(tags));
                            //     }
                            // }
                    }
                    });
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    return () => observer.disconnect();
}

// 新增：更新单个卡片样式
function updateCardStyle(element: HTMLElement, data: TiktokCardData) {
    const elementFooter = element.querySelector('[class*="DivCardFooter"]');
    if (elementFooter) {
        elementFooter.innerHTML = createElementStr(data);
    }
    element.insertAdjacentHTML('afterend', createCreatedTime(data));
    element.insertAdjacentHTML('afterend', createDayTime(data));

    //嵌入一个react组件，试试响应式
    const container = document.createElement('div');
    element.insertAdjacentElement('afterend', container);
}

export function changeTiktokMedianNumber(number:number){
    const CLASS_NAME = "__efluns_median_number"
    const datas = getTiktokVideoData(window.location.href)
    if(datas){
        datas.median_number = number
        datas.videos.forEach(item=>{
            const videoCard = document.querySelector(`[data-e2e="user-post-item"] [href*="/video/${item.id}"]`)
            if(videoCard){
                const createTimeDom = videoCard.parentElement?.querySelector(".__efluns_created_time")
                if(createTimeDom){
                    let medianElement = createTimeDom.querySelector(`.${CLASS_NAME}`)
                    if(!medianElement){
                        medianElement = document.createElement('div')
                        medianElement.className = CLASS_NAME
                        createTimeDom.insertAdjacentElement('afterbegin', medianElement)
                    }
                    const percent = item.playCount/number
                    if(item.is_paid_partnership){
                        medianElement.textContent = item.is_shop ? `${percent.toFixed(1)}X-Shop` : `${percent.toFixed(1)}X-Paid`
                        medianElement.setAttribute('data-type', item.is_shop ? "shop" : "paid")
                    }else{
                        const type = percent <= 0.4 ? 'bad' : percent >= 3.0 ? 'good' : 'normal'
                        medianElement.textContent = `${percent.toFixed(1)}X`
                        medianElement.setAttribute('data-type', type)
                    }
                }
            }
        })
    }
}


export function observeTiktokTags(){
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node instanceof HTMLElement) {
                        const tagElements = node.querySelectorAll('[data-e2e="challenge-item"] a[class*="AVideoContainer"]:not(:has(.__efluns_tag_item))');
                        tagElements.forEach(element => {
                            const href = element.getAttribute('href');
                            if (href) {
                                const videoId = href.split('/video/')[1];
                                if (videoId) {
                                    const data = getTiktokDataById(videoId);
                                    if (data) {
                                        updateTagStyle(element as HTMLElement, data);
                                    }
                                }
                            }
                        });
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    return () => observer.disconnect();
}

function updateTagStyle(element: HTMLElement, data: TiktokCardData) {
    element.insertAdjacentHTML('afterend', createTagElementStr(data))
}

function createTagElementStr(data: TiktokCardData) {
    return `
    <div class="__efluns_tag_item">
        <span class="__video__stat">${ICONS.like}${formatNumber(data.diggCount)}</span>
    </div>
    `;
}