import { TiktokCardData, TiktokChannelData } from "@/@types"

export const VIDEO_DATA_TIKTOK = "VIDEO_DATA_TIKTOK"

const FROM_TIKTOK_EARLY_SCRIPT = 'FROM_TIKTOK_EARLY_SCRIPT'

const STORAGE_KEY_PREFIX = 'TIKTOK_VIDEO_DATA_'

const REFRESH_SELECTOR = "[class*='DivErrorContainer'] button"

export const sendToContentScript = (data: TiktokChannelData) => {
    // window.postMessage({
    //     type: FROM_TIKTOK_EARLY_SCRIPT,
    //     url: window.location.href,
    //     data: data
    // }, window.location.origin);
    sendEvent(FROM_TIKTOK_EARLY_SCRIPT,{
        url: window.location.href,
        data: data
    })
}



export const sendTiktokVideoData = (data: TiktokChannelData,url:string) => {
    const storageKey = STORAGE_KEY_PREFIX + url
    const oldData = JSON.parse(sessionStorage.getItem(storageKey) || '{}') as Tik<PERSON>ChannelData
    // if(!data.isFirst && oldData.videos?.length){
    //     sendEvent("DATA_MONITOR_ERROR",{
    //         url: window.location.href
    //     })
    // }
    
    if(!data.isFirst && oldData.videos?.length > 0){
        data.videos = [...oldData.videos,...data.videos]
        data.median_number = getMedian(data.videos.map(item=>item.playCount))
        
        sessionStorage.setItem(storageKey, JSON.stringify(data))
        sendToContentScript(data)
    }else{
        // 存储到 sessionStorage
        data.median_number = getMedian(data.videos.map(item=>item.playCount))
        sessionStorage.setItem(storageKey, JSON.stringify(data))
        sendToContentScript(data)
        
        // 清理旧数据
        const allKeys = Object.keys(sessionStorage)
        const tiktokKeys = allKeys.filter(key => key.startsWith(STORAGE_KEY_PREFIX))
        if (tiktokKeys.length > 10) {
            sessionStorage.removeItem(tiktokKeys[0])
        }
    }
}

export const getTiktokVideoData = (url:string) => {
    const storageKey = STORAGE_KEY_PREFIX + url
    
    try {
        const data = sessionStorage.getItem(storageKey)
        return data ? JSON.parse(data) as TiktokChannelData : undefined
    } catch (error) {
        return undefined
    }
}

// 从 sessionStorage 获取数据
export function getTiktokDataById(id: string): TiktokCardData | undefined {
    try {
        const url = window.location.href;
        const storageKey = STORAGE_KEY_PREFIX + url;
        const data = sessionStorage.getItem(storageKey);
        if (!data) return undefined;
        
        const parsedData = JSON.parse(data) as TiktokChannelData;
        return parsedData.videos.find((video: TiktokCardData) => video.id === id);
    } catch (err) {
        return undefined;
    }
}

// export const listenFromTikTokEarlyScript = (callback: (data: TiktokChannelData) => void) => {
//     const handler = (event: MessageEvent) => {
//         if (event.origin !== window.location.origin) return;
        
//         if (event.data?.type === 'FROM_TIKTOK_EARLY_SCRIPT') {
//             callback(event.data.data);
//         }
//     };

//     window.addEventListener('message', handler);
    
//     // 返回清理函数
//     return () => {
//         window.removeEventListener('message', handler);
//     };
// }

export const tiktokRefreshBtnClick = async () => {
    
    waitForElementToRunCallback({selector:REFRESH_SELECTOR,callback:element=>{
 if (!location.href.includes("tiktok.com/@")) return;
    setTimeout(()=>{
        element.click()
    },1000)
   }})
};

export const parseTiktokVideoDuration = (seconds: number): string => {
    if(!seconds) return "";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

