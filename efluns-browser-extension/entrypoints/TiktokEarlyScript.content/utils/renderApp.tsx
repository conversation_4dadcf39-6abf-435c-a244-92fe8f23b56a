import createCache, { type EmotionCache } from '@emotion/cache'
import { createRoot, Root } from "react-dom/client";
import { QueryClientProvider } from '@tanstack/react-query';
import { CacheProvider } from '@emotion/react'
import cssStyle from "~/assets/tailwind.css?inline"


const DEFAULT_APP_ROOT_ID = "efluns-app-root"
const DEFAULT_ANCHOR_ID = "efluns-anchor"


// 创建或获取锚点元素
// 修改 getAnchor 函数，添加 container 参数
async function getAnchor(
    anchorId: string = DEFAULT_ANCHOR_ID,
    container?: HTMLElement
): Promise<HTMLElement> {
    let anchor = document.querySelector(`#${anchorId}`) as HTMLElement
    
    if (!anchor) {
        anchor = document.createElement('div')
        anchor.id = anchorId
        // 如果指定了容器，就插入到容器中，否则插入到 body
        const targetContainer = container || document.querySelector("html") || document.body
        targetContainer.appendChild(anchor)
    }
    
    return anchor
}


// 准备应用环境(Shadow DOM + 样式注入)
function prepareAppEnv(
    anchor: HTMLElement, 
    appRootId: string = DEFAULT_APP_ROOT_ID,
    anchorId: string = DEFAULT_ANCHOR_ID
): Promise<{
    appRoot: HTMLElement
    styleCache: EmotionCache
}> {
    return new Promise((resolve, reject) => {
        const createShadowDOM = () => {
            const appRoot = document.createElement('div')
            appRoot.id = appRootId
            
            // 注入全局样式
            const basicStyleElement = document.createElement('style')
            basicStyleElement.innerHTML = cssStyle
            
            // 创建 emotion 样式容器
            const emotionStyleElement = document.createElement('style')
            const styleCache = createCache({
                key: anchorId,
                container: emotionStyleElement
            })
            styleCache.compat = true
            
            // 构建完整的 DOM 结构
            const html = document.createElement('html')
            html.style.fontSize= "16px"
            const head = document.createElement('head')
            const body = document.createElement('body')
            
            head.appendChild(basicStyleElement)
            head.appendChild(emotionStyleElement)
            body.appendChild(appRoot)
            html.appendChild(head)
            html.appendChild(body)
            
            return { html, styleCache }
        }
        
        // 创建或获取 Shadow Root
        if (anchor.shadowRoot === null) {
            anchor.attachShadow({ mode: 'open' })
        }
        
        // 初始化 Shadow DOM
        let html: HTMLElement | null = null
        let styleCache = null
        
        if (anchor.shadowRoot?.childElementCount === 0) {
            const { html: newHtml, styleCache: newCache } = createShadowDOM()
            html = newHtml
            styleCache = newCache
            anchor.shadowRoot?.appendChild(html)
        } else {
            html = anchor.shadowRoot?.querySelector('html') ?? null
        }
        
        const appRoot = html?.querySelector(`#${appRootId}`) as HTMLElement
        
        if (!appRoot || !styleCache) {
            reject(new Error('Failed to initialize app environment'))
            return
        }
        
        resolve({
            appRoot,
            styleCache
        })
    })
}

// 渲染应用
// let root: Root | null = null
// 修改 renderApp 函数，添加 container 选项
export async function renderAppWithTailwind(
    Component: React.ComponentType,
    options?: {
        appRootId?: string
        anchorId?: string
        container?: HTMLElement
    }
) {
    try {
        const anchorId = options?.anchorId ?? DEFAULT_ANCHOR_ID
        const appRootId = options?.appRootId ?? DEFAULT_APP_ROOT_ID
        
        const anchor = await getAnchor(anchorId, options?.container)
        const { appRoot, styleCache } = await prepareAppEnv(anchor, appRootId, anchorId)
        
        // if (root) {
        //     root.unmount()
        // }
        
        const root = createRoot(appRoot)
        root.render(
            <CacheProvider value={styleCache}>
                <QueryClientProvider client={queryClient}>
                    <MessageContainer />
                    <Component />
                </QueryClientProvider>
            </CacheProvider>
        )
    } catch (err) {
        console.error('Failed to render app:', err)
    }
}


//使用方法
// 默认注入到 body
/* renderApp(YourComponent)

// 注入到指定元素
const container = document.querySelector('#some-element')
renderApp(YourComponent, { container })

// 同时指定容器和自定义 ID
renderApp(YourComponent, {
    container: document.querySelector('#some-element'),
    appRootId: 'custom-root',
    anchorId: 'custom-anchor'
}) */
