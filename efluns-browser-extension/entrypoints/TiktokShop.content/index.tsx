/* eslint-disable prefer-rest-params */
import { profileDetailResponse, ttshopResponse } from "@/@types/tiktok/ttshop";
import { TTSErrorMessage, TTSOffscreenMessage } from "../offscreen/const";
import { execuptingOffscreen, observeCaptcha, sendToOffcreen, setSearchInput } from "./utils";
import { interceptXHR } from "./xhr-interceptor";

export default defineContentScript({
    registration:"runtime",
    world:"MAIN",
    main(){
        const log = console.info
        log("loading ttshop...");
        if(window.top === window.self) return;
        observeCaptcha()//随时监听人机验证

        if(window.location.href.includes("/creator/detail")){
            console.log("开始抓取创作者详情页");
            interceptXHR({
                targetUrl: '/api/v1/oec/affiliate/creator/marketplace/profile',
                onRequest: (body) => {
                    // 如果需要处理请求体
                },
                onResponse: (response:profileDetailResponse, requestBody) => {
                    console.log("收到创作者搜索结果");
                    console.log(response);
                    const cid = new URL(window.location.href).searchParams.get("cid")
                    if(requestBody?.creator_oec_id === cid && requestBody?.profile_types.includes(2)){
                        if(response.code === 0){
                        sendToOffcreen(TTSOffscreenMessage.CREATOR_SEARCH_COMPLETE, {
                            cid,
                            data:response.creator_profile
                        });
                    }else{
                        sendToOffcreen(TTSOffscreenMessage.CREATOR_SEARCH_COMPLETE, {
                            cid,
                            data:null
                        });
                    }
                    }
                },
                onError: (error) => {
                    log("创作者搜索结果请求失败");

                    console.error(error);
                }
            });

            return
        }

        if(window.location.href.includes("/creator")){            
                interceptXHR({
                    targetUrl: '/api/v1/oec/affiliate/creator/marketplace/find',
                    onRequest: (body) => {
                        // 如果需要处理请求体
                    },
                    onResponse: (response:ttshopResponse, requestBody) => {

                        const query =  requestBody?.query || ""
                        if(!query) return;
                        const creator = response?.creator_profile_list?.find(item=>item.handle.value.toLowerCase() === query.toLowerCase())
                        if(creator){
                            sendToOffcreen(TTSOffscreenMessage.OPEN_CREATE_DETAIL_IFRAME, {
                                query,
                                cid:creator.creator_oecuid.value
                            });
                        }else{
                            sendToOffcreen(TTSOffscreenMessage.OPEN_CREATE_DETAIL_IFRAME, {
                                query,
                                cid:null
                            });
                        }
                    },
                    onError: (error) => {
                        console.error(error);
                    }
                });
            
            console.log("offscreen");
            
            execuptingOffscreen().then((msg)=>{
                console.log("init Ready!");
                sendToOffcreen(TTSOffscreenMessage.SCRAPED_DATA,{
                    status:"success",msg
                })

        
            }).catch(err=>{
                console.log(err);
                console.log("init Failed!");
                sendToOffcreen(TTSOffscreenMessage.SCRAPED_DATA,{
                    status:"failed",msg:err instanceof Error ? err.message : "Unknown error"
                })

            })




    window.addEventListener('message',(event:any)=>{

        if(event?.data?.from === "EASYKOL"){
            //从 bg发送来的数据
            log("收到由ifrmae转发到inject中消息");
            log(event)

            if(event?.data?.type === TTSOffscreenMessage.CREATOR_SEARCH_START){
                log("一秒后开始搜索...,目标词：",event?.data?.query);
                setTimeout(()=>{
                    setSearchInput(event?.data?.query)
                },1000)
            }
        }
        
    })
    }

        if(window.location.href.includes("/account/login")){
            // 监听来自iframe的消息
           console.log("进入了登录页面？，直接reject");
           sendToOffcreen(TTSOffscreenMessage.SCRAPED_DATA,{
            status:"failed",
            msg:TTSErrorMessage.NOT_LOGIN
           })
           return;
           
        }
}
})


