import { TTSErrorMessage, TTSOffscreenMessage } from "../offscreen/const";

function waitForElement(selector:string):Promise<HTMLElement | null>{
    return new Promise(resolve => {
        // 如果元素已存在，直接返回
        const element = document.querySelector(selector) as HTMLElement;
        if(element) {
            resolve(element);
            return;
        }

        // 否则创建观察器等待元素出现
        const observer = new MutationObserver(() => {
            const element = document.querySelector(selector) as HTMLElement;
            if(element) {
                observer.disconnect();
                resolve(element);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });
}

const SEARCH_INPUT_SELECTOR = '[data-tid="m4b_input_search"]'
const CAPTCHA_SELECTOR = '#captcha_container'

export function observeCaptcha(){
    waitForElement(CAPTCHA_SELECTOR).then(ele=>{
        console.log("需要人机验证");
        if(ele){
            sendToOffcreen(TTSOffscreenMessage.SCRAPED_DATA,{
                status:"failed",
                msg:TTSErrorMessage.NEED_CAPTCHA,
                url:window.location.href
            })
        }
    })
}

export function execuptingOffscreen(){
    return new Promise((resolve,reject) => {
        waitForElement(SEARCH_INPUT_SELECTOR).then((element:HTMLElement | null) => {
            console.log(element!.innerText);
            if(element){
                resolve(element.innerText)
            }
        })
    })
}

export function setSearchInput(text:string) {
    const input = document.querySelector(SEARCH_INPUT_SELECTOR) as HTMLElement;
    if(!input) {
        sendToOffcreen(TTSOffscreenMessage.SCRAPED_DATA,{
            status:"failed",
            msg:TTSErrorMessage.NOT_LOGIN
        })
        return;
    };
    
    input.focus();
    // 先全选当前内容
    document.execCommand('selectAll', false);
    // 删除选中内容
    document.execCommand('delete', false);
    // 输入新内容
    document.execCommand('insertText', false, text);
    
    // 创建并触发回车事件
    const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13,
        which: 13,
        bubbles: true,
        cancelable: true
    });
    input.dispatchEvent(enterEvent);
}

export function sendToOffcreen(type:string,data:any){
    window.parent.postMessage({
        from:"TIKTOK_SHOP",
        type,
        data,
        timestamp:Date.now()
    }, '*');
}