/* eslint-disable prefer-rest-params */
interface XHRInterceptorOptions {
    targetUrl: string;
    onRequest?: (body: any) => void;
    onResponse?: (response: any, requestBody?: any) => void;
    onError?: (error: Error) => void;
  }
  
  export function interceptXHR(options: XHRInterceptorOptions) {
    const originalXHR = window.XMLHttpRequest;
    
    window.XMLHttpRequest = function() {
      const xhr = new originalXHR();
      const send = xhr.send;
      const open = xhr.open;
      let requestUrl = '';
      let requestBody: any;
      
      xhr.open = function(method, url) {
        requestUrl = url as string;
        // @ts-expect-error 111
        return open.apply(this, arguments);
      };
      
      xhr.send = function(body) {
        if (requestUrl.includes(options.targetUrl)) {
          try {
            requestBody = body ? JSON.parse(body as string) : null;
            options.onRequest?.(requestBody);
          } catch(e) {
            options.onError?.(new Error('解析请求体失败: ' + e));
          }
          
          this.addEventListener('load', function() {
            try {
              const response = JSON.parse(this.response);
              options.onResponse?.(response, requestBody);
            } catch(e) {
              options.onError?.(new Error('解析响应失败: ' + e));
            }
          });
        }
        // @ts-expect-error 111
        return send.apply(this, arguments);
      };
      
      return xhr;
    } as any;
  }