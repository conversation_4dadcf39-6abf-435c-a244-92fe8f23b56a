import { renderAppWithTailwind } from "../TiktokEarlyScript.content/utils/renderApp"
import TagBox from "./tagBox"

const TIKTOK_TAG_SELECTOR = '#main-content-challenge'


async function renderTagBox(){
    const tagBox = await waitFor(TIKTOK_TAG_SELECTOR)
	const supportPage = await getSupportPage(window.location.href)
    // console.log("tagBox",tagBox)
    // console.log("supportPage",supportPage)
    if(supportPage === SupportPage.TIKTOK_TAG && tagBox){
        renderAppWithTailwind(()=>{
            return <TagBox />
        },{
            anchorId:"efluns-tag-box-anchor",
            appRootId:"efluns-tag-box-app-root",
            container:tagBox as HTMLElement
        })
    }
}

export default defineContentScript({
    matches: ['https://www.tiktok.com/*'],
    runAt: 'document_idle',
    main() {
        console.log("tiktok tag content script is loading");
        waitFor("picture img").then(()=>{
            renderTagBox()
        })
    }
})