import { getTagsCount } from "@/utils/tiktok/getTags";
import { motion } from "framer-motion";

export default function TagBox() {
    const [tags, setTags] = useState<[string, number][] | null>(getTagsCount());
    const routerToTag = (tag: string) => {
        window.open(`https://www.tiktok.com/tag/${tag}`, '_blank')
     }

     if(!tags?.length){
        return <></>
     }

    return <motion.div 
        className='absolute right-[50px] top-2 w-[300px] h-[135px] overflow-y-auto bg-[#ffffff] rounded-md shadow-md'
        whileHover={{ height: "auto" }}
        transition={{ duration: 0.3, ease: "easeOut" }}
    >
        <ul className='p-2 text-[14px] font-[500]'>
            {tags?.filter((_,index)=>index<10).map(([tag, count]) => (
                <li  onClick={() => routerToTag(tag)} className='flex justify-between items-center px-[15px] py-[10px] border-b border-[#E5E5E5] last:border-b-0 hover:bg-[#E5E5E5] cursor-pointer' key={tag}>
                    <span className='text-[#252447] w-[80%] truncate hover:text-[#000000]'>#{tag}</span>
                    <span className='text-[#6C6C85]'>{count}</span>
                </li>
            ))}
        </ul>
    </motion.div>
}