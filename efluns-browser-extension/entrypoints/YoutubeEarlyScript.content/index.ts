import { GET_YOUTUBE_DATA, getYoutubeData } from "@/utils/youtube";
import "@/assets/youtube.css"

export default defineContentScript({
    matches: ['https://www.youtube.com/*'],
    runAt: 'document_start',
    main() {

        const cache = new Map<string, any>()

        const messageListener = (message: any, sender:any, sendResponse: (response: any) => void) => {
            const cacheKey = `${message.action}:${message.url}`
            
            switch(message.action){
                case GET_YOUTUBE_DATA:
                    if (cache.has(cacheKey)) {
                        sendResponse({ data: cache.get(cacheKey) });
                    } else {
                        getYoutubeData(message.url).then(res => {
                            cache.set(cacheKey, res)
                            sendResponse({ data: res });
                        })
                    }
                    return true;
                default:
                    break;
            }
        }
      
        browser.runtime.onMessage.addListener(messageListener)

        return () => {
            browser.runtime.onMessage.removeListener(messageListener)
        }
    }
})
