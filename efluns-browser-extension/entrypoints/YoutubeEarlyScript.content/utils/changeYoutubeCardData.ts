import { YoutubeChannelData } from "@/@types"

export function changeYoutubeMedianNumber(number:number){
    if(!number) return
    const CLASS_NAME = "__efluns_median_number"
    const datas = getYoutubeData()
    console.log("datas",datas)
    if(datas){
        datas.videos.forEach(item=>{
            if(!item.views) return
            const videoCard = document.querySelector(`#contents  ytd-rich-item-renderer ytd-thumbnail a#thumbnail[href^='/watch?v=${item.id}']`)
            if(videoCard){
                let medianElement = videoCard.querySelector(`.${CLASS_NAME}`)
                if(!medianElement){
                    medianElement = document.createElement('div')
                    medianElement.className = CLASS_NAME
                    videoCard.insertAdjacentElement('beforeend', medianElement)
                }
                const percent = extractCount(item.views.toString())/number
                    if(item.is_paid_partnership){
                        medianElement.textContent = `${percent.toFixed(1)}X-Paid`
                        medianElement.setAttribute('data-type', "paid")
                    }else{
                        const type = percent <= 0.4 ? 'bad' : percent >= 3.0 ? 'good' : 'normal'
                        medianElement.textContent = `${percent.toFixed(1)}X`
                        medianElement.setAttribute('data-type', type)
                    }
                
            }
        })
    }
}

export function saveYoutubeData(data:YoutubeChannelData){
    const storageKey = "youtube_data"
    const storage = window.sessionStorage
    storage.setItem(storageKey,JSON.stringify(data))
}

export function getYoutubeData() :YoutubeChannelData | null{
    const storageKey = "youtube_data"
    const storage = window.sessionStorage
    const data = storage.getItem(storageKey)
    return data ? JSON.parse(data) : null
}
