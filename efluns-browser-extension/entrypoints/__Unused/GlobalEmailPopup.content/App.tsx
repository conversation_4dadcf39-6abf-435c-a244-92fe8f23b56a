import { HTMLAttributes } from 'react'

interface MenuItem {
	id: string
	title: string
	href?: string
}

export interface AppProps extends HTMLAttributes<HTMLDivElement> {
	data: string
}

export default function App({ data: email, ...props }: AppProps) {
	const menuItems: MenuItem[] = [
		{
			id: 'EMAIL',
			title: 'Send Email',
			href: `mailto:${email}`
		}
	]
	return (
		<div className="min-w-[140px] bg-white drop-shadow  rounded-sm px-[8px] py-[8px] divide-y divide-gray-100 text-[14px] ">
			<ul className=" text-gray-700 dark:text-gray-200">
				{menuItems.map((item) => (
					<li key={item.id}>
						<a
							href={item.href}
							className="block px-[8px] py-[4px] hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white rounded-[6px] cursor-pointer transition "
						>
							{item.title}
						</a>
					</li>
				))}
			</ul>
		</div>
	)
}
