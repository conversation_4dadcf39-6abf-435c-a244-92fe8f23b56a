import queryClient from '@/utils/queryClient'
import { QueryClientProvider } from '@tanstack/react-query'
import { Root, createRoot } from 'react-dom/client'
import { ShadowRootContentScriptUi } from 'wxt/client'
import '~/assets/index.css'
import App from './App'

export default defineContentScript({
	matches: ['<all_urls>'],
	runAt: 'document_end',
	cssInjectionMode: 'ui',
	async main(ctx) {
		// const style = document.createElement('style')
		// style.innerHTML = styles
		// document.head.appendChild(style)
		let popup: ShadowRootContentScriptUi<Root> | null = null
		const generatePopup = async (data: string, anchor: Element) => {
			const ui = await createShadowRootUi(ctx, {
				name: 'efluns-email-popup',
				position: 'overlay',
				zIndex: 1,
				alignment: 'bottom-left',
				anchor,
				append: 'after',
				onMount: (container) => {
					// Create a root on the UI container and render a component
					const app = document.createElement('div')
					container.appendChild(app)

					const root = createRoot(app)
					root.render(
						<QueryClientProvider client={queryClient}>
							<App data={data} />
						</QueryClientProvider>
					)
					return root
				},
				onRemove: (root) => {
					// Unmount the root when the UI is removed
					root?.unmount()
				}
			})

			// Call mount to add the UI to the DOM
			ui.mount()
			return ui
		}
		const dismiss = () => {
			popup?.remove()
		}
		watchTextSelection((email, anchor) => {
			dismiss()
			generatePopup(email, anchor).then((ui) => {
				popup = ui
			})
		}, dismiss)
	}
})

function watchTextSelection(onEmailSelect: (email: string, element: Element) => void, onDismiss: () => void) {
	const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b/
	let isSelecting = false
	let selectedEmail: string | undefined = undefined
	let selectedElement: Element | undefined | null = null

	document.addEventListener('selectionchange', () => {
		const selection = document.getSelection()
		const selectedText = selection?.toString()
		const element = selection?.anchorNode?.parentElement

		if (!selectedText || !element) {
			return
		}

		isSelecting = true
		selectedElement = element
		selectedEmail = selectedText.match(emailRegex)?.[0]
	})

	document.addEventListener('mouseup', () => {
		if (!isSelecting) {
			return
		}

		if (selectedEmail && selectedElement) {
			onEmailSelect(selectedEmail, selectedElement)
		}
		isSelecting = false
	})

	document.body.addEventListener('click', () => {
		onDismiss()
	})
}
