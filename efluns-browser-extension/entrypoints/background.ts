import { UPDATE_JUMP_URL } from '@/constants'
import { getAuthService, registerAuthService } from '@/services/AuthService'
import { registerCommonService } from '@/services/CommonService'
import { getCookieSyncService, registerCookieSyncService } from '@/services/CookieSyncService'
import { registerEasyMessageService } from '@/services/EasyMessageService'
import { registerEasykolCookieService } from '@/services/EasykolCookieService'
import { registerEmailAuthorizationService } from '@/services/EmailAuthorizationService'
import { registerEmailTemplateService } from '@/services/EmailTemplateService'
import { registerFirstLoginService } from '@/services/FirstLoginService'
import { registerGoogleSheetService } from '@/services/GoogleSheetService'
import { registerKOLService } from '@/services/KOLService'
import { registerPlatformService } from '@/services/PlatformService'
import { registerPostHogService } from '@/services/PostHogService'
import { registerProjectService } from '@/services/ProjectService'
import { registerTagService } from '@/services/TagService'
// import { registerTTShopService } from '@/services/TTShopService'
// import { CookieSessionManager } from '@/services/CookieSessionManager'
import { registerKolsIdService } from '@/services/KolsId'
import { registerTikTokService } from '@/services/TikTokService'
import { registerTwitterService } from '@/services/TwitterService'
import { registerUserService } from '@/services/UserService'
import { createAnalysisPost } from '@/utils/analysisPost'
import { get } from '@/utils/request'
import { initSentry } from './KOLInfoCard.content/utils/sentry'

export default defineBackground(() => {
	const log = makeModuleLog('Background')
	const $event = createAnalysisPost('Background')
	log('Hello background!', { runtimeId: browser.runtime.id })

	initSentry()

	// eslint-disable-next-line @typescript-eslint/ban-ts-comment
	// @ts-ignore
	browser.sidePanel.setPanelBehavior({ openPanelOnActionClick: true }).catch((error) => console.error(error))

	browser.runtime.onInstalled.addListener(async (details) => {
		if (details.reason === 'install') {
			$event('install', {
				installTime: new Date().toISOString()
			})
			// 打开 easykol.com 登录页
			browser.tabs.create({ url: 'https://easykol.com/login' })

			// Login page will be opened automatically on first successful login
			// via AuthService or CookieSyncService
		} else if (details.reason === 'update') {
			// 如果更新到这个版本，则先把自动 ttshop 关闭
			get(UPDATE_JUMP_URL).then((res) => {
				if (res?.data?.data?.url) {
					updateJumpURLState.setValue(res.data.data.url)
				}
			})
			$event('update', {
				updateTime: new Date().toISOString(),
				previousVersion: details.previousVersion,
				currentVersion: browser.runtime.getManifest().version
			})
		}
	})

	browser.runtime.setUninstallURL('https://airy-update.notion.site/2025-6-22-188e8694012c80249e6ce32ac679d252')

	changeInterfaceHeader()

	registerCommonService()
	registerAuthService()
	registerPlatformService()
	registerProjectService()
	registerKOLService()
	registerTikTokService()
	registerCookieSyncService()
	registerPostHogService()

	registerEmailTemplateService()
	registerEmailAuthorizationService()
	registerFirstLoginService()

	getAuthService().watchAuthCallback()

	// registerTTShopService()

	registerGoogleSheetService()
	registerEasykolCookieService()

	registerEasyMessageService()

	registerTagService()
	registerUserService()
	registerTwitterService()
	registerKolsIdService()
	getCookieSyncService().initialize()
})

async function changeInterfaceHeader() {
	const RULE_ID = 1024
	await browser.declarativeNetRequest.updateDynamicRules({
		removeRuleIds: [RULE_ID]
	})
	await browser.declarativeNetRequest.updateDynamicRules({
		addRules: [
			{
				id: RULE_ID, // 规则 ID 需要是唯一的
				priority: 10000,
				action: {
					type: 'modifyHeaders',
					responseHeaders: [
						{
							header: 'access-control-allow-origin',
							operation: 'append',
							value: '*'
						},
						{
							header: 'access-control-allow-credentials',
							operation: 'append',
							value: 'true'
						}
					]
				},
				condition: {
					resourceTypes: ['xmlhttprequest'],
					regexFilter: '^https?://easykol.com/.*'
				}
			}
		]
	})
}

// 扩展 globalThis 类型
// declare global {
// 	let test: () => void
// 	let testUser: () => void
// 	let testUserConnections: () => void
// 	let testSearch: () => void
// 	let testUserFollowing: () => void
// }

// 	;(globalThis as any).test = () => {
// 		console.log("测试推文接口");
// 		new GuestTwitterGraphQLTweetUseCase("1676132765098741762").fetchTweet()
// 	}

// 	;(globalThis as any).testUser = () => {
// 		console.log("测试用户信息接口");
// 		new GuestTwitterGraphQLUserUseCase("luoling8192").fetchUser()
// 	}

// 	;(globalThis as any).testUserConnections = () => {
// 		console.log("测试用户关注列表接口");
// 		new GuestTwitterGraphQLUserConnectionsUseCase("1676132765098741762", 20).fetchUserConnections()
// 	}

// 	;(globalThis as any).testUserFollowing = () => {
// 		console.log("测试用户关注列表（Following）接口");
// 		new GuestTwitterGraphQLUserFollowingUseCase("44196397", "elonmusk", 20).fetchUserFollowing()
// 	}
