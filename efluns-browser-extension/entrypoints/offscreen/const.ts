import { DeclarativeNetRequest } from 'wxt/browser'

// export const OFFSCREEN_URLS = ['*://*.tiktokglobalshop.com/*','*://*.tiktok.com/*','*://*.tiktokshop.com/*']
export const OFFSCREEN_URLS = ['<all_urls>']
export const ttsRules: DeclarativeNetRequest.Rule[] = [
	{
		id: 1, // 规则ID需要是唯一的
		priority: 10000,
		action: {
			type: 'modifyHeaders',
			responseHeaders: [
				{
					header: 'content-security-policy',
					operation: 'remove'
				},
				{
					header: 'content-security-policy-report-only',
					operation: 'remove'
				}
			]
		},
		condition: {
			resourceTypes: ['sub_frame'],
			regexFilter:
				'^https?://[^/]*\\.(tiktok|tiktokglobalshop|tiktokshop)\\.com/(connection/creator|affiliate-cmp/creator)'
		}
	}
]

export enum TTSErrorMessage {
	NOT_LOGIN = 'Please log in TikTok Shop Seller Center',
	NO_DATA = 'No Showcase For your market',
	NEED_CAPTCHA = 'Please complete the verification in TikTok Shop Seller Center'
}

export enum TTSOffscreenMessage {
	CREATOR_SEARCH_START = 'CREATOR_SEARCH_START', //开始创建一个查询任务
	CREATOR_SEARCH_COMPLETE = 'CREATOR_SEARCH_COMPLETE', //查询任务完成
	SCRAPED_DATA = 'SCRAPED_DATA', //搜索框的页面初始化完成
	OPEN_CREATE_DETAIL_IFRAME = 'OPEN_CREATE_DETAIL_IFRAME', //查询到了博主打开创作者详情页查找详细信息
	REFRESH_QUERY = 'REFRESH_QUERY' //刷新查询
}

// ttshop区和域名的映射
export const TTSDomain = {
	'affiliate.tiktokglobalshop.com': 'GLOBAL',
	'affiliate-us.tiktok.com': 'US',
	'partner.us.tiktokshop.com': 'US'
} as const

export const createTTShopUrl = (domain: keyof typeof TTSDomain, cid?: string) => {
	const url =
		domain === 'partner.us.tiktokshop.com'
			? `https://${domain}/affiliate-cmp/creator`
			: `https://${domain}/connection/creator`
	if (!cid) {
		return url
	}
	return `${url}/detail?cid=${cid}`
}
