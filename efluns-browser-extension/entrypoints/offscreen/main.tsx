import { createTTShopUrl, TTSDomain, TTSOffscreenMessage } from "./const"

const log = makeModuleLog("Main.offscreen")
log("open")
const domain = new URL(window.location.href).searchParams.get("domain") as keyof typeof TTSDomain

const iframe = document.createElement('iframe')
iframe.style.width = '100%'
iframe.style.height = '100%'
iframe.src = createTTShopUrl(domain)
document.body.appendChild(iframe)


// TODO 写一个转发器，bg发来的请求转发给iframe
// 监听来自 background 的消息
browser.runtime.onMessage.addListener((message:any):any => {
    log("收到来自bg的消息", message)
    if(message.from === "EASYKOL"){
    // 获取iframe元素
    if (!iframe) {
        log("未找到iframe元素")
        return
    }

    // 转发消息给iframe
    iframe.contentWindow?.postMessage(message, '*')
}
})

// 监听来自iframe的消息
window.addEventListener('message',async (event:any) => {
    const message = event.data
    if(message?.from === 'TIKTOK_SHOP'){
        log("收到来自iframe的消息", message)
        if(message.type === TTSOffscreenMessage.OPEN_CREATE_DETAIL_IFRAME){
            log("打开创作者详情页")
            const {query,cid} = message.data
            if(await sendMessage("isQuerying",query)){
                sendMessage("complateInput",{query,cid})
                if(!cid) return 
                if(document.querySelector(`iframe#c_${cid}`)){
                    document.querySelector(`iframe#c_${cid}`)?.remove()
                }
                const detailPage = document.createElement('iframe')
                detailPage.id = `c_${cid}`
                detailPage.style.width = '100%'
                detailPage.style.height = '100%'
                detailPage.setAttribute("data-query",query)
                detailPage.src = createTTShopUrl(domain,cid)
                document.body.appendChild(detailPage)
            }
        }else if(message.type === TTSOffscreenMessage.CREATOR_SEARCH_COMPLETE){
            log("获取到抓取到的数据，可以关闭iframe了")
            log(message)
            const {cid} = message.data
            if(document.querySelector(`iframe#c_${cid}`)){
                message.data.query = document.querySelector(`iframe#c_${cid}`)?.getAttribute("data-query")
                document.querySelector(`iframe#c_${cid}`)?.remove()
            }
        }
        // 转发给background
        browser.runtime.sendMessage(message)
    }
})
