import { messagesProxyState } from "@/entrypoints/KOLInfoCard.content/store";

type MessageConfig = {
  pending?: string;
  success?: string;
  error?: string;
  duration?: number;
};

export const cardMessage = {
  success(content: string, duration = 3000) {
    messagesProxyState.push({
      id: crypto.randomUUID(),
      type: "success",
      content,
      duration,
    });
  },

  error(content: string, duration = 3000) {
    messagesProxyState.push({
      id: crypto.randomUUID(),
      type: "error",
      content,
      duration,
    });
  },

  warning(content: string, duration = 3000) {
    messagesProxyState.push({
      id: crypto.randomUUID(),
      type: "warning",
      content,
      duration,
    });
  },

  info(content: string, duration = 3000) {
    messagesProxyState.push({
      id: crypto.randomUUID(),
      type: "info",
      content,
      duration,
    });
  },

  loading(content: string) {
    const id = crypto.randomUUID();
    messagesProxyState.push({
      id,
      type: "loading",
      content,
      duration: 0,
    });
    return id;
  },

  remove(id: string) {
    const index = messagesProxyState.findIndex((msg) => msg.id === id);
    if (index > -1) {
      messagesProxyState.splice(index, 1);
    }
  },

  async promise<T>(
    promiseOrFn: Promise<T> | (() => Promise<T>),
    config: MessageConfig = {},
  ): Promise<T> {
    const {
      pending = "processing...",
      success = "success",
      error = "error",
      duration = 3000,
    } = config;
    const promise = typeof promiseOrFn === "function" ? promiseOrFn() : promiseOrFn;

    const loadingId = this.loading(pending);

    try {
      const result = await promise;
      this.remove(loadingId);
      this.success(success, duration);
      return result;
    } catch (err) {
      this.remove(loadingId);
      this.error(err instanceof Error ? err.message : (error as string), duration);
      throw err;
    }
  },
};
