import { useCallback, useEffect, useState } from 'react'
import { WxtStorageItem } from 'wxt/storage'

export default function useStorageState<Value, Metadata extends Record<string, unknown>>(
	item: WxtStorageItem<Value, Metadata>
) {
	const [data, setData] = useState<Value>(item.defaultValue)

	useEffect(() => {
		item.getValue().then(setData)
		const unwatch = item.watch(setData)
		return unwatch
	}, [item])

	return [
		data,
		useCallback(
			async (next: Value | ((prev: Value) => Value)) => {
				if (typeof next === 'function') {
					const updater = next as (prev: Value) => Value
					await item.setValue(updater(data))
				} else {
					await item.setValue(next)
				}
			},
			[item, data]
		)
	] as const
}
