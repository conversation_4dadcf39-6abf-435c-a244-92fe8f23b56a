import getSupportPage, { SupportPage } from '@/utils/getSupportPage'
import { useLocation } from 'react-use'

export default function useSupportPageInfo() {
	const location = useLocation()
	const url = location.href
	const [supportPage, setSupportPage] = useState<SupportPage | null>(null)

	useEffect(() => {
		if (url) {
			getSupportPage(url).then(page=>{
				setSupportPage(page)
			})
		} else {
			setSupportPage(null)
		}
	}, [url])

	return {
		page: supportPage,
		isInYoutubeKOLPage: supportPage === SupportPage.YOUTUBE_KOL,
		isInTikTokTagPage: supportPage === SupportPage.TIKTOK_TAG,
		isInTikTokSearchPage: supportPage === SupportPage.TIKTOK_SEARCH,
		isInTikTokKOLPage: supportPage === SupportPage.TIKTOK_KOL,
		isInInsKOLPage: supportPage === SupportPage.INS_KOL
	}
}
