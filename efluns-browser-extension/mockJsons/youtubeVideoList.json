[{"type": "Video", "id": "ltDCUKyE8bM", "title": {"runs": [{"text": "NEW iPhone 16e - Making the iPhone 16 Irrelevant?", "bold": false, "italics": false, "strikethrough": false}], "text": "NEW iPhone 16e - Making the iPhone 16 Irrelevant?"}, "description_snippet": {"runs": [{"text": "Apple just announced the iPhone 16e which is $200 cheaper than the iPhone 16 yet has better battery life, the same chipset, a better modem, the same software, and surprisingly few compromises....", "bold": false, "italics": false, "strikethrough": false}], "text": "Apple just announced the iPhone 16e which is $200 cheaper than the iPhone 16 yet has better battery life, the same chipset, a better modem, the same software, and surprisingly few compromises...."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/ltDCUKyE8bM/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBcFOBSU7xmGaiZ8-9HyVE0EAWp3g", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/ltDCUKyE8bM/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCVA5uFEETVvGkUo7Z6tVZLYIqjEg", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/ltDCUKyE8bM/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBmHPSfLnsLy2OX7N96DITbnb88pA", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/ltDCUKyE8bM/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLAi1pUMt5Cx6YgvDQpfFtzXC339oA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "7:09", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "ltDCUKyE8bM"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "ltDCUKyE8bM", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CPkBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "ltDCUKyE8bM", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CPkBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["ltDCUKyE8bM"], "params": "CAQ%3D"}}, "videoIds": ["ltDCUKyE8bM"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/ltDCUKyE8bM/mqdefault_6s.webp?du=3000&sqp=CMii670G&rs=AOn4CLClusL-e6NttjBp-HwW2XxxNUgcng", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "ltDCUKyE8bM", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguesndl.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=96d0c250ac84f1b3&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=ltDCUKyE8bM", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "3 days ago"}, "view_count": {"text": "19,713 views"}, "short_view_count": {"text": "19K views"}, "duration": {"text": "7:09", "seconds": 429}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CP0BEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "ltDCUKyE8bM", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CP0BEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["ltDCUKyE8bM"], "params": "CAQ%3D"}}, "videoIds": ["ltDCUKyE8bM"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "ltDCUKyE8bM", "onAddCommand": {"clickTrackingParams": "CPwBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "ltDCUKyE8bM", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtsdERDVUt5RThiTQ%3D%3D", "commands": [{"clickTrackingParams": "CPgBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CPsBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "0JIUQDMVjg8", "title": {"runs": [{"text": "OnePlus Watch 3 - A Breakthrough in Smartwatches?", "bold": false, "italics": false, "strikethrough": false}], "text": "OnePlus Watch 3 - A Breakthrough in Smartwatches?"}, "description_snippet": {"runs": [{"text": "OnePlus Watch 3 (affiliate): https://mikeobrienmedia.com/OPWatch3 \n\nThe Galaxy watch has been a great recommendation for years until last year where the OnePlus watch gave it some competition....", "bold": false, "italics": false, "strikethrough": false}], "text": "OnePlus Watch 3 (affiliate): https://mikeobrienmedia.com/OPWatch3 \n\nThe Galaxy watch has been a great recommendation for years until last year where the OnePlus watch gave it some competition...."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i9.ytimg.com/vi/0JIUQDMVjg8/hqdefault_custom_1.jpg?sqp=COjL670G-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBUwk6avgiot8QnKwarLjw9ru8edA", "width": 336, "height": 188}, {"url": "https://i9.ytimg.com/vi/0JIUQDMVjg8/hqdefault_custom_1.jpg?sqp=COjL670G-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLApmYS_mOyVC1BDH98vfmZQoDwtJg", "width": 246, "height": 138}, {"url": "https://i9.ytimg.com/vi/0JIUQDMVjg8/hqdefault_custom_1.jpg?sqp=COjL670G-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDq1AqdFiYCPFrE5Yj_NFsKgjYwkA", "width": 196, "height": 110}, {"url": "https://i9.ytimg.com/vi/0JIUQDMVjg8/hqdefault_custom_1.jpg?sqp=COjL670G-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLAaabpe0crXCFN4MT78-j1qtTlxvg", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "19:42", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "0JIUQDMVjg8"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "0JIUQDMVjg8", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CPIBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "0JIUQDMVjg8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CPIBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["0JIUQDMVjg8"], "params": "CAQ%3D"}}, "videoIds": ["0JIUQDMVjg8"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/0JIUQDMVjg8/mqdefault_6s.webp?du=3000&sqp=CKuk670G&rs=AOn4CLC2upxAxlfQYW7MNohL0NJVKK18ZA", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "0JIUQDMVjg8", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguesnd7.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=d092144033158e0f&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=0JIUQDMVjg8", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "4 days ago"}, "view_count": {"text": "35,579 views"}, "short_view_count": {"text": "35K views"}, "duration": {"text": "19:42", "seconds": 1182}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CPYBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "0JIUQDMVjg8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CPYBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["0JIUQDMVjg8"], "params": "CAQ%3D"}}, "videoIds": ["0JIUQDMVjg8"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "0JIUQDMVjg8", "onAddCommand": {"clickTrackingParams": "CPUBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "0JIUQDMVjg8", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgswSklVUURNVmpnOA%3D%3D", "commands": [{"clickTrackingParams": "CPEBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CPQBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "vcPDx11BTjo", "title": {"runs": [{"text": "Don't Waste $500 - Galaxy S25 Ultra vs OnePlus 13 vs Pixel 9 Pro", "bold": false, "italics": false, "strikethrough": false}], "text": "Don't Waste $500 - Galaxy S25 Ultra vs OnePlus 13 vs Pixel 9 Pro"}, "description_snippet": {"runs": [{"text": "S25 Ultra Enhanced Trade-In + Exclusive $300 bonus  + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/s25Ultra\nGoogle Pixel 9 Pro XL 👉 https://mikeobrienmedia.com/Pixel9Pro...", "bold": false, "italics": false, "strikethrough": false}], "text": "S25 Ultra Enhanced Trade-In + Exclusive $300 bonus  + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/s25Ultra\nGoogle Pixel 9 Pro XL 👉 https://mikeobrienmedia.com/Pixel9Pro..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/vcPDx11BTjo/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDggHyESk7bU6vRgOUHkW59sp7w8w", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/vcPDx11BTjo/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAk_0615L9VUNTFG1idOBHu1EhfhQ", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/vcPDx11BTjo/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLAKpbDC-6h1i_HJEydz7rJGlx_pjw", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/vcPDx11BTjo/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDVPUl7-mVstEIUCNTT89gFA9yFgA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "30:45", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "vcPDx11BTjo"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "vcPDx11BTjo", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "COsBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "vcPDx11BTjo", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "COsBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["vcPDx11BTjo"], "params": "CAQ%3D"}}, "videoIds": ["vcPDx11BTjo"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/vcPDx11BTjo/mqdefault_6s.webp?du=3000&sqp=CKK-670G&rs=AOn4CLDNzzao_IDGIevkoOJ0m1mLcAL-Kw", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "vcPDx11BTjo", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr4---sn-oguesnd7.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=bdc3c3c75d414e3a&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=vcPDx11BTjo", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "9 days ago"}, "view_count": {"text": "133,375 views"}, "short_view_count": {"text": "133K views"}, "duration": {"text": "30:45", "seconds": 1845}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CO8BEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "vcPDx11BTjo", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CO8BEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["vcPDx11BTjo"], "params": "CAQ%3D"}}, "videoIds": ["vcPDx11BTjo"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "vcPDx11BTjo", "onAddCommand": {"clickTrackingParams": "CO4BENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "vcPDx11BTjo", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "Cgt2Y1BEeDExQlRqbw%3D%3D", "commands": [{"clickTrackingParams": "COoBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CO0BEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "gc_N4LcpSo4", "title": {"runs": [{"text": "NEW Powerbeats Pro 2 - Beating AirPods with This New Feature", "bold": false, "italics": false, "strikethrough": false}], "text": "NEW Powerbeats Pro 2 - Beating AirPods with This New Feature"}, "description_snippet": {"runs": [{"text": "After SIX years, we are finally getting a refresh on one of the most popular earbuds of all time, the PowerBeats Pro 2 are here. They are perhaps the best dual-native Android/iOS earbuds you...", "bold": false, "italics": false, "strikethrough": false}], "text": "After SIX years, we are finally getting a refresh on one of the most popular earbuds of all time, the PowerBeats Pro 2 are here. They are perhaps the best dual-native Android/iOS earbuds you..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/gc_N4LcpSo4/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLB3851vafzBlco-OvtZAHdxn1IgXA", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/gc_N4LcpSo4/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLC6FesAjIduYWjlgXoHRFCVzVMzHw", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/gc_N4LcpSo4/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLAyax8Iq8uFhxpoJPLnuIvAxbd5tQ", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/gc_N4LcpSo4/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCRxAg95l5dPHmAMzx8oYjzaIhKoA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "16:48", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "gc_N4LcpSo4"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "gc_N4LcpSo4", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "COQBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "gc_N4LcpSo4", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "COQBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["gc_N4LcpSo4"], "params": "CAQ%3D"}}, "videoIds": ["gc_N4LcpSo4"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/gc_N4LcpSo4/mqdefault_6s.webp?du=3000&sqp=CLq0670G&rs=AOn4CLBrj8cLLoETx8gIGA3-tueh_0tZ5w", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "gc_N4LcpSo4", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguesndl.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=81cfcde0b7294a8e&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=gc_N4LcpSo4", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "11 days ago"}, "view_count": {"text": "67,479 views"}, "short_view_count": {"text": "67K views"}, "duration": {"text": "16:48", "seconds": 1008}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "COgBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "gc_N4LcpSo4", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "COgBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["gc_N4LcpSo4"], "params": "CAQ%3D"}}, "videoIds": ["gc_N4LcpSo4"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "gc_N4LcpSo4", "onAddCommand": {"clickTrackingParams": "COcBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "gc_N4LcpSo4", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtnY19ONExjcFNvNA%3D%3D", "commands": [{"clickTrackingParams": "COMBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "COYBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "Bz-E8bxA-U8", "title": {"runs": [{"text": "Samsung Galaxy S25 vs S25+ vs S25 Ultra (Don't Waste $500)", "bold": false, "italics": false, "strikethrough": false}], "text": "Samsung Galaxy S25 vs S25+ vs S25 Ultra (Don't Waste $500)"}, "description_snippet": {"runs": [{"text": "S25 Ultra Enhanced Trade-In + Exclusive $300 bonus  + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/s25Ultra\nS25 Enhanced Trade-In + Exclusive $50 credit + 5% Student Discount...", "bold": false, "italics": false, "strikethrough": false}], "text": "S25 Ultra Enhanced Trade-In + Exclusive $300 bonus  + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/s25Ultra\nS25 Enhanced Trade-In + Exclusive $50 credit + 5% Student Discount..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/Bz-E8bxA-U8/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAo-_XurYLzYZnwpQra7VYjiZTPcw", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/Bz-E8bxA-U8/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCYG6jGYiCGp0DJykxRw0tImMUU2g", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/Bz-E8bxA-U8/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBoLohRIMYKw8sPkbkmgJdq1ITXyA", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/Bz-E8bxA-U8/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCL9D47q82Q5z3iHosZkBRI-SjlAg", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "15:00", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "Bz-E8bxA-U8"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "Bz-E8bxA-U8", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CN0BEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "Bz-E8bxA-U8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CN0BEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["Bz-E8bxA-U8"], "params": "CAQ%3D"}}, "videoIds": ["Bz-E8bxA-U8"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/Bz-E8bxA-U8/mqdefault_6s.webp?du=3000&sqp=CLvF670G&rs=AOn4CLCXJo6SvvQQT7Fd9UEli7oCLg71GA", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "Bz-E8bxA-U8", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr5---sn-oguelnle.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=073f84f1bc40f94f&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=Bz-E8bxA-U8", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 weeks ago"}, "view_count": {"text": "199,490 views"}, "short_view_count": {"text": "199K views"}, "duration": {"text": "15:00", "seconds": 900}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "COEBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "Bz-E8bxA-U8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "COEBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["Bz-E8bxA-U8"], "params": "CAQ%3D"}}, "videoIds": ["Bz-E8bxA-U8"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "Bz-E8bxA-U8", "onAddCommand": {"clickTrackingParams": "COABENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "Bz-E8bxA-U8", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtCei1FOGJ4QS1VOA%3D%3D", "commands": [{"clickTrackingParams": "CNwBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CN8BEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "yaBK8FRN57U", "title": {"runs": [{"text": "NEW Galaxy S25 (Samsung's Small Flagship)", "bold": false, "italics": false, "strikethrough": false}], "text": "NEW Galaxy S25 (Samsung's Small Flagship)"}, "description_snippet": {"runs": [{"text": "Pre-order for Enhanced Trade-In + Exclusive $50 credit + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/S25\nWatch my S25 Plus video: https://youtu.be/fx1-GFwCuM8\nWatch my...", "bold": false, "italics": false, "strikethrough": false}], "text": "Pre-order for Enhanced Trade-In + Exclusive $50 credit + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/S25\nWatch my S25 Plus video: https://youtu.be/fx1-GFwCuM8\nWatch my..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/yaBK8FRN57U/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLD49xfYkdXouUXknPk3GsV9xEBf7Q", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/yaBK8FRN57U/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBh1_5Iynw178IRjMGnR1J7zbC5oA", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/yaBK8FRN57U/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDIq8Jier7N0HNAyAS6UOJ5QBCaXg", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/yaBK8FRN57U/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBAF5Rs5WVOOz6Rhbuvm-cKqlmddw", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "13:34", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "yaBK8FRN57U"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "yaBK8FRN57U", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CNYBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "yaBK8FRN57U", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CNYBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["yaBK8FRN57U"], "params": "CAQ%3D"}}, "videoIds": ["yaBK8FRN57U"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/yaBK8FRN57U/mqdefault_6s.webp?du=3000&sqp=CL-i670G&rs=AOn4CLCKhdqRv7cwebHlKaeYo1Q_0fEnMg", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "yaBK8FRN57U", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr2---sn-oguesnde.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=c9a04af0544de7b5&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=yaBK8FRN57U", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "1 month ago"}, "view_count": {"text": "202,888 views"}, "short_view_count": {"text": "202K views"}, "duration": {"text": "13:34", "seconds": 814}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CNoBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "yaBK8FRN57U", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CNoBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["yaBK8FRN57U"], "params": "CAQ%3D"}}, "videoIds": ["yaBK8FRN57U"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "yaBK8FRN57U", "onAddCommand": {"clickTrackingParams": "CNkBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "yaBK8FRN57U", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "Cgt5YUJLOEZSTjU3VQ%3D%3D", "commands": [{"clickTrackingParams": "CNUBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CNgBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "fx1-GFwCuM8", "title": {"runs": [{"text": "NEW Galaxy S25+ (What Changed in Samsung S25 Plus?)", "bold": false, "italics": false, "strikethrough": false}], "text": "NEW Galaxy S25+ (What Changed in Samsung S25 Plus?)"}, "description_snippet": {"runs": [{"text": "Pre-order for Enhanced Trade-In + Exclusive $100 in credits + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/S25Plus\nWatch my S25 video: https://youtu.be/yaBK8FRN57U\nWatch...", "bold": false, "italics": false, "strikethrough": false}], "text": "Pre-order for Enhanced Trade-In + Exclusive $100 in credits + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/S25Plus\nWatch my S25 video: https://youtu.be/yaBK8FRN57U\nWatch..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/fx1-GFwCuM8/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBtZ8ejgrL6ZSgJO7iwPqzuwuh0mA", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/fx1-GFwCuM8/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLD8UvHC-1FJAyEU4TADP8pfjvEMug", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/fx1-GFwCuM8/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBduP2SzjAHe9ealBQhF0mIFU_0GA", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/fx1-GFwCuM8/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLAMsfTOisifvuxq22szOozvcW55fA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "11:12", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "fx1-GFwCuM8"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "fx1-GFwCuM8", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CM8BEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "fx1-GFwCuM8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CM8BEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["fx1-GFwCuM8"], "params": "CAQ%3D"}}, "videoIds": ["fx1-GFwCuM8"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/fx1-GFwCuM8/mqdefault_6s.webp?du=3000&sqp=CLOk670G&rs=AOn4CLDyhNOnJbWLNPALcbur0xCoQQqr3Q", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "fx1-GFwCuM8", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr4---sn-ogul7n7s.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=7f1d7e185c02b8cf&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=fx1-GFwCuM8", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "1 month ago"}, "view_count": {"text": "142,058 views"}, "short_view_count": {"text": "142K views"}, "duration": {"text": "11:12", "seconds": 672}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CNMBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "fx1-GFwCuM8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CNMBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["fx1-GFwCuM8"], "params": "CAQ%3D"}}, "videoIds": ["fx1-GFwCuM8"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "fx1-GFwCuM8", "onAddCommand": {"clickTrackingParams": "CNIBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "fx1-GFwCuM8", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtmeDEtR0Z3Q3VNOA%3D%3D", "commands": [{"clickTrackingParams": "CM4BENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CNEBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "LHy9xufzrpg", "title": {"runs": [{"text": "NEW Galaxy S25 Ultra (What Did Samsung Do?)", "bold": false, "italics": false, "strikethrough": false}], "text": "NEW Galaxy S25 Ultra (What Did Samsung Do?)"}, "description_snippet": {"runs": [{"text": "Pre-order for Enhanced Trade-In + Exclusive $300 bonus  + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/s25Ultra\nWatch my S25 Plus video: https://youtu.be/fx1-GFwCuM8\nWatch...", "bold": false, "italics": false, "strikethrough": false}], "text": "Pre-order for Enhanced Trade-In + Exclusive $300 bonus  + 5% Student Discount (affiliate) 👉 https://mikeobrienmedia.com/s25Ultra\nWatch my S25 Plus video: https://youtu.be/fx1-GFwCuM8\nWatch..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/LHy9xufzrpg/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCMr8gzl_p0fNKLKskeooEoiTFLtA", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/LHy9xufzrpg/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCVbA7W6myYdnvT0YcKVDA9uhwS1A", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/LHy9xufzrpg/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCpWo8NKFvqur8v7W5xSgQUYFVdMw", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/LHy9xufzrpg/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLB0hIefYioJlSGcMAd6XUvuVfJErg", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "13:21", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "LHy9xufzrpg"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "LHy9xufzrpg", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CMgBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "LHy9xufzrpg", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CMgBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["LHy9xufzrpg"], "params": "CAQ%3D"}}, "videoIds": ["LHy9xufzrpg"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/LHy9xufzrpg/mqdefault_6s.webp?du=3000&sqp=CJCg670G&rs=AOn4CLDDlQqHuHd8PxZ-cQuYNYEiUhistw", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "LHy9xufzrpg", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr5---sn-ogul7n76.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=2c7cbdc6e7f3ae98&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=LHy9xufzrpg", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "1 month ago"}, "view_count": {"text": "34,819 views"}, "short_view_count": {"text": "34K views"}, "duration": {"text": "13:21", "seconds": 801}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CMwBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "LHy9xufzrpg", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CMwBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["LHy9xufzrpg"], "params": "CAQ%3D"}}, "videoIds": ["LHy9xufzrpg"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "LHy9xufzrpg", "onAddCommand": {"clickTrackingParams": "CMsBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "LHy9xufzrpg", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtMSHk5eHVmenJwZw%3D%3D", "commands": [{"clickTrackingParams": "CMcBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CMoBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "KItryIwA1T8", "title": {"runs": [{"text": "MacBook User Switches To Samsung Galaxy Book5 Pro (Laptop Review)", "bold": false, "italics": false, "strikethrough": false}], "text": "MacBook User Switches To Samsung Galaxy Book5 Pro (Laptop Review)"}, "description_snippet": {"runs": [{"text": "This is the new Samsung Galaxy Book5 Pro, a slim and powerful laptop that I’ve been using HEAVILY for the past few days to see if I can switch to this from my MacBook Pro. So this is my review...", "bold": false, "italics": false, "strikethrough": false}], "text": "This is the new Samsung Galaxy Book5 Pro, a slim and powerful laptop that I’ve been using HEAVILY for the past few days to see if I can switch to this from my MacBook Pro. So this is my review..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/KItryIwA1T8/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLA-qppOPLNhyezwhWngXocXi8eJWg", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/KItryIwA1T8/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAkXBsJVtAubg5X84xq1o-UdNCdSA", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/KItryIwA1T8/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDAmzmyIwTmDkXS25CcBFsm2uMYqw", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/KItryIwA1T8/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDFWdRmM67e2qaqyezzVcdDUAmxVg", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "15:39", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "KItryIwA1T8"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "KItryIwA1T8", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CMEBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "KItryIwA1T8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CMEBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["KItryIwA1T8"], "params": "CAQ%3D"}}, "videoIds": ["KItryIwA1T8"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/KItryIwA1T8/mqdefault_6s.webp?du=3000&sqp=CPqs670G&rs=AOn4CLDmXR9ivC2Dl29EijDghp9_WKKlAA", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "KItryIwA1T8", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr1---sn-oguesn6y.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=288b6bc88c00d53f&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=KItryIwA1T8", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "1 month ago"}, "view_count": {"text": "218,725 views"}, "short_view_count": {"text": "218K views"}, "duration": {"text": "15:39", "seconds": 939}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CMUBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "KItryIwA1T8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CMUBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["KItryIwA1T8"], "params": "CAQ%3D"}}, "videoIds": ["KItryIwA1T8"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "KItryIwA1T8", "onAddCommand": {"clickTrackingParams": "CMQBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "KItryIwA1T8", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtLSXRyeUl3QTFUOA%3D%3D", "commands": [{"clickTrackingParams": "CMABENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CMMBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "INz22-SvhpQ", "title": {"runs": [{"text": "I Tried Smart Glasses for 7 Days (EvenRealities G1 UNSPONSORED Review)", "bold": false, "italics": false, "strikethrough": false}], "text": "I Tried Smart Glasses for 7 Days (EvenRealities G1 UNSPONSORED Review)"}, "description_snippet": {"runs": [{"text": "I tested the EvenRealities G1 smart glasses and honestly, there is a lot about them that feels like the future, but there are also a few hiccups that need to be improved. Here is my full review....", "bold": false, "italics": false, "strikethrough": false}], "text": "I tested the EvenRealities G1 smart glasses and honestly, there is a lot about them that feels like the future, but there are also a few hiccups that need to be improved. Here is my full review...."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/INz22-SvhpQ/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBe1QOGe6gES43BDeg44PL8rNY3tQ", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/INz22-SvhpQ/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDppsldfaFNKfnoR4UHnnfvg_dY9w", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/INz22-SvhpQ/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDiUmUw7vvUzCuG-K-VPTZtY4aXbg", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/INz22-SvhpQ/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLALEpe_eBWmAFtjJBHlg9Y_sp42ng", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "18:13", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "INz22-SvhpQ"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "INz22-SvhpQ", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CLoBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "INz22-SvhpQ", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CLoBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["INz22-SvhpQ"], "params": "CAQ%3D"}}, "videoIds": ["INz22-SvhpQ"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/INz22-SvhpQ/mqdefault_6s.webp?du=3000&sqp=CIC9670G&rs=AOn4CLDx-6Ur1e_jieBamr6W2LGttVyMkw", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "INz22-SvhpQ", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguelnsr.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=20dcf6dbe4af8694&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=INz22-SvhpQ", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "1 month ago"}, "view_count": {"text": "48,572 views"}, "short_view_count": {"text": "48K views"}, "duration": {"text": "18:13", "seconds": 1093}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CL4BEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "INz22-SvhpQ", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CL4BEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["INz22-SvhpQ"], "params": "CAQ%3D"}}, "videoIds": ["INz22-SvhpQ"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "INz22-SvhpQ", "onAddCommand": {"clickTrackingParams": "CL0BENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "INz22-SvhpQ", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtJTnoyMi1TdmhwUQ%3D%3D", "commands": [{"clickTrackingParams": "CLkBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CLwBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "fFPmCU1nZXE", "title": {"runs": [{"text": "NEW OnePlus 13 - Best Android of 2025?", "bold": false, "italics": false, "strikethrough": false}], "text": "NEW OnePlus 13 - Best Android of 2025?"}, "description_snippet": {"runs": [{"text": "I tested the new OnePlus 13 as my everyday phone for the past few weeks and there are some things I liked, some things I didn't like, and a few things that don't matter. Here are my thoughts...", "bold": false, "italics": false, "strikethrough": false}], "text": "I tested the new OnePlus 13 as my everyday phone for the past few weeks and there are some things I liked, some things I didn't like, and a few things that don't matter. Here are my thoughts..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/fFPmCU1nZXE/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAJtl2BYsGQHw1Y98UgFR5vKLC_iw", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/fFPmCU1nZXE/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDeFyombGHragLCItLaQnAdOAu1kQ", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/fFPmCU1nZXE/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLD0e8AuPzeXsQXZstYNZcyFLm8y7w", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/fFPmCU1nZXE/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBVxlg0DM7CGQHVJLX_uFyG-UmzfQ", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "12:15", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "fFPmCU1nZXE"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "fFPmCU1nZXE", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CLMBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "fFPmCU1nZXE", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CLMBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["fFPmCU1nZXE"], "params": "CAQ%3D"}}, "videoIds": ["fFPmCU1nZXE"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/fFPmCU1nZXE/mqdefault_6s.webp?du=3000&sqp=CI-8670G&rs=AOn4CLCVKspS7mTHQ1lBIOzj4Gb6tBw-Ug", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "fFPmCU1nZXE", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-ogul7n7z.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=7c53e6094d676571&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=fFPmCU1nZXE", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "1 month ago"}, "view_count": {"text": "39,414 views"}, "short_view_count": {"text": "39K views"}, "duration": {"text": "12:15", "seconds": 735}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CLcBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "fFPmCU1nZXE", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CLcBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["fFPmCU1nZXE"], "params": "CAQ%3D"}}, "videoIds": ["fFPmCU1nZXE"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "fFPmCU1nZXE", "onAddCommand": {"clickTrackingParams": "CLYBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "fFPmCU1nZXE", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtmRlBtQ1UxblpYRQ%3D%3D", "commands": [{"clickTrackingParams": "CLIBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CLUBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "M5xAJ0CJ6rk", "title": {"runs": [{"text": "Major Changes after 3 Months - iPhone 16 Pro Worth The Upgrade?", "bold": false, "italics": false, "strikethrough": false}], "text": "Major Changes after 3 Months - iPhone 16 Pro Worth The Upgrade?"}, "description_snippet": {"runs": [{"text": "A portion of this video was sponsored by Ecoflow\nEcoFlow Website: https://bit.ly/3OA80GB\nEcoFlow Amazon: https://amzn.to/3YJAJi8\nEcoFlow Rapid: https://bit.ly/3ASpz1v\nPower up the holidays...", "bold": false, "italics": false, "strikethrough": false}], "text": "A portion of this video was sponsored by Ecoflow\nEcoFlow Website: https://bit.ly/3OA80GB\nEcoFlow Amazon: https://amzn.to/3YJAJi8\nEcoFlow Rapid: https://bit.ly/3ASpz1v\nPower up the holidays..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/M5xAJ0CJ6rk/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDxRj8bLpATScN3ZR7TVcs00OG3tw", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/M5xAJ0CJ6rk/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDDq14vtsrNzEmvUlDulrAHKueRkw", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/M5xAJ0CJ6rk/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDRstOxgRTf2xsGkmvx3ZENv0paxw", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/M5xAJ0CJ6rk/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLC42k4y7X8Vbwjjz43dJNNZDh0hdg", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "12:01", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "M5xAJ0CJ6rk"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "M5xAJ0CJ6rk", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CKwBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "M5xAJ0CJ6rk", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CKwBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["M5xAJ0CJ6rk"], "params": "CAQ%3D"}}, "videoIds": ["M5xAJ0CJ6rk"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/M5xAJ0CJ6rk/mqdefault_6s.webp?du=3000&sqp=CKC7670G&rs=AOn4CLB69wURfrUdWPltNijtp2FgtNi7PA", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "M5xAJ0CJ6rk", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr1---sn-oguesn6r.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=339c40274089eab9&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=M5xAJ0CJ6rk", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 months ago"}, "view_count": {"text": "21,844 views"}, "short_view_count": {"text": "21K views"}, "duration": {"text": "12:01", "seconds": 721}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CLABEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "M5xAJ0CJ6rk", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CLABEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["M5xAJ0CJ6rk"], "params": "CAQ%3D"}}, "videoIds": ["M5xAJ0CJ6rk"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "M5xAJ0CJ6rk", "onAddCommand": {"clickTrackingParams": "CK8BENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "M5xAJ0CJ6rk", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtNNXhBSjBDSjZyaw%3D%3D", "commands": [{"clickTrackingParams": "CKsBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CK4BEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "ZMfn-p7Bu0c", "title": {"runs": [{"text": "I Tried Soundcore's Audio Lineup ... Don't Choose Wrong! - Liberty 4 Pro vs Aerofit 2 vs Space Pro", "bold": false, "italics": false, "strikethrough": false}], "text": "I Tried Soundcore's Audio Lineup ... Don't Choose Wrong! - Liberty 4 Pro vs Aerofit 2 vs Space Pro"}, "description_snippet": {"runs": [{"text": "Product links (official website) - sponsored by Soundcore\nsoundcore C40i https://soundcore.tech/5-in-1VQpM \nsoundcore Sleep A20 https://soundcore.tech/5-in-1Y82U\nsoundcore Aerofit 2 https://soundco...", "bold": false, "italics": false, "strikethrough": false}], "text": "Product links (official website) - sponsored by Soundcore\nsoundcore C40i https://soundcore.tech/5-in-1VQpM \nsoundcore Sleep A20 https://soundcore.tech/5-in-1Y82U\nsoundcore Aerofit 2 https://soundco..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/ZMfn-p7Bu0c/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLC8doVQcWPn7YZhECU3I9ugG3zn2Q", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/ZMfn-p7Bu0c/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAqqg29rnk54m1mX5dywmT-TzE6fw", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/ZMfn-p7Bu0c/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBJkYy8CS1tHae8sZq3i9AnizMm2w", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/ZMfn-p7Bu0c/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBArG37yhA65xQ3-xNoSMMS4-rGzw", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "24:11", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "ZMfn-p7Bu0c"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "ZMfn-p7Bu0c", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CKUBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "ZMfn-p7Bu0c", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CKUBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["ZMfn-p7Bu0c"], "params": "CAQ%3D"}}, "videoIds": ["ZMfn-p7Bu0c"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/ZMfn-p7Bu0c/mqdefault_6s.webp?du=3000&sqp=CMKr670G&rs=AOn4CLC2yOMJp0xCyXU8dXhIb_dCu62sEg", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "ZMfn-p7Bu0c", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguelnzl.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=64c7e7fa9ec1bb47&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=ZMfn-p7Bu0c", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 months ago"}, "view_count": {"text": "18,256 views"}, "short_view_count": {"text": "18K views"}, "duration": {"text": "24:11", "seconds": 1451}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CKkBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "ZMfn-p7Bu0c", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CKkBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["ZMfn-p7Bu0c"], "params": "CAQ%3D"}}, "videoIds": ["ZMfn-p7Bu0c"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "ZMfn-p7Bu0c", "onAddCommand": {"clickTrackingParams": "CKgBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "ZMfn-p7Bu0c", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtaTWZuLXA3QnUwYw%3D%3D", "commands": [{"clickTrackingParams": "CKQBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CKcBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "HuL2zrLOdBs", "title": {"runs": [{"text": "The Samsung Galaxy S25 - Things I'm Excited To See", "bold": false, "italics": false, "strikethrough": false}], "text": "The Samsung Galaxy S25 - Things I'm Excited To See"}, "description_snippet": {"runs": [{"text": "A portion of this video was sponsored by BestBuy\nSave $60 on the Galaxy Buds3 Pro at BestBuy https://go.magik.ly/ml/27uro/\n\nIt's that time of year, the Samsung Galaxy S25 lineup is right around...", "bold": false, "italics": false, "strikethrough": false}], "text": "A portion of this video was sponsored by BestBuy\nSave $60 on the Galaxy Buds3 Pro at BestBuy https://go.magik.ly/ml/27uro/\n\nIt's that time of year, the Samsung Galaxy S25 lineup is right around..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/HuL2zrLOdBs/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDwalujsWCSom-WqUdSBn_maOWF0g", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/HuL2zrLOdBs/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDkBevPuW9bAhGdUjBmA0mwGA1BAw", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/HuL2zrLOdBs/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBJkO8isOrOgqjTZcmsAbByirFjFw", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/HuL2zrLOdBs/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBDV-fpbn4e9b-aUqF7YAmGh-_fXw", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "12:49", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "HuL2zrLOdBs"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "HuL2zrLOdBs", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CJ4BEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "HuL2zrLOdBs", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CJ4BEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["HuL2zrLOdBs"], "params": "CAQ%3D"}}, "videoIds": ["HuL2zrLOdBs"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/HuL2zrLOdBs/mqdefault_6s.webp?du=3000&sqp=CO62670G&rs=AOn4CLAY4AJ9wpyafUX-tGiKSbw_mJ2sIQ", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "HuL2zrLOdBs", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguelnsy.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=1ee2f6ceb2ce741b&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=HuL2zrLOdBs", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 months ago"}, "view_count": {"text": "27,196 views"}, "short_view_count": {"text": "27K views"}, "duration": {"text": "12:49", "seconds": 769}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CKIBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "HuL2zrLOdBs", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CKIBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["HuL2zrLOdBs"], "params": "CAQ%3D"}}, "videoIds": ["HuL2zrLOdBs"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "HuL2zrLOdBs", "onAddCommand": {"clickTrackingParams": "CKEBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "HuL2zrLOdBs", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtIdUwyenJMT2RCcw%3D%3D", "commands": [{"clickTrackingParams": "CJ0BENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CKABEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "3eR5WsnO3bg", "title": {"runs": [{"text": "Is The Google Ecosystem Worth It?", "bold": false, "italics": false, "strikethrough": false}], "text": "Is The Google Ecosystem Worth It?"}, "description_snippet": {"runs": [{"text": "A portion of this video was sponsored by Level Home 👉 https://go.magik.ly/ml/281wy/\n\nGoogle’s ecosystem is different from the apple ecosystem and the Samsung ecosystem. You see, they both...", "bold": false, "italics": false, "strikethrough": false}], "text": "A portion of this video was sponsored by Level Home 👉 https://go.magik.ly/ml/281wy/\n\nGoogle’s ecosystem is different from the apple ecosystem and the Samsung ecosystem. You see, they both..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/3eR5WsnO3bg/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAcZmC1n6Ws98a8MTvhY6YSAOGLOQ", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/3eR5WsnO3bg/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAbXxzGJaJLk7lIvs6pPjIUyoUPmA", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/3eR5WsnO3bg/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBF4yQ3EoA83JH5RFEbYB490krXkA", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/3eR5WsnO3bg/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLA9hvqs0xE-ZqgHjcezn4T6q58jCA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "15:43", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "3eR5WsnO3bg"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "3eR5WsnO3bg", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CJcBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "3eR5WsnO3bg", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CJcBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["3eR5WsnO3bg"], "params": "CAQ%3D"}}, "videoIds": ["3eR5WsnO3bg"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/3eR5WsnO3bg/mqdefault_6s.webp?du=3000&sqp=CIC-670G&rs=AOn4CLDjN0sMRs-0Q7ekQ1Ozogy8XyBWhA", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "3eR5WsnO3bg", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr2---sn-oguesnd6.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=dde4795ac9ceddb8&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=3eR5WsnO3bg", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 months ago"}, "view_count": {"text": "61,642 views"}, "short_view_count": {"text": "61K views"}, "duration": {"text": "15:43", "seconds": 943}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CJsBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "3eR5WsnO3bg", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CJsBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["3eR5WsnO3bg"], "params": "CAQ%3D"}}, "videoIds": ["3eR5WsnO3bg"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "3eR5WsnO3bg", "onAddCommand": {"clickTrackingParams": "CJoBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "3eR5WsnO3bg", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgszZVI1V3NuTzNiZw%3D%3D", "commands": [{"clickTrackingParams": "CJYBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CJkBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "6MEEMGLq5-o", "title": {"runs": [{"text": "iPhone 16 Pro vs Galaxy S24 Ultra vs OPPO Find X8 Pro - CAMERA COMPARISON", "bold": false, "italics": false, "strikethrough": false}], "text": "iPhone 16 Pro vs Galaxy S24 Ultra vs OPPO Find X8 Pro - CAMERA COMPARISON"}, "description_snippet": {"runs": [{"text": "This video was made in partnership with OPPO", "bold": false, "italics": false, "strikethrough": false}], "text": "This video was made in partnership with OPPO"}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/6MEEMGLq5-o/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCZ88O22S_w6vKFORwDRpcXntUO5w", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/6MEEMGLq5-o/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBTgiUZiXC56JapUJQoi3-1i_XNQg", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/6MEEMGLq5-o/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBDpnpnnN2BkOVPq3BS49oyuXBS5g", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/6MEEMGLq5-o/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBPSaOXqugm6-rbmCFP7BFSY1Ed5w", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "13:23", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "6MEEMGLq5-o"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "6MEEMGLq5-o", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CJABEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "6MEEMGLq5-o", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CJABEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["6MEEMGLq5-o"], "params": "CAQ%3D"}}, "videoIds": ["6MEEMGLq5-o"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/6MEEMGLq5-o/mqdefault_6s.webp?du=3000&sqp=CJ67670G&rs=AOn4CLChc5pNbVKfBbgnd9NqR5s_FxBzig", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "6MEEMGLq5-o", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguelnsr.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=e8c1043062eae7ea&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=6MEEMGLq5-o", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 months ago"}, "view_count": {"text": "49,845 views"}, "short_view_count": {"text": "49K views"}, "duration": {"text": "13:23", "seconds": 803}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CJQBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "6MEEMGLq5-o", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CJQBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["6MEEMGLq5-o"], "params": "CAQ%3D"}}, "videoIds": ["6MEEMGLq5-o"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "6MEEMGLq5-o", "onAddCommand": {"clickTrackingParams": "CJMBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "6MEEMGLq5-o", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "Cgs2TUVFTUdMcTUtbw%3D%3D", "commands": [{"clickTrackingParams": "CI8BENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CJIBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "wbzZP01vjyI", "title": {"runs": [{"text": "My 45 Favorite Tech Gadgets You Can Buy (2024 Ultimate Gift Guide)", "bold": false, "italics": false, "strikethrough": false}], "text": "My 45 Favorite Tech Gadgets You Can Buy (2024 Ultimate Gift Guide)"}, "description_snippet": {"runs": [{"text": "Bambu Labs Black Friday Sale (sponsored): https://shareasale.com/r.cfm?b=2683783&u=3043928&m=138211&urllink=&afftrack= \nPRODUCT LINKS ⬇️ (affiliate)\n🚁DJI Neo Drone https://mikeobrienmedia.co...", "bold": false, "italics": false, "strikethrough": false}], "text": "Bambu Labs Black Friday Sale (sponsored): https://shareasale.com/r.cfm?b=2683783&u=3043928&m=138211&urllink=&afftrack= \nPRODUCT LINKS ⬇️ (affiliate)\n🚁DJI Neo Drone https://mikeobrienmedia.co..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/wbzZP01vjyI/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLB_NvXWTWT-gc7pgR-hKvzcnmXtZA", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/wbzZP01vjyI/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDwr-hxM_XMu718rwAFhM0Hn16_Sw", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/wbzZP01vjyI/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLAPtATnqnKYwwSzPUhDHauJ2iCvXw", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/wbzZP01vjyI/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDAla_o1NiNI54n065etQOezSPsGA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "22:29", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "wbzZP01vjyI"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "wbzZP01vjyI", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CIkBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "wbzZP01vjyI", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CIkBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["wbzZP01vjyI"], "params": "CAQ%3D"}}, "videoIds": ["wbzZP01vjyI"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/wbzZP01vjyI/mqdefault_6s.webp?du=3000&sqp=CJiK670G&rs=AOn4CLCjTGogXIQ4WCRAYJdCGPyNPvipyg", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "wbzZP01vjyI", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr4---sn-oguesn6y.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=c1bcd93f4d6f8f22&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=wbzZP01vjyI", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 months ago"}, "view_count": {"text": "88,455 views"}, "short_view_count": {"text": "88K views"}, "duration": {"text": "22:29", "seconds": 1349}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CI0BEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "wbzZP01vjyI", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CI0BEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["wbzZP01vjyI"], "params": "CAQ%3D"}}, "videoIds": ["wbzZP01vjyI"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "wbzZP01vjyI", "onAddCommand": {"clickTrackingParams": "CIwBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "wbzZP01vjyI", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "Cgt3YnpaUDAxdmp5SQ%3D%3D", "commands": [{"clickTrackingParams": "CIgBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CIsBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "XjTBupOjWmM", "title": {"runs": [{"text": "My Favorite Desk Accessories", "bold": false, "italics": false, "strikethrough": false}], "text": "My Favorite Desk Accessories"}, "description_snippet": {"runs": [{"text": "This video was sponsored by LG\nAmazon https://geni.us/g9GKB1Q\n*White model is 24% OFF as Black Friday Promotion in Amazon (Approx. $80 USD OFF)\nLG.com https://bit.ly/3AHcFmP\n*Black model is...", "bold": false, "italics": false, "strikethrough": false}], "text": "This video was sponsored by LG\nAmazon https://geni.us/g9GKB1Q\n*White model is 24% OFF as Black Friday Promotion in Amazon (Approx. $80 USD OFF)\nLG.com https://bit.ly/3AHcFmP\n*Black model is..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/XjTBupOjWmM/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCiHYeXpcabL_OQMR43aDlFt_E0Bg", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/XjTBupOjWmM/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCvOSQ5vLdiPivwJhUbL3hZIFxmJg", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/XjTBupOjWmM/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDHVFEzmnQnjszIeO2SqLo9qi3KCA", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/XjTBupOjWmM/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCLie81jUPm7XuXfZE9UqOPxitGSg", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "14:52", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "XjTBupOjWmM"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "XjTBupOjWmM", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CIIBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "XjTBupOjWmM", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CIIBEMfsBBgDIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["XjTBupOjWmM"], "params": "CAQ%3D"}}, "videoIds": ["XjTBupOjWmM"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/XjTBupOjWmM/mqdefault_6s.webp?du=3000&sqp=CL2p670G&rs=AOn4CLB00Lsnt56LIdwgR3SSECxWMhjomg", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "XjTBupOjWmM", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguelnze.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=5e34c1ba93a35a63&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=XjTBupOjWmM", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 months ago"}, "view_count": {"text": "64,870 views"}, "short_view_count": {"text": "64K views"}, "duration": {"text": "14:52", "seconds": 892}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CIYBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "XjTBupOjWmM", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CIYBEP6YBBgHIhMIqJbXubnZiwMV6X0PAh1C0jSt", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["XjTBupOjWmM"], "params": "CAQ%3D"}}, "videoIds": ["XjTBupOjWmM"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "XjTBupOjWmM", "onAddCommand": {"clickTrackingParams": "CIUBENGqBRgIIhMIqJbXubnZiwMV6X0PAh1C0jSt", "getDownloadActionCommand": {"videoId": "XjTBupOjWmM", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtYalRCdXBPaldtTQ%3D%3D", "commands": [{"clickTrackingParams": "CIEBENwwIhMIqJbXubnZiwMV6X0PAh1C0jSt", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CIQBEI5iIhMIqJbXubnZiwMV6X0PAh1C0jSt", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "eazWa4yshXo", "title": {"runs": [{"text": "Can Smart Rings Replace Smart Watches? - RingConn Gen 2 Smart Ring (sleep, health, fitness tracking)", "bold": false, "italics": false, "strikethrough": false}], "text": "Can Smart Rings Replace Smart Watches? - RingConn Gen 2 Smart Ring (sleep, health, fitness tracking)"}, "description_snippet": {"runs": [{"text": "This video was sponsored by RingConn\nMy dedicated promo code: QJE332EZ ($15 USD off)\nEnjoy bundles on sale for Black Friday at RingConn Official Website: https://bit.ly/4gI5LOp\n\nSmartwatches...", "bold": false, "italics": false, "strikethrough": false}], "text": "This video was sponsored by RingConn\nMy dedicated promo code: QJE332EZ ($15 USD off)\nEnjoy bundles on sale for Black Friday at RingConn Official Website: https://bit.ly/4gI5LOp\n\nSmartwatches..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/eazWa4yshXo/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBzUju7leRFsbnPIN-9SbvhAPjHRw", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/eazWa4yshXo/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDKdme2typeVD_aJ8TbBC8I6UEgcQ", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/eazWa4yshXo/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBfOyo2R--U3eQuri47LxdKVMtV_A", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/eazWa4yshXo/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLAjXu7xnWO8f87-17fW6EfhqVYpmA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "11:41", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "eazWa4yshXo"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "eazWa4yshXo", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CHsQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "eazWa4yshXo", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CHsQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["eazWa4yshXo"], "params": "CAQ%3D"}}, "videoIds": ["eazWa4yshXo"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/eazWa4yshXo/mqdefault_6s.webp?du=3000&sqp=CI62670G&rs=AOn4CLDXTm2pSXoAOTF8LLfAECeujfQzUA", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "eazWa4yshXo", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr2---sn-oguelnze.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=79acd66b8cac857a&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=eazWa4yshXo", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "2 months ago"}, "view_count": {"text": "40,745 views"}, "short_view_count": {"text": "40K views"}, "duration": {"text": "11:41", "seconds": 701}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CH8Q_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "eazWa4yshXo", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CH8Q_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["eazWa4yshXo"], "params": "CAQ%3D"}}, "videoIds": ["eazWa4yshXo"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "eazWa4yshXo", "onAddCommand": {"clickTrackingParams": "CH4Q0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "eazWa4yshXo", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtlYXpXYTR5c2hYbw%3D%3D", "commands": [{"clickTrackingParams": "CHoQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CH0QjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "GT1FMUxwVyQ", "title": {"runs": [{"text": "AIRPODS Tips, Tricks, and Hidden Features most people don't know", "bold": false, "italics": false, "strikethrough": false}], "text": "AIRPODS Tips, Tricks, and Hidden Features most people don't know"}, "description_snippet": {"runs": [{"text": "A portion of this video was sponsored by ecoflow \nEcoFlow Website: https://bit.ly/3YHpsih\nEcoFlow Amazon: https://amzn.to/3YJAJi8\nEcoFlow River 3: https://bit.ly/40G5KoF\nScore big this Black...", "bold": false, "italics": false, "strikethrough": false}], "text": "A portion of this video was sponsored by ecoflow \nEcoFlow Website: https://bit.ly/3YHpsih\nEcoFlow Amazon: https://amzn.to/3YJAJi8\nEcoFlow River 3: https://bit.ly/40G5KoF\nScore big this Black..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/GT1FMUxwVyQ/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLA1BERh1iu0OaKLenYjLW93AUJemw", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/GT1FMUxwVyQ/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAohFjzFDXj0kOBJPZ9r20JmbupMQ", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/GT1FMUxwVyQ/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDkcpnfM1YPhUkkGxOgpluTc9DwxQ", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/GT1FMUxwVyQ/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLC_K731qgtskRCWltRXPEUiW7N1KQ", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "16:32", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "GT1FMUxwVyQ"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "GT1FMUxwVyQ", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CHQQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "GT1FMUxwVyQ", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CHQQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["GT1FMUxwVyQ"], "params": "CAQ%3D"}}, "videoIds": ["GT1FMUxwVyQ"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/GT1FMUxwVyQ/mqdefault_6s.webp?du=3000&sqp=CNy0670G&rs=AOn4CLCqslBqxENqfQvS4TQEbw3T5EJShQ", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "GT1FMUxwVyQ", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr1---sn-oguesndr.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=193d45314c705724&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=GT1FMUxwVyQ", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "3 months ago"}, "view_count": {"text": "13,127 views"}, "short_view_count": {"text": "13K views"}, "duration": {"text": "16:32", "seconds": 992}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CHgQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "GT1FMUxwVyQ", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CHgQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["GT1FMUxwVyQ"], "params": "CAQ%3D"}}, "videoIds": ["GT1FMUxwVyQ"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "GT1FMUxwVyQ", "onAddCommand": {"clickTrackingParams": "CHcQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "GT1FMUxwVyQ", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtHVDFGTVV4d1Z5UQ%3D%3D", "commands": [{"clickTrackingParams": "CHMQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CHYQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "uKNAzcHGa5Y", "title": {"runs": [{"text": "Smartwatch Awards 2024 (Best by Category)", "bold": false, "italics": false, "strikethrough": false}], "text": "Smartwatch Awards 2024 (Best by Category)"}, "description_snippet": {"runs": [{"text": "THE WINNERS (links are affiliate)\n* Garmin Epix Pro Gen 2 👉 https://mikeobrienmedia.com/GarminEpixPro2\n* Withing Scanwatch Nova 👉 https://mikeobrienmedia.com/Withings-Scanwatch-Nova\n*...", "bold": false, "italics": false, "strikethrough": false}], "text": "THE WINNERS (links are affiliate)\n* Garmin Epix Pro Gen 2 👉 https://mikeobrienmedia.com/GarminEpixPro2\n* Withing Scanwatch Nova 👉 https://mikeobrienmedia.com/Withings-Scanwatch-Nova\n*..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/uKNAzcHGa5Y/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAYQT7-umoD4UjpTd1I0e_eGGcvQQ", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/uKNAzcHGa5Y/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCkA7R8mJBorbJbH8Rg95vD3Ymciw", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/uKNAzcHGa5Y/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDvgjW-OB_A8FPtpkAgXvs2f2v8YQ", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/uKNAzcHGa5Y/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLClwOjlpkdATAAxiiITcTLGlY8rhQ", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "20:27", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "uKNAzcHGa5Y"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "uKNAzcHGa5Y", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CG0Qx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "uKNAzcHGa5Y", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CG0Qx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["uKNAzcHGa5Y"], "params": "CAQ%3D"}}, "videoIds": ["uKNAzcHGa5Y"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/uKNAzcHGa5Y/mqdefault_6s.webp?du=3000&sqp=CIjF670G&rs=AOn4CLBxF6kgKFb9N_SqjXGKhFGP1E3iYQ", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "uKNAzcHGa5Y", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr1---sn-oguelnl7.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=b8a340cdc1c66b96&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=uKNAzcHGa5Y", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "3 months ago"}, "view_count": {"text": "438,513 views"}, "short_view_count": {"text": "438K views"}, "duration": {"text": "20:27", "seconds": 1227}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CHEQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "uKNAzcHGa5Y", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CHEQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["uKNAzcHGa5Y"], "params": "CAQ%3D"}}, "videoIds": ["uKNAzcHGa5Y"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "uKNAzcHGa5Y", "onAddCommand": {"clickTrackingParams": "CHAQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "uKNAzcHGa5Y", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "Cgt1S05BemNIR2E1WQ%3D%3D", "commands": [{"clickTrackingParams": "CGwQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CG8QjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "F9IiEgfy9Ig", "title": {"runs": [{"text": "Apple Watch Series 10 vs Ultra vs SE (Don't Waste $500)", "bold": false, "italics": false, "strikethrough": false}], "text": "Apple Watch Series 10 vs Ultra vs SE (Don't Waste $500)"}, "description_snippet": {"runs": [{"text": "Is it worth buying the new Apple Watch Series 10 or should you spend half the price on the Apple Watch SE 2, or splurge on the Apple Watch Ultra 2? Choosing the wrong one could be costly or...", "bold": false, "italics": false, "strikethrough": false}], "text": "Is it worth buying the new Apple Watch Series 10 or should you spend half the price on the Apple Watch SE 2, or splurge on the Apple Watch Ultra 2? Choosing the wrong one could be costly or..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/F9IiEgfy9Ig/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBArKXfL-iILzsSIt3E7_drMoFeTQ", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/F9IiEgfy9Ig/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLB5z6MxymDbGZi7Pr6z611Tm6xN5Q", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/F9IiEgfy9Ig/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDtIyYXF4e34RRI-3dIo-IBXkERwg", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/F9IiEgfy9Ig/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCAD_DvggsAkVnJG-q99lfI7HGy2w", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "18:23", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "F9IiEgfy9Ig"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "F9IiEgfy9Ig", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CGYQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "F9IiEgfy9Ig", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CGYQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["F9IiEgfy9Ig"], "params": "CAQ%3D"}}, "videoIds": ["F9IiEgfy9Ig"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/F9IiEgfy9Ig/mqdefault_6s.webp?du=3000&sqp=COjH670G&rs=AOn4CLBDiAsSjQYpMc9o7pcPFFuPNWPzOw", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "F9IiEgfy9Ig", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr5---sn-oguesn6k.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=17d2221207f2f488&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=F9IiEgfy9Ig", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "3 months ago"}, "view_count": {"text": "324,654 views"}, "short_view_count": {"text": "324K views"}, "duration": {"text": "18:23", "seconds": 1103}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CGoQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "F9IiEgfy9Ig", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CGoQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["F9IiEgfy9Ig"], "params": "CAQ%3D"}}, "videoIds": ["F9IiEgfy9Ig"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "F9IiEgfy9Ig", "onAddCommand": {"clickTrackingParams": "CGkQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "F9IiEgfy9Ig", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtGOUlpRWdmeTlJZw%3D%3D", "commands": [{"clickTrackingParams": "CGUQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CGgQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "Y84A9whuth8", "title": {"runs": [{"text": "What Is The Samsung Music Frame?", "bold": false, "italics": false, "strikethrough": false}], "text": "What Is The Samsung Music Frame?"}, "description_snippet": {"runs": [{"text": "This video was made in partnership with Samsung. Samsung Music Frame:   https://www.samsung.com/audio \n\nAs an audio reviewer, I’ve tested hundreds of speakers but I’ve never tested one...", "bold": false, "italics": false, "strikethrough": false}], "text": "This video was made in partnership with Samsung. Samsung Music Frame:   https://www.samsung.com/audio \n\nAs an audio reviewer, I’ve tested hundreds of speakers but I’ve never tested one..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/Y84A9whuth8/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAd1bb0PPn3D8PkDYw-5Gb_PtnZPg", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/Y84A9whuth8/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCYyBUFPGeEntj8l9stPH-5FgO_4A", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/Y84A9whuth8/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLB0lny7FE1GfzI2QAApCwvVeCeZfg", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/Y84A9whuth8/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCbMTUdWkTz1m0vH2gosXa7bMy2nA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "13:37", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "Y84A9whuth8"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "Y84A9whuth8", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CF8Qx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "Y84A9whuth8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CF8Qx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["Y84A9whuth8"], "params": "CAQ%3D"}}, "videoIds": ["Y84A9whuth8"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/Y84A9whuth8/mqdefault_6s.webp?du=3000&sqp=CKab670G&rs=AOn4CLBTEjMWBPamA4ehtKigrNmlloNfTQ", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "Y84A9whuth8", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr5---sn-oguesn6y.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=63ce00f7086eb61f&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=Y84A9whuth8", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "3 months ago"}, "view_count": {"text": "119,121 views"}, "short_view_count": {"text": "119K views"}, "duration": {"text": "13:37", "seconds": 817}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CGMQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "Y84A9whuth8", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CGMQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["Y84A9whuth8"], "params": "CAQ%3D"}}, "videoIds": ["Y84A9whuth8"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "Y84A9whuth8", "onAddCommand": {"clickTrackingParams": "CGIQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "Y84A9whuth8", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtZODRBOXdodXRoOA%3D%3D", "commands": [{"clickTrackingParams": "CF4Q3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CGEQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "fwUzrgOj_uM", "title": {"runs": [{"text": "2024 Earbuds Awards - Unexpected Winners!", "bold": false, "italics": false, "strikethrough": false}], "text": "2024 Earbuds Awards - Unexpected Winners!"}, "description_snippet": {"runs": [{"text": "PRODUCT LINKS ⬇️ (affiliate links support the channel)\n🎧 Galaxy Buds3 Pro 👉 https://mikeobrienmedia.com/GalaxyBuds3Pro\n🎧 Sony WF-1000XM5 👉 https://mikeobrienmedia.com/SonyXM5...", "bold": false, "italics": false, "strikethrough": false}], "text": "PRODUCT LINKS ⬇️ (affiliate links support the channel)\n🎧 Galaxy Buds3 Pro 👉 https://mikeobrienmedia.com/GalaxyBuds3Pro\n🎧 Sony WF-1000XM5 👉 https://mikeobrienmedia.com/SonyXM5..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/fwUzrgOj_uM/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDVPueiQbg6fAxXFWM6R7fZKrZt2A", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/fwUzrgOj_uM/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCwvFU2vJeWpWNn-YP9Zly9iFSVOg", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/fwUzrgOj_uM/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBBE2YytCJjRRvZVh9s5yLsYSPtOw", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/fwUzrgOj_uM/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLAtD8-4slTFPfCL2ZDv7B4nMALytQ", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "16:35", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "fwUzrgOj_uM"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "fwUzrgOj_uM", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CFgQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "fwUzrgOj_uM", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CFgQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["fwUzrgOj_uM"], "params": "CAQ%3D"}}, "videoIds": ["fwUzrgOj_uM"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/fwUzrgOj_uM/mqdefault_6s.webp?du=3000&sqp=COG5670G&rs=AOn4CLBdPcQcO0cDsryQcfybk9wr3oalOw", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "fwUzrgOj_uM", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr3---sn-oguelnsy.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=7f0533ae03a3fee3&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=fwUzrgOj_uM", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "3 months ago"}, "view_count": {"text": "558,526 views"}, "short_view_count": {"text": "558K views"}, "duration": {"text": "16:35", "seconds": 995}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CFwQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "fwUzrgOj_uM", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CFwQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["fwUzrgOj_uM"], "params": "CAQ%3D"}}, "videoIds": ["fwUzrgOj_uM"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "fwUzrgOj_uM", "onAddCommand": {"clickTrackingParams": "CFsQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "fwUzrgOj_uM", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "Cgtmd1V6cmdPal91TQ%3D%3D", "commands": [{"clickTrackingParams": "CFcQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CFoQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "K71CXAO636U", "title": {"runs": [{"text": "Is The Samsung Ecosystem Worth It?", "bold": false, "italics": false, "strikethrough": false}], "text": "Is The Samsung Ecosystem Worth It?"}, "description_snippet": {"runs": [{"text": "So Samsung Galaxy phones, wearables, Tvs, laptops, appliances, even smart refrigerators. Although we hear a lot of talk about the Apple Ecosystem, Samsung’s is by far the largest. But with...", "bold": false, "italics": false, "strikethrough": false}], "text": "So Samsung Galaxy phones, wearables, Tvs, laptops, appliances, even smart refrigerators. Although we hear a lot of talk about the Apple Ecosystem, Samsung’s is by far the largest. But with..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/K71CXAO636U/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDBmAcbDV4QHMXy-0EHpVp8K3fgrw", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/K71CXAO636U/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAXrm7sQdSi1I0euJv3Hp_8hm8dbA", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/K71CXAO636U/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCbQOjDkb9ueCWETbVhKerZSWj9-Q", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/K71CXAO636U/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLC-r0lusUVfE70giqvxgCvKtpOGsg", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "16:53", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "K71CXAO636U"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "K71CXAO636U", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CFEQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "K71CXAO636U", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CFEQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["K71CXAO636U"], "params": "CAQ%3D"}}, "videoIds": ["K71CXAO636U"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/K71CXAO636U/mqdefault_6s.webp?du=3000&sqp=COWB670G&rs=AOn4CLAsij5IrEzqccYuwQzEG62vHoQPPQ", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "K71CXAO636U", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr1---sn-oguelnsz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=2bbd425c03badfa5&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=K71CXAO636U", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "3 months ago"}, "view_count": {"text": "301,772 views"}, "short_view_count": {"text": "301K views"}, "duration": {"text": "16:53", "seconds": 1013}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CFUQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "K71CXAO636U", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CFUQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["K71CXAO636U"], "params": "CAQ%3D"}}, "videoIds": ["K71CXAO636U"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "K71CXAO636U", "onAddCommand": {"clickTrackingParams": "CFQQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "K71CXAO636U", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtLNzFDWEFPNjM2VQ%3D%3D", "commands": [{"clickTrackingParams": "CFAQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CFMQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "ZK-30IMSoYY", "title": {"runs": [{"text": "The Best Bluetooth Speakers in 2024 (by Category)", "bold": false, "italics": false, "strikethrough": false}], "text": "The Best Bluetooth Speakers in 2024 (by Category)"}, "description_snippet": {"runs": [{"text": "PRODUCT LINKS ⬇️ (affiliate) \nSony ULT Tower 10 👉 https://mikeobrienmedia.com/SonyULT10\nJBL Clip 5 👉 https://mikeobrienmedia.com/JBLClip5\nSamsung Music Frame 👉 https://mikeobrienmedia....", "bold": false, "italics": false, "strikethrough": false}], "text": "PRODUCT LINKS ⬇️ (affiliate) \nSony ULT Tower 10 👉 https://mikeobrienmedia.com/SonyULT10\nJBL Clip 5 👉 https://mikeobrienmedia.com/JBLClip5\nSamsung Music Frame 👉 https://mikeobrienmedia...."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/ZK-30IMSoYY/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAWLEqIIwIbhFitisgXg6cEVJfc5g", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/ZK-30IMSoYY/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDsZhHRylfQ-eSOa4u8bzWI5AACcA", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/ZK-30IMSoYY/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLBmmKr6LOBxrSExG-sMZij2P9F2Aw", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/ZK-30IMSoYY/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCHUAqmzF3hjR7r273_zUL46aOs9Q", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "15:05", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "ZK-30IMSoYY"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "ZK-30IMSoYY", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CEoQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "ZK-30IMSoYY", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CEoQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["ZK-30IMSoYY"], "params": "CAQ%3D"}}, "videoIds": ["ZK-30IMSoYY"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/ZK-30IMSoYY/mqdefault_6s.webp?du=3000&sqp=CJ6r670G&rs=AOn4CLD2ty1ypkyADRx9xbKGKHYxMUq4pw", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "ZK-30IMSoYY", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr5---sn-oguelnl7.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=64afb7d08312a186&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=ZK-30IMSoYY", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "4 months ago"}, "view_count": {"text": "149,657 views"}, "short_view_count": {"text": "149K views"}, "duration": {"text": "15:05", "seconds": 905}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CE4Q_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "ZK-30IMSoYY", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CE4Q_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["ZK-30IMSoYY"], "params": "CAQ%3D"}}, "videoIds": ["ZK-30IMSoYY"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "ZK-30IMSoYY", "onAddCommand": {"clickTrackingParams": "CE0Q0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "ZK-30IMSoYY", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtaSy0zMElNU29ZWQ%3D%3D", "commands": [{"clickTrackingParams": "CEkQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CEwQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "juQ1IMBY9VQ", "title": {"runs": [{"text": "What Has Bose Done?? - NEW 2024 QuietComfort Earbuds", "bold": false, "italics": false, "strikethrough": false}], "text": "What Has Bose Done?? - NEW 2024 QuietComfort Earbuds"}, "description_snippet": {"runs": [{"text": "Bose Quietcomfort Earbuds latest price (affiliate) 👉 https://mikeobrienmedia.com/boseQCE2024\n\nBose just launched their new 2024 QuietComfort Earbuds and they absolutely nail the fundamentals...", "bold": false, "italics": false, "strikethrough": false}], "text": "Bose Quietcomfort Earbuds latest price (affiliate) 👉 https://mikeobrienmedia.com/boseQCE2024\n\nBose just launched their new 2024 QuietComfort Earbuds and they absolutely nail the fundamentals..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/juQ1IMBY9VQ/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLArZF9ezbsKSqBYLTBcILBQjyrvIA", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/juQ1IMBY9VQ/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLDUSdnUK-JduSjDMdmW6IunVUm-xg", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/juQ1IMBY9VQ/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLA-YNZnfqgdyz7OmsynWVyI1hWnMA", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/juQ1IMBY9VQ/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLC1NamPmus0y-WkYsZNqoZ1ng6eGA", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "13:15", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "juQ1IMBY9VQ"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "juQ1IMBY9VQ", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CEMQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "juQ1IMBY9VQ", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CEMQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["juQ1IMBY9VQ"], "params": "CAQ%3D"}}, "videoIds": ["juQ1IMBY9VQ"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/juQ1IMBY9VQ/mqdefault_6s.webp?du=3000&sqp=CMDH670G&rs=AOn4CLBPmvRZYE72VpU9GOV7pwT0IA7E2Q", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "juQ1IMBY9VQ", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr1---sn-oguesndz.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=8ee43520c058f554&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=juQ1IMBY9VQ", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "4 months ago"}, "view_count": {"text": "56,056 views"}, "short_view_count": {"text": "56K views"}, "duration": {"text": "13:15", "seconds": 795}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CEcQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "juQ1IMBY9VQ", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CEcQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["juQ1IMBY9VQ"], "params": "CAQ%3D"}}, "videoIds": ["juQ1IMBY9VQ"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "juQ1IMBY9VQ", "onAddCommand": {"clickTrackingParams": "CEYQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "juQ1IMBY9VQ", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtqdVExSU1CWTlWUQ%3D%3D", "commands": [{"clickTrackingParams": "CEIQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CEUQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "hR30piPv9W0", "title": {"runs": [{"text": "I've Never Seen <PERSON>ds Do This... (Soundcore Liberty 4 Pro)", "bold": false, "italics": false, "strikethrough": false}], "text": "I've Never Seen <PERSON>ds Do This... (Soundcore Liberty 4 Pro)"}, "description_snippet": {"runs": [{"text": "Soundcore Liberty 4 Pro 👉 https://amzn.to/3XTIG2u (use code LIBERTY4PRO for the sale)\n\nThese are the new Soundcore Liberty 4 Pro and these are wild. They have 7 crazy features that really...", "bold": false, "italics": false, "strikethrough": false}], "text": "Soundcore Liberty 4 Pro 👉 https://amzn.to/3XTIG2u (use code LIBERTY4PRO for the sale)\n\nThese are the new Soundcore Liberty 4 Pro and these are wild. They have 7 crazy features that really..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/hR30piPv9W0/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLAcIjFm-Fuk62PJ4a3lqRjqIiTJ8A", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/hR30piPv9W0/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLCyqKQMfSU9MHF7azkVXNNJrMkvTg", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/hR30piPv9W0/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCiGhANZvQ9jCKuwe1leKKmg2Q_rA", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/hR30piPv9W0/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCHANgnf5mo6yVm_zbsX4bIUVcHZg", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "11:58", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "hR30piPv9W0"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "hR30piPv9W0", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CDwQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "hR30piPv9W0", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CDwQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["hR30piPv9W0"], "params": "CAQ%3D"}}, "videoIds": ["hR30piPv9W0"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/hR30piPv9W0/mqdefault_6s.webp?du=3000&sqp=CKKX670G&rs=AOn4CLBPDHEGDAjA1Qvh0M3L8x3MVYhLSw", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "hR30piPv9W0", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr1---sn-oguelnzs.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=851df4a623eff56d&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=hR30piPv9W0", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "4 months ago"}, "view_count": {"text": "101,148 views"}, "short_view_count": {"text": "101K views"}, "duration": {"text": "11:58", "seconds": 718}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CEAQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "hR30piPv9W0", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CEAQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["hR30piPv9W0"], "params": "CAQ%3D"}}, "videoIds": ["hR30piPv9W0"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "hR30piPv9W0", "onAddCommand": {"clickTrackingParams": "CD8Q0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "hR30piPv9W0", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtoUjMwcGlQdjlXMA%3D%3D", "commands": [{"clickTrackingParams": "CDsQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CD4QjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "d73Pe1GdNMI", "title": {"runs": [{"text": "A Day In The Life w/ Sony’s New Audio Ecosystem (Linkbuds Fit, Open, & Speaker!)", "bold": false, "italics": false, "strikethrough": false}], "text": "A Day In The Life w/ Sony’s New Audio Ecosystem (Linkbuds Fit, Open, & Speaker!)"}, "description_snippet": {"runs": [{"text": "This video was sponsored by Sony\nSony Linkbuds Fit 👉 https://mikeobrienmedia.com/LinkbudsFit\nSony Linkbuds Open 👉 https://mikeobrienmedia.com/LinkbudsOpen\nSony Linkbuds Speaker 👉 https://m...", "bold": false, "italics": false, "strikethrough": false}], "text": "This video was sponsored by Sony\nSony Linkbuds Fit 👉 https://mikeobrienmedia.com/LinkbudsFit\nSony Linkbuds Open 👉 https://mikeobrienmedia.com/LinkbudsOpen\nSony Linkbuds Speaker 👉 https://m..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/d73Pe1GdNMI/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLC1LykfIHW7A-SyNVsgWmrYzUKfRQ", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/d73Pe1GdNMI/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBiwWaBVoJ5r5vu7zZCQUARf5HEqg", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/d73Pe1GdNMI/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLDWD0fVbYAaMjZBxX7wzkz6lupkog", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/d73Pe1GdNMI/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLCfIkBEKYqCXBOSVwZg6zN2Lgh8kQ", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "18:21", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "d73Pe1GdNMI"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "d73Pe1GdNMI", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CDUQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "d73Pe1GdNMI", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CDUQx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["d73Pe1GdNMI"], "params": "CAQ%3D"}}, "videoIds": ["d73Pe1GdNMI"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/d73Pe1GdNMI/mqdefault_6s.webp?du=3000&sqp=CIaa670G&rs=AOn4CLAtDmwKIPUzZPw36HMRwlFdj_43JA", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "d73Pe1GdNMI", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr4---sn-oguesndl.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=77bdcf7b519d34c2&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=d73Pe1GdNMI", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "4 months ago"}, "view_count": {"text": "34,826 views"}, "short_view_count": {"text": "34K views"}, "duration": {"text": "18:21", "seconds": 1101}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CDkQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "d73Pe1GdNMI", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CDkQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["d73Pe1GdNMI"], "params": "CAQ%3D"}}, "videoIds": ["d73Pe1GdNMI"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "d73Pe1GdNMI", "onAddCommand": {"clickTrackingParams": "CDgQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "d73Pe1GdNMI", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtkNzNQZTFHZE5NSQ%3D%3D", "commands": [{"clickTrackingParams": "CDQQ3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CDcQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}, {"type": "Video", "id": "_6nE5QpfxgI", "title": {"runs": [{"text": "I Tried Every Pair of AirPods - DON'T CHOOSE WRONG!", "bold": false, "italics": false, "strikethrough": false}], "text": "I Tried Every Pair of AirPods - DON'T CHOOSE WRONG!"}, "description_snippet": {"runs": [{"text": "AirPods 4 vs AirPods Pro 2 vs AirPods Max, vs AirPods 4 ANC. Which are the best airpods to buy? I tested every pair of AirPods that ever launched and now with the new AirPods 4 and AirPods...", "bold": false, "italics": false, "strikethrough": false}], "text": "AirPods 4 vs AirPods Pro 2 vs AirPods Max, vs AirPods 4 ANC. Which are the best airpods to buy? I tested every pair of AirPods that ever launched and now with the new AirPods 4 and AirPods..."}, "expandable_metadata": null, "thumbnails": [{"url": "https://i.ytimg.com/vi/_6nE5QpfxgI/hqdefault.jpg?sqp=-oaymwEcCNACELwBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLA5RUdDkyfTlS6eVCrKkOKShhEgtw", "width": 336, "height": 188}, {"url": "https://i.ytimg.com/vi/_6nE5QpfxgI/hqdefault.jpg?sqp=-oaymwEcCPYBEIoBSFXyq4qpAw4IARUAAIhCGAFwAcABBg==&rs=AOn4CLBBpyE0TWXy9dnVyeRyK7Lh62PrXQ", "width": 246, "height": 138}, {"url": "https://i.ytimg.com/vi/_6nE5QpfxgI/hqdefault.jpg?sqp=-oaymwEbCMQBEG5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLB1YNZ8b7E-U0XDMxpVaqZM4hd_2A", "width": 196, "height": 110}, {"url": "https://i.ytimg.com/vi/_6nE5QpfxgI/hqdefault.jpg?sqp=-oaymwEbCKgBEF5IVfKriqkDDggBFQAAiEIYAXABwAEG&rs=AOn4CLA3RbBoGaQIoHXDoixBdXiXL94BrQ", "width": 168, "height": 94}], "thumbnail_overlays": [{"type": "ThumbnailOverlayTimeStatus", "text": "23:34", "style": "DEFAULT"}, {"type": "ThumbnailOverlayToggleButton", "is_toggled": false, "icon_type": {"toggled": "CHECK", "untoggled": "WATCH_LATER"}, "tooltip": {"toggled": "Added", "untoggled": "Watch later"}, "toggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"action": "ACTION_REMOVE_VIDEO_BY_VIDEO_ID", "removedVideoId": "_6nE5QpfxgI"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"playlistId": "WL", "actions": [{"addedVideoId": "_6nE5QpfxgI", "action": "ACTION_ADD_VIDEO"}]}, "metadata": {"api_url": "browse/edit_playlist", "send_post": true}}}, {"type": "ThumbnailOverlayToggleButton", "icon_type": {"toggled": "PLAYLIST_ADD_CHECK", "untoggled": "ADD_TO_QUEUE_TAIL"}, "tooltip": {"toggled": "Added", "untoggled": "Add to queue"}, "untoggled_endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CC4Qx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "_6nE5QpfxgI", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CC4Qx-wEGAMiEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["_6nE5QpfxgI"], "params": "CAQ%3D"}}, "videoIds": ["_6nE5QpfxgI"]}}]}, "metadata": {"send_post": true}}}, {"type": "ThumbnailOverlayNowPlaying", "text": "Now playing"}], "rich_thumbnail": [{"url": "https://i.ytimg.com/an_webp/_6nE5QpfxgI/mqdefault_6s.webp?du=3000&sqp=CJiK670G&rs=AOn4CLDhORgT21NbJcYsBgZPQLTzbIUgbw", "width": 320, "height": 180}], "author": {"id": "N/A", "name": "N/A", "thumbnails": [], "badges": [{"type": "MetadataBadge", "icon_type": "CHECK_CIRCLE_THICK", "style": "BADGE_STYLE_TYPE_VERIFIED", "tooltip": "Verified"}], "is_moderator": false, "is_verified": true, "is_verified_artist": false, "url": "https://www.youtube.com/u/undefined"}, "badges": [], "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "_6nE5QpfxgI", "watchEndpointSupportedOnesieConfig": {"html5PlaybackOnesieConfig": {"commonConfig": {"url": "https://rr4---sn-ogul7n7k.googlevideo.com/initplayback?source=youtube&oeis=1&c=WEB&oad=3200&ovd=3200&oaad=11000&oavd=11000&ocs=700&oewis=1&oputc=1&ofpcc=1&msp=1&odepv=1&id=ffa9c4e50a5fc602&ip=**************&initcwndbps=3772500&mt=1740301280&oweuc=&pxtags=Cg4KAnR4Egg1MTM5MzE2MQ&rxtags=Cg4KAnR4Egg1MTM5MzE2MA%2CCg4KAnR4Egg1MTM5MzE2MQ%2CCg4KAnR4Egg1MTM5MzE2Mg%2CCg4KAnR4Egg1MTM5MzE2Mw%2CCg4KAnR4Egg1MTM5MzE2NA"}}}}, "metadata": {"url": "/watch?v=_6nE5QpfxgI", "page_type": "WEB_PAGE_TYPE_WATCH", "api_url": "/player"}}, "published": {"text": "4 months ago"}, "view_count": {"text": "141,498 views"}, "short_view_count": {"text": "141K views"}, "duration": {"text": "23:34", "seconds": 1414}, "show_action_menu": false, "is_watched": false, "menu": {"type": "<PERSON><PERSON>", "items": [{"type": "MenuServiceItem", "text": "Add to queue", "icon_type": "ADD_TO_QUEUE_TAIL", "endpoint": {"type": "NavigationEndpoint", "payload": {"signal": "CLIENT_SIGNAL", "actions": [{"clickTrackingParams": "CDIQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "addToPlaylistCommand": {"openMiniplayer": true, "videoId": "_6nE5QpfxgI", "listType": "PLAYLIST_EDIT_LIST_TYPE_QUEUE", "onCreateListCommand": {"clickTrackingParams": "CDIQ_pgEGAciEwiolte5udmLAxXpfQ8CHULSNK0=", "commandMetadata": {"webCommandMetadata": {"sendPost": true, "apiUrl": "/youtubei/v1/playlist/create"}}, "createPlaylistServiceEndpoint": {"videoIds": ["_6nE5QpfxgI"], "params": "CAQ%3D"}}, "videoIds": ["_6nE5QpfxgI"]}}]}, "metadata": {"send_post": true}}}, {"type": "MenuServiceItemDownload", "has_separator": false, "endpoint": {"type": "NavigationEndpoint", "payload": {"videoId": "_6nE5QpfxgI", "onAddCommand": {"clickTrackingParams": "CDEQ0aoFGAgiEwiolte5udmLAxXpfQ8CHULSNK0=", "getDownloadActionCommand": {"videoId": "_6nE5QpfxgI", "params": "CAIQAA%3D%3D"}}}, "metadata": {}}}, {"type": "MenuServiceItem", "text": "Share", "icon_type": "SHARE", "endpoint": {"type": "NavigationEndpoint", "payload": {"serializedShareEntity": "CgtfNm5FNVFwZnhnSQ%3D%3D", "commands": [{"clickTrackingParams": "CC0Q3DAiEwiolte5udmLAxXpfQ8CHULSNK0=", "openPopupAction": {"popup": {"unifiedSharePanelRenderer": {"trackingParams": "CDAQjmIiEwiolte5udmLAxXpfQ8CHULSNK0=", "showLoadingSpinner": true}}, "popupType": "DIALOG", "beReused": true}}]}, "metadata": {"api_url": "share/get_share_panel", "send_post": true}}}], "top_level_buttons": [], "label": "Action menu"}}]