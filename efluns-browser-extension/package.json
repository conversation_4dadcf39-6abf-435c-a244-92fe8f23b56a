{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "manifest.json description", "private": true, "version": "2.5.5", "type": "module", "scripts": {"dev": "wxt", "dev:mock": "wxt -m mock", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:staging": "pnpm run build -m staging", "build:firefox": "wxt build -b firefox", "clean": "wxt clean", "zip:staging": "pnpm zip -m staging", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare", "postversion": "git push && git push --tags && pnpm zip && open .output", "lint": "prettier . --check", "format": "yarn lint --write", "push": "git tag v$npm_package_version && git push origin v$npm_package_version"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@fingerprintjs/fingerprintjs": "^4.5.1", "@floating-ui/react": "^0.26.16", "@fortawesome/fontawesome-free": "^6.5.2", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@sentry/browser": "^9.5.0", "@sentry/tracing": "^7.120.3", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.43.4", "@tanstack/react-query": "^5.40.0", "@types/uuid": "^10.0.0", "@webext-core/messaging": "^1.4.0", "@webext-core/proxy-service": "^1.2.0", "axios": "^1.7.2", "clsx": "^2.1.1", "consola": "^3.2.3", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "dayjs": "^1.11.11", "dom-to-image": "^2.6.0", "framer-motion": "^11.2.10", "html2canvas": "^1.4.1", "i18n-iso-countries": "^7.14.0", "jotai": "^2.8.2", "jotai-effect": "^1.0.0", "jotai-valtio": "^0.5.0", "lodash-es": "^4.17.21", "lucide-react": "^0.487.0", "promise-postmessage": "^3.5.0", "quill": "^2.0.2", "react": "^18.2.0", "react-activation": "^0.12.4", "react-d3-cloud": "^1.0.6", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.51.5", "react-hot-toast": "^2.4.1", "react-use": "^17.5.0", "sonner": "^2.0.5", "uuid": "^11.0.3", "valtio": "^1.13.2", "youtubei.js": "^10.3.0"}, "devDependencies": {"@types/dom-to-image": "^2.6.7", "@types/lodash-es": "^4.17.12", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "daisyui": "^4.11.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "jotai-devtools": "^0.9.1", "navigation-api-types": "^0.5.1", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-organize-imports": "^3.2.4", "stylelint": "^16.3.1", "stylelint-config-standard": "^36.0.0", "stylelint-prettier": "^5.0.0", "tailwindcss": "^3.4.3", "typescript": "^5.3.3", "wxt": "^0.19.23"}}