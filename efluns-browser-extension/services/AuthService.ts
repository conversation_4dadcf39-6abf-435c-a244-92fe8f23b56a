import { CREDENTIALS, EMAILS, USER_TIME_ZONE } from '@/constants'
import { post, put } from '@/utils/request'
import supabase from '@/utils/supabase'
import type { Session } from '@supabase/supabase-js'
import { defineProxyService } from '@webext-core/proxy-service'
import { upperCase } from 'lodash-es'
import { getCommonService } from './CommonService'
import { CookieSessionManager } from './CookieSessionManager'
import { getFirstLoginService } from './FirstLoginService'

import { createAnalysisPost } from '@/utils/analysisPost'
import { makeModuleLog } from '@/utils/log'
import { authSessionState, hasReportBrowserIdState } from '@/utils/storages'
import { browser } from 'wxt/browser'

const log = makeModuleLog('AuthService')
const $event = createAnalysisPost('AuthService')
const AUTH_CALLBACK_URL = `https://easykol.com/callback`

class AuthService {
	private refreshPromise: Promise<Session | null> | null = null

	async bindEmail(email: string, credentialId: string) {
		return post(EMAILS, {
			email,
			credentialId
		})
	}

	private async parseAuthCallbackURL(url: string) {
		try {
			log(`handling user OAuth callback ...`, url)

			// extract tokens from hash

			const hashMap = parseUrlHash(url)
			log({ hashMap })
			const accessToken = hashMap.get('access_token')
			const refreshToken = hashMap.get('refresh_token')
			const providerToken = hashMap.get('provider_token')
			const providerRefreshToken = hashMap.get('provider_refresh_token')

			if (!accessToken || !refreshToken) {
				throw new Error(`no supabase tokens found in URL hash`)
			}

			// check if they work
			const { data, error } = await supabase.auth.setSession({
				access_token: accessToken,
				refresh_token: refreshToken
			})
			if (error) throw error

			// persist session to storage
			await authSessionState.setValue(data.session)
			await this.syncSessionToWeb(data?.session)
			$event('login', {
				userId: data.session?.user?.id,
				email: data.session?.user?.email
			})
			hasReportBrowserIdState.setValue(false) //重新登录的时候，需要重新上报浏览器指纹

			// Handle first login
			const firstLoginService = getFirstLoginService()
			await firstLoginService.handleFirstLogin()

			// upload credentials to backend
			const provider = data.session?.user?.app_metadata?.provider
			const email = data.session?.user?.email
			if (provider && providerToken && providerRefreshToken) {
				// Extract scopes from the callback if available
				const providerScope = hashMap.get('provider_scope')
				const scopes = providerScope ? decodeURIComponent(providerScope).split(' ') : undefined

				post(CREDENTIALS, {
					provider: upperCase(provider),
					accessToken: decodeURIComponent(providerToken),
					refreshToken: decodeURIComponent(providerRefreshToken),
					scopes
				})
					.then((res) => res.data)
					.then(async (credential) => {
						if (email && credential.id) {
							this.bindEmail(email, credential.id)
							$event('credentialsSuccess', {
								email,
								credentialId: credential.id
							})
						} else {
							log('no email or credential id')
						}
					})
					.finally(() => {
						this.setTimeZone()
					})
			}

			const loginPageURL = browser.runtime.getURL('/Login.html')
			// finally redirect to a post oauth page
			await browser.tabs.update({ url: loginPageURL })
			log(`finished handling user OAuth callback`)
			// await getGoogleSheetService().getSpreadsheetInfo()
		} catch (error) {
			log(error)
		}
	}

	async syncSessionToWeb(session: Session | null) {
		if (!session) {
			return
		}
		try {
			// 使用新的 CookieSessionManager 来设置 cookies
			await CookieSessionManager.setSessionToCookies(session)
			log('成功同步 session 到网页')
		} catch (error) {
			log('同步 session 到网页失败', error)
		}
	}

	/**
	 * 从网页 cookie 同步 session 到插件
	 */
	async syncSessionFromWeb(): Promise<Session | null> {
		try {
			const webSession = await CookieSessionManager.getSessionFromCookies()
			if (!webSession) {
				log('网页端未找到 session')
				return null
			}

			// 设置到 Supabase
			const { data, error } = await supabase.auth.setSession({
				access_token: webSession.access_token,
				refresh_token: webSession.refresh_token
			})

			if (error) {
				log('设置 Supabase session 失败：', error)
				return null
			}

			// 保存到插件存储
			await authSessionState.setValue(data.session)
			log('成功从网页同步 session')
			return data.session
		} catch (error) {
			log('从网页同步 session 失败：', error)
			return null
		}
	}

	async refreshSession() {
		if (this.refreshPromise) {
			return this.refreshPromise
		}

		try {
			this.refreshPromise = (async () => {
				const currentSession = await authSessionState.getValue()
				log('开始刷新 session', { currentSession })

				// 检查 session 是否即将过期
				if (currentSession && !CookieSessionManager.isSessionExpiringSoon(currentSession, 10)) {
					log('Session 未过期，无需刷新')
					return currentSession
				}

				const { data, error } = await supabase.auth.refreshSession(currentSession ?? undefined)
				if (error) throw error

				// 保存新的 session
				await authSessionState.setValue(data.session)
				// 同步到网页端
				await this.syncSessionToWeb(data.session)

				$event('session_refreshed', {
					userEmail: data.session?.user?.email
				})

				return data.session
			})()

			return await this.refreshPromise
		} finally {
			this.refreshPromise = null
		}
	}

	async getSession(): Promise<Session | null> {
		return authSessionState.getValue()
	}

	async getUser() {
		const session = await this.getSession()
		return supabase.auth.getUser(session?.access_token).then((res) => {
			if (res.error) {
				log('getUser Error', res.error)
			}
			return res.data.user
		})
	}

	async setTimeZone() {
		put(USER_TIME_ZONE, {
			timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
		})
	}

	async logout(banOpenLoginPage: boolean = false) {
		await supabase.auth.signOut()
		await authSessionState.removeValue()
		log('logout')

		// 使用 CookieSessionManager 清除所有 session 相关的 cookies
		try {
			await CookieSessionManager.clearSessionCookies()
			log('成功清除所有 session cookies')
			// 重新打开登录页面
			if (!banOpenLoginPage) {
				this.openLoginPage()
			}
		} catch (error) {
			log('清除 cookies 失败', error)
		}

		$event('user_logout')
	}

	async openLoginPage() {
		const url = 'https://easykol.com/login'
		getCommonService().openTab({
			url
		})
	}

	watchAuthCallback() {
		browser.tabs.onUpdated.addListener((_tabId, changeInfo, _tab) => {
			if (changeInfo.url?.startsWith(AUTH_CALLBACK_URL)) {
				this.parseAuthCallbackURL(changeInfo.url)
			}
		})
	}
}

export const [registerAuthService, getAuthService] = defineProxyService('AuthService', () => new AuthService())

function parseUrlHash(url: string) {
	const hashParts = new URL(url).hash.slice(1).split('&')
	const hashMap = new Map(
		hashParts.map((part) => {
			const [name, value] = part.split('=')
			return [name, value]
		})
	)

	return hashMap
}
