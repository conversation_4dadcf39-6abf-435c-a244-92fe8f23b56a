import { Notifies } from '@/@types'
import { NOTIFIES, UPDATE_JUMP_URL } from '@/constants'
import { get } from '@/utils/request'
import { defineProxyService } from '@webext-core/proxy-service'
import { compact, first } from 'lodash-es'
import { Tabs } from 'wxt/browser'

const log = makeModuleLog('CommonService')
class CommonService {
	constructor() {
		onMessage('openSidePanel', async (message) => {
			const tabId = message.sender.tab?.id
			tabId && this.openSidePanel(tabId)
		})
		this.watchActiveTabURLChange()
	}

	async getActiveTab(){
		const tabs = await browser.tabs.query({ active: true, currentWindow: true })
		return tabs[0]
	}

	async closeActiveTab() {
		// find out actived tab
		const tabs = await browser.tabs.query({ active: true, currentWindow: true })
		const tabIds = compact(tabs.map((item) => item.id))

		browser.tabs.remove(tabIds)
	}

	async getActiveTabId() {
		return browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => tabs[0]?.id)
	}

	async closeTabByURL(url: string) {
		const tabs = await browser.tabs.query({})
		const matchedTabs = tabs.filter(tab => tab.url?.includes(url))
		const tabIds = compact(matchedTabs.map((item) => item.id))

		return browser.tabs.remove(tabIds)
	}

	async closeTab(tabIds: number | number[]) {
		return browser.tabs.remove(tabIds)
	}

	async activeTabUrl(url:string){
		const tabs = await browser.tabs.query({ url })
		if(tabs.length > 0){
			await browser.tabs.update(tabs[0].id, { active: true })
		}
	}

	async activateTab(tabId: number) {
		return browser.tabs.update(tabId, { active: true })
	}

	async getTabsByURL(url: string) {
		return browser.tabs.query({ url })
	}

	async openTabIfNotExist(url: string, options?: Tabs.CreateCreatePropertiesType) {
		const tabs = await this.getTabsByURL(url)
		if (tabs.length > 0) {
			return tabs[0]
		}
		return await this.openTab({ url, ...options })
	}

	async openTab(options: Tabs.CreateCreatePropertiesType) {
		return browser.tabs.create(options)
	}

	async getActiveTabURL() {
		return browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => {
			const tab = first(tabs)
			const url = tab?.pendingUrl ?? tab?.url
			return url
		})
	}

	async sendMessageToCurrentYoutubeContent(options: { [key: string]: any }){
		const youtubeTabs = await browser.tabs.query({url:"https://www.youtube.com/*"})
		const currentTabId = youtubeTabs.find(tab=>tab.active)?.id || youtubeTabs[0].id
		if(!currentTabId) return false
		try {
			return await browser.tabs.sendMessage(currentTabId,options) as any
		} catch (error) {
			return null
		}
	}

	async sendMessageToCurrentContent(options: { [key: string]: any },needRetry:boolean = false) {
		const currentTabId = await this.getActiveTabId()
		if(!currentTabId) return false
		
		async function waitForTabIdle(tabId: number) {
			return new Promise((resolve) => {
				const listener = (changedTabId: number, changeInfo: any) => {
					if (changedTabId === tabId && changeInfo.status === 'complete') {
						browser.tabs.onUpdated.removeListener(listener)
						// 给页面一点额外时间加载 content script
						setTimeout(resolve, 500)
					}
				}
				browser.tabs.onUpdated.addListener(listener)
			})
		}
	
		try {
			return await browser.tabs.sendMessage(currentTabId, options)
		} catch (error) {
			if (error instanceof Error && 
				needRetry &&
				(error.message.includes('connection') || 
				 error.message.includes('message port closed'))) {
				await browser.tabs.reload(currentTabId)
				// 等待页面重新加载完成
				await waitForTabIdle(currentTabId)
				// 重试一次
				try {
					return await browser.tabs.sendMessage(currentTabId, options)
				} catch (retryError) {
					console.log('重试失败:', retryError)
					return false
				}
			}
			return false
		}
	}

	private async openSidePanel(tabId: number) {
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		// @ts-ignore
		await browser.sidePanel.open({
			tabId
		})
		
		// 广播消息到所有标签页，通知侧边栏已打开
		try {
			const tabs = await browser.tabs.query({})
			for (const tab of tabs) {
				if (tab.id) {
					browser.tabs.sendMessage(tab.id, { type: 'sidePanelOpened' }).catch(() => {
						// 忽略错误，某些标签页可能没有内容脚本
					})
				}
			}
		} catch (error) {
			log('Failed to broadcast sidePanelOpened message:', error)
		}
	}

	private watchActiveTabURLChange() {
		const handleActiveTabChange = (tab: Tabs.Tab) => {
			if (!tab.active || !tab.url) {
				return
			}
			activeTabURLState.setValue(tab.url)
		}
		browser.tabs.onActivated.addListener(async (activeInfo) => {
			const tab = await browser.tabs.get(activeInfo.tabId)
			handleActiveTabChange(tab)
		})
		browser.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
			handleActiveTabChange(tab)
			if(changeInfo.status === "complete"){
				this.checkUpdate(tab.url)
			}
		})
	}

	async checkUpdate(url:string | undefined){
		if(!url) return
		if(["youtube.com","tiktok.com","instagram.com"].some(e=>url.includes(e))) {
			const updateJumpURL = await updateJumpURLState.getValue()
			if(updateJumpURL){
				await updateJumpURLState.setValue('')
				browser.windows.create({
					url:updateJumpURL,
					type:"popup",
					width:800,
					height:600,
					left:400,
					top:200
				})
			}
		}

	}

	private async refreshNotifies(){
		try {
			const data = await get<{data:Notifies}>(NOTIFIES).then(res=>res.data.data)
			await notifiesState.setValue(data)
			return data
		} catch (error) {
			log('refreshNotifies',error)
			return []
		}
	}

	async pollNotifies(){
		const lastNotifiesTime = await lastNotifiesTimeState.getValue()
		const now = Date.now()
		if(now - lastNotifiesTime < 1000*60*10){
			return
		}
		await lastNotifiesTimeState.setValue(now)
		await this.refreshNotifies()
	}


	async getHistorySessionByURL(url:string){
		const sessions = await browser.sessions.getRecentlyClosed({
			maxResults:10
		})
		return sessions.find(session=>session.tab?.url === url)
	}

	async restoreSessionByURL(url:string){
		const session = await this.getHistorySessionByURL(url)
		if(session){
			await browser.sessions.restore(session.tab?.sessionId)
		}
	}

	
}

export const [registerCommonService, getCommonService] = defineProxyService('CommonService', () => new CommonService())
