import { makeModuleLog } from '@/utils/log'
import type { Session } from '@supabase/supabase-js'
import { browser } from 'wxt/browser'

const log = makeModuleLog('CookieSessionManager')

export class CookieSessionManager {
	private static readonly COOKIE_DOMAIN = 'easykol.com'
	private static readonly COOKIE_URL = 'https://easykol.com'

	/**
	 * 从 easykol.com 的 cookies 中获取 session
	 */
	static async getSessionFromCookies(): Promise<Session | null> {
		try {
			// 获取 easykol.com 的所有 cookies
			const cookies = await browser.cookies.getAll({
				url: this.COOKIE_URL
			})

			if (!cookies || cookies.length === 0) {
				log('No cookies found for domain:', this.COOKIE_DOMAIN)
				return null
			}

			// 构建 session 对象
			const session: any = {}

			// 将 cookies 转换为 session 对象
			cookies.forEach((cookie) => {
				const key = decodeURIComponent(cookie.name)
				let value: any = decodeURIComponent(cookie.value)

				// 尝试解析 JSON 值
				try {
					value = JSON.parse(value)
				} catch {
					// 保持字符串值
				}

				session[key] = value
			})

			// 确保 expires_at 是数字类型
			if (session.expires_at && typeof session.expires_at === 'string') {
				session.expires_at = parseInt(session.expires_at)
			}

			log('Session extracted from cookies:', session)
			return session as Session
		} catch (error) {
			log('Error getting session from cookies:', error)
			return null
		}
	}

	/**
	 * 将 session 保存到 cookies
	 */
	static async setSessionToCookies(session: Session | null): Promise<void> {
		try {
			// 设置每个 session 属性为独立的 cookie
			for (const [key, value] of Object.entries(session || {})) {
				const encodedKey = encodeURIComponent(key)
				const encodedValue =
					typeof value === 'object' ? encodeURIComponent(JSON.stringify(value)) : encodeURIComponent(String(value))
				console.log('update cookie', {
					name: encodedKey,
					value: encodedValue,
					path: '/',
					sameSite: 'lax',
					secure: true
				})

				await browser.cookies.set({
					url: this.COOKIE_URL,
					name: encodedKey,
					value: encodedValue,
					// 不设置 domain，这样 cookie 只会应用于确切的主机名 easykol.com
					path: '/',
					sameSite: 'lax',
					secure: true
				})
			}

			log('Session saved to cookies successfully')
		} catch (error) {
			log('Error setting session to cookies:', error)
			throw error
		}
	}

	/**
	 * 清除 easykol.com 的所有 session 相关 cookies
	 */
	static async clearSessionCookies(): Promise<void> {
		try {
			// 获取所有 easykol.com 的 cookies
			const cookies = await browser.cookies.getAll({
				domain: this.COOKIE_DOMAIN
			})

			// 删除所有 cookies
			for (const cookie of cookies) {
				await browser.cookies.remove({
					url: this.COOKIE_URL,
					name: cookie.name
				})
			}

			log('All session cookies cleared')
		} catch (error) {
			log('Error clearing session cookies:', error)
			throw error
		}
	}

	/**
	 * 比较两个 session 的过期时间，返回更新的那个
	 */
	static getLatestSession(session1: Session | null, session2: Session | null): Session | null {
		if (!session1 && !session2) return null
		if (!session1) return session2
		if (!session2) return session1

		const expires1 = parseInt(session1.expires_at as any) || 0
		const expires2 = parseInt(session2.expires_at as any) || 0

		return expires1 > expires2 ? session1 : session2
	}

	/**
	 * 检查 session 是否即将过期（默认 5 分钟内）
	 */
	static isSessionExpiringSoon(session: Session | null, thresholdMinutes = 5): boolean {
		if (!session || !session.expires_at) {
			return true
		}

		const expiresAt = parseInt(session.expires_at as any)
		const now = Date.now()
		const thresholdMs = thresholdMinutes * 60 * 1000

		return expiresAt - now < thresholdMs
	}

	/**
	 * 监听特定 cookie 的变化
	 */
	static addCookieChangeListener(callback: (session: Session | null) => void): () => void {
		const listener = async (changeInfo: any) => {
			// 只监听 easykol.com 域名的 cookie 变化
			// 由于我们不设置 domain 属性，cookie 只会应用于确切的主机名
			if (changeInfo.cookie.domain !== this.COOKIE_DOMAIN) {
				return
			}
			log('Cookie changed:', changeInfo)
			// 获取最新的 session
			const session = await this.getSessionFromCookies()
			callback(session)
		}

		// 添加监听器
		browser.cookies.onChanged.addListener(listener)

		// 返回清理函数
		return () => {
			browser.cookies.onChanged.removeListener(listener)
		}
	}
}
