import { CREDENTIALS } from '@/constants'
import { createAnalysisPost } from '@/utils/analysisPost'
import { isDev } from '@/utils/isDev'
import { makeModuleLog } from '@/utils/log'
import { post } from '@/utils/request'
import { authSessionState, hasReportBrowserIdState } from '@/utils/storages'
import supabase from '@/utils/supabase'
import type { Session } from '@supabase/supabase-js'
import { defineProxyService } from '@webext-core/proxy-service'
import { upperCase } from 'lodash-es'
import { getAuthService } from './AuthService'
import { CookieSessionManager } from './CookieSessionManager'
import { getFirstLoginService } from './FirstLoginService'

const log = makeModuleLog('CookieSyncService')
const $event = createAnalysisPost('CookieSyncService')

class CookieSyncService {
	private cookieChangeUnsubscribe: (() => void) | null = null
	// 记录已处理过的登录成功事件，避免重复处理
	private processedLogins = new Set<string>()

	/**
	 * 初始化 cookie 同步服务
	 */
	async initialize() {
		log('初始化 cookie 同步服务')

		// 检测插件是否已经登录，登录就同步登录信息到 web 端的 cookie 中
		this.syncExtensionSessionToWeb()

		// 监听 cookie 变化
		this.setupCookieChangeListener()
		// 启动时检查网页 session
		this.checkWebSessionOnStartup()
		// 启用 supabase 的 authState 监听
		supabase.auth.onAuthStateChange(async (_event, session) => {
			log('onAuthStateChange Session', _event, session)
			if (_event === 'SIGNED_OUT' && session) {
				await authSessionState.removeValue()
				CookieSessionManager.clearSessionCookies()
				this.processedLogins.clear()
			}
			if (session) {
				await authSessionState.setValue(session)
				await getAuthService().syncSessionToWeb(session)
			}
		})
	}

	/**
	 * 检测插件是否已经登录，如果登录则同步到 web 端 cookie
	 * 这是一个异步方法，通过 await 确保在其他初始化代码执行前完成
	 */
	private syncExtensionSessionToWeb() {
		try {
			authSessionState.getValue().then((extensionSession) => {
				if (!extensionSession) {
					log('插件未登录，跳过同步到 web')
					return
				}

				log('插件已登录，同步 session 到 web 端 cookie', extensionSession)
				// 同步到 web 端 cookie
				CookieSessionManager.setSessionToCookies(extensionSession)
			})
		} catch (error) {
			log('同步插件 session 到 web 端失败：', error)
		}
	}

	/**
	 * 生成 session 的唯一标识，用于去重
	 */
	private generateSessionKey(session: Session): string {
		// 使用用户 ID + access_token 的前 20 位作为唯一标识，因为 cookie 频繁更新，所以这里做一下重复调用处理
		return `${session.user?.id || 'unknown'}_${session.access_token.substring(0, 20)}`
	}

	/**
	 * 检查是否需要执行完整的登录成功处理
	 */
	private shouldProcessLoginSuccess(session: Session, currentSession: Session | null): boolean {
		const sessionKey = this.generateSessionKey(session)

		// 如果已经处理过这个 session，跳过
		if (this.processedLogins.has(sessionKey)) {
			log('该 session 已处理过，跳过登录成功处理', sessionKey)
			return false
		}

		// 如果是同一个用户的 session 更新（如 token 刷新），跳过
		if (currentSession && currentSession.user?.id === session.user?.id) {
			const currentSessionKey = this.generateSessionKey(currentSession)
			if (this.processedLogins.has(currentSessionKey)) {
				log('同一用户的 session 更新，跳过登录成功处理')
				return false
			}
		}

		return true
	}

	/**
	 * 处理网页端登录成功，执行与 parseAuthCallbackURL 类似的逻辑
	 */
	private async handleWebLoginSuccess(session: Session, currentSession: Session | null = null) {
		try {
			// 检查是否需要执行完整的登录成功处理
			if (!this.shouldProcessLoginSuccess(session, currentSession)) {
				return
			}
			const sessionKey = this.generateSessionKey(session)
			log('处理网页端登录成功', session.user?.email, sessionKey)

			// 记录此 session 已处理
			this.processedLogins.add(sessionKey)

			// 清理旧的记录（保留最近的 20 个，避免内存泄漏）
			if (this.processedLogins.size > 20) {
				const entries = Array.from(this.processedLogins)
				this.processedLogins.clear()
				// 保留最后 10 个
				entries.slice(-10).forEach((key) => this.processedLogins.add(key))
			}

			// 记录登录事件
			$event('login', {
				userId: session.user?.id,
				email: session.user?.email
			})

			// 重新登录的时候，需要重新上报浏览器指纹
			hasReportBrowserIdState.setValue(false)

			// 处理首次登录逻辑
			const firstLoginService = getFirstLoginService()
			await firstLoginService.handleFirstLogin()

			const provider = session.user?.app_metadata?.provider
			const providerToken = session.provider_token
			const providerRefreshToken = session.provider_refresh_token
			const scopes = session.user?.app_metadata?.scopes ?? []

			if (provider && providerToken && providerRefreshToken) {
				post(CREDENTIALS, {
					provider: upperCase(provider),
					accessToken: decodeURIComponent(providerToken),
					refreshToken: decodeURIComponent(providerRefreshToken),
					scopes
				})
					.then((res) => res.data)
					.then(async (credential) => {
						if (session.user?.email && credential.id) {
							await getAuthService().bindEmail(session.user?.email, credential.id)
							$event('credentialsSuccess', {
								email: session.user?.email,
								credentialId: credential.id
							})
						} else {
							log('no email or credential id')
						}
					})
					.finally(() => {
						getAuthService().setTimeZone()
					})
			}

			log('网页端登录成功处理完成')
		} catch (error) {
			log('处理网页端登录成功时出错：', error)
		}
	}

	/**
	 * 设置 cookie 变化监听器，实现网页登录同步到插件
	 */
	setupCookieChangeListener() {
		console.log('setupCookieChangeListener')

		// 使用新的 CookieSessionManager 监听 cookie 变化
		this.cookieChangeUnsubscribe = CookieSessionManager.addCookieChangeListener(async (webSession) => {
			try {
				const currentSession = await authSessionState.getValue()
				if (!webSession) {
					log('检测到网页端退出登录')
					// 清除处理记录
					this.processedLogins.clear()
					await getAuthService().logout(true)
					return
				}
				if (!webSession?.access_token) {
					return
				}
				// 使用 CookieSessionManager 比较并获取最新的 session
				const latestSession = CookieSessionManager.getLatestSession(currentSession, webSession)

				if (
					latestSession === webSession &&
					(!currentSession || currentSession.access_token !== webSession.access_token)
				) {
					// 网页端的 session 更新，需要同步到插件
					log('检测到网页端登录/更新，同步到插件', webSession)

					// 保存到插件存储
					await authSessionState.setValue({
						...currentSession,
						...webSession
					})
					$event('cookie_sync_to_extension', {
						source: 'web_cookie_change'
					})

					// 处理网页端登录成功（带去重逻辑）
					await this.handleWebLoginSuccess(
						{
							...currentSession,
							...webSession
						},
						currentSession
					)
				} else if (latestSession === currentSession && webSession) {
					// 插件端的 session 更新，需要同步到网页
					log('插件端 session 更新，同步到网页', currentSession)
					await CookieSessionManager.setSessionToCookies(currentSession)
				}
			} catch (error) {
				log('处理 cookie 变化时出错：', error)
			}
		})
	}

	/**
	 * 插件启动时检查网页 session
	 */
	async checkWebSessionOnStartup() {
		try {
			const currentSession = await authSessionState.getValue()
			const webSession = await CookieSessionManager.getSessionFromCookies()

			log('启动时检查 session', {
				hasCurrentSession: !!currentSession,
				hasWebSession: !!webSession,
				isDev: isDev()
			})

			// 使用 CookieSessionManager 获取最新的 session
			const latestSession = CookieSessionManager.getLatestSession(currentSession, webSession)

			if (latestSession && latestSession !== currentSession) {
				// 需要更新插件的 session
				log('从网页 cookie 恢复/更新会话', latestSession)

				// 保存到插件存储
				await authSessionState.setValue(latestSession)
				$event('startup_session_sync', {
					source: 'web_cookie'
				})

				// 处理网页端登录成功（带去重逻辑）
				await this.handleWebLoginSuccess(latestSession, currentSession)
			} else if (
				latestSession === currentSession &&
				webSession &&
				currentSession &&
				currentSession.access_token !== webSession.access_token
			) {
				// 插件的 session 更新，同步到网页
				log('启动时同步插件 session 到网页')
				await CookieSessionManager.setSessionToCookies(currentSession)
			}
		} catch (error) {
			log('启动时检查 session 失败：', error)
		}
	}

	/**
	 * 清理资源
	 */
	destroy() {
		if (this.cookieChangeUnsubscribe) {
			this.cookieChangeUnsubscribe()
			this.cookieChangeUnsubscribe = null
		}
		// 清理处理记录
		this.processedLogins.clear()
	}
}

export const [registerCookieSyncService, getCookieSyncService] = defineProxyService(
	'CookieSyncService',
	() => new CookieSyncService()
)
