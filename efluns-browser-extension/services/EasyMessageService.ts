import { TaskQueue } from "@/utils/taskQueue";
import { defineProxyService } from "@webext-core/proxy-service";

class EasyMessageService {
    private taskQueue = new TaskQueue()
    constructor() {}

    addSendMessageTask(nickname:string,link:string,text:string){
        return this.taskQueue.add(async ()=>{
            await this.sendMessage(nickname,link,text)
        })
    }

    private async sendMessage(nickname:string,link:string,text:string){
        const tabId = await this.getTTMessageTabId(nickname,link)
        console.log("准备就绪，发送消息",nickname,link,text);
        
        if(!tabId) return
        await browser.tabs.sendMessage(tabId,{type:"sendMessage",nickname,text})
    }

    private async getTTMessageTabId(nickname:string,link:string){
        const tabs = await browser.tabs.query({url: "https://www.tiktok.com/messages*"})
        if(tabs.length > 0){
            const res = await browser.tabs.sendMessage(tabs[0].id!, {type: "readyToMessage",nickname,link })
            if(res){
                return tabs[0].id
            }else{
                // 如果消息页面存在但未准备好，则更新该页面的URL为link
                
                await browser.tabs.update(tabs[0].id!, { url: link });
                return this.waitForTabLoad(tabs[0].id!, '更新聊天页面超时');
            }
        }

        const tab = await browser.tabs.create({url:link,active:false})
        // 等待这个tab完全加载完
        return this.waitForTabLoad(tab.id!, '获取聊天信息超时');
    }
    
    private waitForTabLoad(tabId: number, errorMessage: string): Promise<number> {
        // 等待页面加载完成，设置60秒超时
        return new Promise<number>((resolve, reject) => {
            const listener = (listenTabId: number, changeInfo: any) => {
                if (listenTabId === tabId && changeInfo.status === 'complete') {
                    browser.tabs.onUpdated.removeListener(listener);
                    clearTimeout(timeoutId);
                    resolve(tabId);
                }
            };
            
            const timeoutId = setTimeout(() => {
                browser.tabs.onUpdated.removeListener(listener);
                reject(new Error(errorMessage));
            }, 120000);
            
            browser.tabs.onUpdated.addListener(listener);
        });
    }
    
}

export const [registerEasyMessageService, getEasyMessageService] = defineProxyService(
    'EasyMessageService',
    () => new EasyMessageService()
)