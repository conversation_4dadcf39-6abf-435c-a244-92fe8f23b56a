import { COOKIE_NAME, EASYKOL_HOST } from '@/constants'
import { defineProxyService } from '@webext-core/proxy-service'
import { getAuthService } from './AuthService'
import { type Session } from '@supabase/supabase-js'

const log = makeModuleLog('EasykolCookieService')
class EasykolCookieService {
	private allSiteMatches = ['<all_urls>']

	constructor() {
		// 初始化
	}

	/**
	 * 请求访问easykol.com的cookie权限
	 */
	async requestCookiePermission() {
		const res = await browser.permissions.request({ origins: this.allSiteMatches })

		if (!res) {
			throw new Error('您需要授予权限才能使用此功能')
		} else {
			const cookies = await this.getAllCookies()

			const bigbangCookie = cookies.find(
				(cookie) => cookie.name === COOKIE_NAME && cookie.value.includes('access_token')
			)
			const currentSession = await authSessionState.getValue()
			const session: Session = JSON.parse(bigbangCookie?.value || '{}')
			if (!session?.expires_at || !currentSession?.expires_at)  return res;
			/** 网页端 session 是最新的 */
			const isNewsWeb = session?.expires_at > currentSession?.expires_at
			if (bigbangCookie?.value) {
				if(isNewsWeb) {
					// token 过期时间必须比当下时间大
					const isMoreNow =  session?.expires_at > (new Date().getTime() / 1000)
					if (isMoreNow) {
						await authSessionState.setValue(session)
					}
					
				}
			} else {
				// 如果网站没有登录，但是插件登录了，应该同步插件的登录状态到网站
				if (currentSession && !isNewsWeb) {
					log('同步插件的登录状态到网站', currentSession)
					const isMoreNow = currentSession?.expires_at > (new Date().getTime() / 1000)
					// token 过期时间必须比当下时间大
					if (isMoreNow) {
						await getAuthService().syncSessionToWeb(currentSession)
					}
				}
			}
		}
		return res
	}

	/**
	 * 检查是否已有easykol.com的cookie权限
	 */
	async hasPermission() {
		return await browser.permissions.contains({ origins: ['https://easykol.com/*'] })
	}

	/**
	 * 获取easykol.com的所有cookie
	 */
	async getAllCookies() {
		const hasPermission = await this.hasPermission()
		if (!hasPermission) {
			await this.requestCookiePermission()
		}

		return await browser.cookies.getAll({ domain: 'easykol.com' })
	}

	/**
	 * 获取指定名称的cookie
	 */
	async getCookie(name: string) {
		const cookies = await this.getAllCookies()
		return cookies.find((cookie) => cookie.name === name)
	}

	
	
}



export const [registerEasykolCookieService, getEasykolCookieService] = defineProxyService(
	'EasykolCookieService',
	() => new EasykolCookieService()
)
