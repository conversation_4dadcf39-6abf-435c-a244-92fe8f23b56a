import { makeModuleLog } from '@/utils/log'
import { sendMessage } from '@/utils/message'
import { authSessionState } from '@/utils/storages'
import supabase from '@/utils/supabase'
import { defineProxyService } from '@webext-core/proxy-service'
import { browser } from 'wxt/browser'
import { getAuthService } from './AuthService'

const log = makeModuleLog('EmailAuthorizationService')

interface AuthorizationStatus {
	isAuthorized: boolean
	hasGmailScope: boolean
	provider: string | null
}

class EmailAuthorizationService {
	/**
	 * Check if the user has authorized email sending permissions
	 */
	async checkEmailAuthorization(): Promise<AuthorizationStatus> {
		try {
			const session = await authSessionState.getValue()
			if (!session || !session.user) {
				return {
					isAuthorized: false,
					hasGmailScope: false,
					provider: null
				}
			}
			console.log(session, 'session123')

			const provider = session.user.app_metadata?.provider

			return {
				isAuthorized: provider === 'google',
				hasGmailScope: provider === 'google' ? true : false,
				provider: provider || null
			}
		} catch (error) {
			log('Error checking email authorization:', error)
			return {
				isAuthorized: false,
				hasGmailScope: false,
				provider: null
			}
		}
	}

	/**
	 * Trigger incremental authorization flow for email sending
	 */
	async requestEmailAuthorization(): Promise<void> {
		try {
			const isPanelOpen = await this.checkSidePanelOpen()
			console.log(isPanelOpen, 'isPanelOpen')

			if (!isPanelOpen) {
				await supabase.auth.signOut()
				await getAuthService().logout(true)
				browser.tabs.create({ url: 'https://easykol.com/login?scope=gmail' })
			}

			const session = await authSessionState.getValue()
			if (!session || !session.user) {
				// No session, need full login
				log('No session found, requesting full login')
				sendMessage('showEmailAuthModal', undefined)
				return
			}

			const provider = session.user.app_metadata?.provider
			// Always show modal in sidebar for both Google and non-Google users
			log('Requesting email authorization via sidebar modal', { provider })
			sendMessage('showEmailAuthModal', undefined)
		} catch (error) {
			log('Error requesting email authorization:', error)
			sendMessage('showEmailAuthModal', undefined)
		}
	}

	/**
	 * Check if side panel is open
	 */
	private async checkSidePanelOpen(): Promise<boolean> {
		try {
			// Method 1: Use runtime.getContexts() if available (Chrome 116+)
			if (browser.runtime && typeof browser.runtime.getContexts === 'function') {
				try {
					// eslint-disable-next-line @typescript-eslint/ban-ts-comment
					// @ts-ignore
					const contexts = await browser.runtime.getContexts({
						contextTypes: ['SIDE_PANEL']
					})
					// If we have side panel contexts, the panel is open
					return contexts && contexts.length > 0
				} catch (error) {
					log('runtime.getContexts failed:', error)
				}
			}

			// Method 2: Try to detect if side panel is accessible
			// Check if we can access side panel APIs, which indicates it might be open
			try {
				// eslint-disable-next-line @typescript-eslint/ban-ts-comment
				// @ts-ignore
				if (browser.sidePanel && browser.sidePanel.getOptions) {
					// eslint-disable-next-line @typescript-eslint/ban-ts-comment
					// @ts-ignore
					const options = await browser.sidePanel.getOptions({})
					// If panel is enabled and we can get options, it suggests panel availability
					return options?.enabled === true
				}
			} catch (error) {
				log('sidePanel.getOptions failed:', error)
			}

			// Method 3: Fallback - assume not open if we can't detect
			return false
		} catch (error) {
			log('Error checking side panel status:', error)
			return false
		}
	}
}

export const [registerEmailAuthorizationService, getEmailAuthorizationService] = defineProxyService(
	'EmailAuthorizationService',
	() => new EmailAuthorizationService()
)
