import { EmailTemplate } from '@/@types'
import { EMAIL_TEMPLATES } from '@/constants'
import { get } from '@/utils/request'
import { defineProxyService } from '@webext-core/proxy-service'

const log = makeModuleLog('EmailTemplateService')
class EmailTemplateService {
	constructor() {
		this.refreshEmailTemplates()
	}

	async refreshEmailTemplates() {
		try {
			const data = await get(EMAIL_TEMPLATES).then((res) => res.data.data as EmailTemplate[])
			await emailTemplatesState.setValue(data)
			return data
		} catch (err) {
			log('refreshEmailTemplates', 'Error fetching email templates', err)
		}
	}

	async selectEmailTemplate(id: EmailTemplate['id']) {
		await currentEmailTemplateIdState.setValue(id)
	}
}

export const [registerEmailTemplateService, getEmailTemplateService] = defineProxyService(
	'EmailTemplateService',
	() => new EmailTemplateService()
)
