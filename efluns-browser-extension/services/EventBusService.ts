
type EventCallback = (...args: any[]) => void

class EventBusService {
  private static instance: EventBusService | null = null
  private events: Map<string, Set<EventCallback>> = new Map()

  // 先写个简单的单例模式
  static getInstance() {
    if (!EventBusService.instance) {
      EventBusService.instance = new EventBusService()
    }
    return EventBusService.instance
  }

  subscribe(event: string, callback: EventCallback) {
    if (!this.events.has(event)) {
      this.events.set(event, new Set())
    }
    
    const callbacks = this.events.get(event)!
    callbacks.add(callback)

    // 返回取消订阅函数
    return () => {
      callbacks.delete(callback)
      if (callbacks.size === 0) {
        this.events.delete(event)
      }
    }
  }

  publish(event: string, ...args: any[]) {
    const callbacks = this.events.get(event)
    if (!callbacks) return
    
    callbacks.forEach(callback => {
      try {
        callback(...args)
      } catch (error) {
        console.error(`Error in event ${event} callback:`, error)
      }
    })
  }
}

export const getEventBusService = () => EventBusService.getInstance()