import { makeModuleLog } from '@/utils/log'
import { hasCompletedFirstLoginState } from '@/utils/storages'
import { defineProxyService } from '@webext-core/proxy-service'
import { browser } from 'wxt/browser'

const log = makeModuleLog('FirstLoginService')

class FirstLoginService {
	private isProcessingFirstLogin = false
	private firstLoginPromise: Promise<void> | null = null

	/**
	 * Handle first login logic - ensures Login.html is opened only once
	 */
	async handleFirstLogin(): Promise<void> {
		// If already processing, return the existing promise
		if (this.isProcessingFirstLogin && this.firstLoginPromise) {
			log('First login already being processed, returning existing promise')
			return this.firstLoginPromise
		}

		// Create a new promise for first login handling
		this.firstLoginPromise = this.processFirstLogin()
		return this.firstLoginPromise
	}

	private async processFirstLogin(): Promise<void> {
		try {
			this.isProcessingFirstLogin = true
			
			// Check if first login has already been completed
			const hasCompletedFirstLogin = await hasCompletedFirstLoginState.getValue()
			if (hasCompletedFirstLogin) {
				log('First login already completed, skipping')
				return
			}

			// Double-check with a small delay to handle race conditions
			await new Promise(resolve => setTimeout(resolve, 100))
			
			// Check again in case another process set it
			const checkAgain = await hasCompletedFirstLoginState.getValue()
			if (checkAgain) {
				log('First login was just completed by another process')
				return
			}

			// Mark first login as completed BEFORE opening the tab
			await hasCompletedFirstLoginState.setValue(true)
			
			log('First login detected, opening extension login page')
			// Open the extension login page for first-time users
			const loginUrl = browser.runtime.getURL('/Login.html')
			await browser.tabs.create({ url: loginUrl })
			
		} catch (error) {
			log('Error handling first login:', error)
			// Reset the flag if something went wrong
			await hasCompletedFirstLoginState.setValue(false)
		} finally {
			this.isProcessingFirstLogin = false
			this.firstLoginPromise = null
		}
	}

	/**
	 * Reset first login state (useful for testing or debugging)
	 */
	async resetFirstLogin(): Promise<void> {
		await hasCompletedFirstLoginState.setValue(false)
		log('First login state reset')
	}
}

export const [registerFirstLoginService, getFirstLoginService] = defineProxyService(
	'FirstLoginService',
	() => new FirstLoginService()
)