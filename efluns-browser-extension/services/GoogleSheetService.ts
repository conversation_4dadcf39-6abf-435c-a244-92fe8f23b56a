import { GoogleSheetGetPostLinksResponse, GoogleSheetGetResponse, GoogleSheetIds } from '@/@types/googleSheet'
import {
	GOOGLE_GET_POSTS,
	GOOGLE_MONTHLY_REFRESH,
	GOOGLE_SHEET_GET,
	GOOGLE_SHEET_SCHEMA_DATA_RENDER,
	GOOGLE_SHEET_SYNC_PUBLICATION,
	GOOGLE_SHEET_UPDATE_READONLY,
	PUBLICATION_UPDATE_VIDEO_DATA
} from '@/constants'
import { get, post } from '@/utils/request'
import { googleSheetInfoState } from '@/utils/storages'
import { defineProxyService } from '@webext-core/proxy-service'
import { getAuthService } from './AuthService'

const log = makeModuleLog('GoogleSheetService')

class GoogleSheetService {
	private spreadsheetInfo: GoogleSheetGetResponse | undefined = undefined
	private googleSheetScriptId = 'GoogleSheetScript'
	private allSiteMatches = ['<all_urls>']
	constructor() {
		// this.initGoogleSheetInfo()
		// this.registerGoogleSheetScript()
	}

	private async requestPermission() {
		const res = await browser.permissions.request({ origins: this.allSiteMatches })
		if (!res) {
			throw new Error('You need to grant permission to use this feature')
		} else {
			// 如果网站没有登录，但是插件登录了，应该同步插件的登录状态到网站
			const currentSession = await authSessionState.getValue()
			if (currentSession) {
				log('同步插件的登录状态到网站', currentSession)
				await getAuthService().syncSessionToWeb(currentSession)
			}
		}
		await this.registerGoogleSheetScript()
	}

	private async registerGoogleSheetScript() {
		try {
			// 先移除已存在的 content script
			await browser.scripting
				.unregisterContentScripts({
					ids: [this.googleSheetScriptId]
				})
				.catch(() => {
					// 忽略首次执行时script不存在的错误
				})

			// 注册新的 content script
			await browser.scripting.registerContentScripts([
				{
					id: this.googleSheetScriptId,
					matches: this.allSiteMatches,
					js: ['content-scripts/GoogleSheetScript.js'],
					runAt: 'document_end'
				}
			])
		} catch (e) {
			console.error('注册 content script 失败:', e)
		}
	}

	private async initGoogleSheetInfo() {
		this.spreadsheetInfo = await googleSheetInfoState.getValue()
	}

	async getSpreadsheetInfo() {
		const res = await get<{ data: GoogleSheetGetResponse }>(GOOGLE_SHEET_GET).then((res) => res.data.data)
		if (res) {
			googleSheetInfoState.setValue(res)
			this.spreadsheetInfo = res
		}
		return res
	}

	async refreshSheetData(spreadsheetId: string) {
		return await get(GOOGLE_SHEET_SCHEMA_DATA_RENDER, {
			params: {
				spreadsheetId
			}
		})
	}

	async getPostLinks(type: 'all' | string) {
		return get<GoogleSheetGetPostLinksResponse>(GOOGLE_GET_POSTS, {
			params: {
				type
			}
		}).then((res) => res.data.data)
	}

	async refreshMonthlyReportSheetData() {
		if (!this.spreadsheetInfo) throw new Error('No spreadsheet info')
		return await post(
			GOOGLE_MONTHLY_REFRESH,
			{},
			{
				params: {
					spreadsheetId: this.spreadsheetInfo.spreadsheetId
				}
			}
		).then((res) => {
			console.log('refreshMonthlyReportSheetData')
			googleSheetMonthlyUpdateTimeState.setValue(res?.data?.data?.updateTime)
		})
	}

	private async updateOnlyReadOnly(spreadsheetId: string) {
		return await get(GOOGLE_SHEET_UPDATE_READONLY, {
			params: {
				spreadsheetId
			}
		})
	}
	async createTab() {
		await browser.tabs.create({ url: 'https://easykol.com/dataManagement/easykolTrack' })
	}
	// 打开sheet
	async openSheet() {
		await this.requestPermission()
		await this.createTab()
		// const spreadsheetInfo = await this.getSpreadsheetInfo()
		// this.updateOnlyReadOnly(spreadsheetInfo.spreadsheetId)
		// const url = `${spreadsheetInfo.sheetUrl}/*`
		// const tabs = await browser.tabs.query({url})

		// let tabId: number
		// if(tabs.length > 0){
		//     tabId = tabs[0].id!
		//     await browser.tabs.update(tabId, {active:true})
		// }else{
		//     const newTab = await browser.tabs.create({url:spreadsheetInfo.sheetUrl})
		//     tabId = newTab.id!
		// }

		// // 监听标签页关闭事件
		// browser.tabs.onRemoved.addListener((closedTabId) => {
		//     if(closedTabId === tabId) {
		//         // 在这里处理标签页关闭后的逻辑
		//         this.saveSheetData()
		//     }
		// })
	}

	// 打开sheet并请求Cookie权限（合并权限请求）
	async openSheetWithCookiePermission() {
		await this.requestPermission()
		await this.createTab()
	}

	async submitSheetData(parsedLinks: GoogleSheetIds) {
		if (!this.spreadsheetInfo) throw new Error('No spreadsheet info')
		await post(PUBLICATION_UPDATE_VIDEO_DATA, {
			...parsedLinks,
			spreadsheetId: this.spreadsheetInfo.spreadsheetId
		})
		await this.updateOnlyReadOnly(this.spreadsheetInfo.spreadsheetId)
	}

	async saveSheetData() {
		if (!this.spreadsheetInfo) throw new Error('No spreadsheet info')
		await post(
			GOOGLE_SHEET_SYNC_PUBLICATION,
			{},
			{
				params: {
					spreadsheetId: this.spreadsheetInfo.spreadsheetId
				}
			}
		)
	}
}

export const [registerGoogleSheetService, getGoogleSheetService] = defineProxyService(
	'GoogleSheetService',
	() => new GoogleSheetService()
)
