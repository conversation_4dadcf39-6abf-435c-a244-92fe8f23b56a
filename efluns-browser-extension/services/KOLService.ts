import { TaskStatus } from '@/@types'
import { AudienceAnalysis, FakeDetection, FakeDetectionResult } from '@/@types/card'
import { ContactInfo, EmailSource, KolRelation, SEARCH_KOL_RES, SearchKOLParams } from '@/@types/kol'
import { Platform } from '@/@types/platform'
import { Project } from '@/@types/project'
import { AiFilterParams } from '@/@types/similar'
import {
	AUDIENCE_ANALYSIS,
	AUDIENCE_ANALYSIS_EXPORT,
	CREATE_FAKE_DETECTION_TASK,
	EXPORT_FAKE_DETECTION_TASK,
	EXPORT_KOLS,
	GET_KOL_REIGON,
	GET_SIMILAR_TASK,
	INFO_CARD_ACCESS_STATUS,
	KOL,
	KOL_RELATION,
	KOL_RELATION2,
	PATCH_LINKS,
	QUERY_FAKE_DETECTION_TASK,
	QUERY_SINGLE_AUDIENCE_ANALYSIS,
	<PERSON><PERSON><PERSON>_<PERSON>OL_EMAIL,
	<PERSON>AR<PERSON>_KOL_PLATFORM,
	SEND_EMAIL,
	SINGLE_AUDIENCE_ANALYSIS,
	USER_VERTICAL,
	YTB_PAGE_TYPE
} from '@/constants'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'
import extractKOLHandler from '@/utils/extractKOLHandler'
import { get, patch, post } from '@/utils/request'
import { defineProxyService } from '@webext-core/proxy-service'
import { YTNodes } from 'youtubei.js'
import { getAuthService } from './AuthService'
import { getEmailAuthorizationService } from './EmailAuthorizationService'
import { getPlatformService } from './PlatformService'
import { isEnterpriseUserState } from '@/utils/storages'

const log = makeModuleLog('KOLService')

class KOLService {
	async sendEmail(kolId: string, email?: string) {
		// Check email authorization first
		const emailAuthService = getEmailAuthorizationService()
		const authStatus = await emailAuthService.checkEmailAuthorization()

		if (!authStatus.isAuthorized) {
			log('Email not authorized, requesting authorization', authStatus)
			await emailAuthService.requestEmailAuthorization()
			return Promise.reject(new Error('Email authorization required'))
		}

		const projectId = await currentProjectIdState.getValue()
		const templateId = await currentEmailTemplateIdState.getValue()
		const templates = await emailTemplatesState.getValue()
		const template = templates.find((item) => item.id === templateId)
		const authService = getAuthService()
		const user = await authService.getUser()
		const session = await getAuthService().getSession()
		const usingEmail = user?.email

		if (!kolId || !usingEmail || !templateId || !projectId) {
			log('Invalid Params', {
				emailSubject: template?.subject,
				emailContent: template?.content,
				kolId,
				usingEmail,
				templateId,
				projectId
			})
			return Promise.reject(new Error('Invalid Params'))
		}
		return post(SEND_EMAIL, {
			emailSubject: template?.subject,
			emailContent: template?.content,
			templateId,
			kolId,
			usingEmail,
			projectId,
			email
		}).catch((err) => {
			log('sendEmail', 'Error sending email', err)
			if (err instanceof Error) {
				// Handle different email credential errors
				if (err.message === 'sender not found' || err.message === 'Email credential not match') {
					log('Email credential error detected, clearing cache and requesting authorization')
					emailAuthService.requestEmailAuthorization()
				}
			}
			return Promise.reject(err)
		})
	}

	async getKOLHandler(url: string): Promise<string | null> {
		const platform = await getPlatformService().getPlatform(url)
		switch (platform) {
			case Platform.YOUTUBE:
				return await getKOLService().getYoutubeChannelId(url)
			case Platform.TIKTOK:
				return extractKOLHandler(url)
			case Platform.INS:
				return extractInsKOLHandler(url)
			default:
				return null
		}
	}

	async searchKOL(params: SearchKOLParams): Promise<SEARCH_KOL_RES> {
		return get(SEARCH_KOL_PLATFORM, {
			params: {
				...params,
				handler: params.handler?.replace('@', '')
			}
		})
			.then((res) => res.data?.data)
			.then((data: SEARCH_KOL_RES) => {
				return data
			})
	}

	async searchKOLEmail(params: SearchKOLParams): Promise<SEARCH_KOL_RES> {
		return get(SEARCH_KOL_EMAIL, {
			params: {
				...params,
				handler: params.handler?.replace('@', '')
			}
		})
			.then((res) => res.data?.data)
			.then((data: SEARCH_KOL_RES) => {
				const info = data.kolInfo
				log('searchKOLEmail', { info })
				return data
			})
	}

	// 获取 visonMode 三要素
	async getVisionModeInfo(params: SearchKOLParams): Promise<AiFilterParams> {
		return get(USER_VERTICAL, {
			params: {
				...params,
				handler: params.handler?.replace('@', ''),
				language: navigator.language
			}
		})
			.then((e) => e.data)
			.then((e) => e.data)
	}

	//上报 kol 的链接
	async patchKolLinks(kolId: string, links: string[]) {
		return patch<{ data: ContactInfo[] }>(PATCH_LINKS, {
			kolId,
			links
		})
			.then((e) => e.data)
			.then((e) => e.data)
	}

	async getKolRelation(kolId: string) {
		return get<{ data: KolRelation[] }>(KOL_RELATION, {
			params: {
				kolId
			}
		}).then((e) => e.data?.data)
	}

	async searchKolRelation(params: SearchKOLParams) {
		return get<{ data: KolRelation[] }>(KOL_RELATION2, {
			params
		}).then((e) => e.data?.data)
	}
	//用户链接爬虫
	// async postLinksContact(links:string[]){
	// 	const res = await post<{data:ContactInfo}>(POST_LINKS_CONTACT,{
	// 		links
	// 	}).then(e=>e.data)
	// 	return res.data[links[0]]
	// }

	// async getKOLInfo(id: string): Promise<KOLType> {
	// 	return get<KOLType>(KOL, {
	// 		params: {
	// 			id
	// 		}
	// 	})
	// 		.then((res) => res.data)
	// 		.then((info) => {
	// 			if (isEmpty(info.email)) {
	// 				return post(
	// 					FETCH_KOL_EMAIL,
	// 					{},
	// 					{
	// 						params: {
	// 							id
	// 						}
	// 					}
	// 				)
	// 					.then(() => sleep(10000))
	// 					.then(() => this.getKOLInfo(id))
	// 					.catch(() => info)
	// 			}
	// 			return info
	// 		})
	// }

	async updateKOLInfo(id: string, payload: { email: string; emailSource: EmailSource }) {
		return patch(KOL, payload, {
			params: {
				id
			}
		})
	}

	async exportKOLs(projectId: Project['id'],isStop?:boolean): Promise<boolean> {
		if (!projectId) {
			throw new Error('Project ID is required')
		}
		try {
			const csvURL = await post(EXPORT_KOLS, {
				projectId,
				isStop
			}).then((res) => res.data.url)
			await browser.downloads.download({ url: csvURL })
			return true
		} catch (error) {
			if (error instanceof Error) {
				throw new Error(error.message || 'Export KOLs failed')
			}
			throw new Error('Export KOLs failed')
		}
	}

	async getAudienceAnalysis(handler: string, platform: Platform): Promise<AudienceAnalysis> {
		const result = await this.queryAudienceResult(handler, platform)
		if (!result.needCreateTask) {
			return result
		}
		const { id: taskId } = await this.createAudienceTask(handler, platform)
		await this.pollTaskResult(taskId)
		const data = await this.queryAudienceResult(handler, platform)
		if (!data.regionAnalysisResult) {
			throw new Error('Audience analysis failed')
		}
		return data
	}

	private async queryAudienceResult(handler: string, platform: Platform): Promise<AudienceAnalysis> {
		return get(AUDIENCE_ANALYSIS, { params: { platform, source: handler } })
			.then((e) => e.data)
			.then((e) => e.data)
	}

	private async createAudienceTask(handler: string, platform: Platform) {
		const projectId = await currentProjectIdState.getValue()
		return post(AUDIENCE_ANALYSIS, {
			projectId,
			platform,
			source: handler
		})
			.then((e) => e.data)
			.then((e) => e.data)
	}

	private async querySingleAudienceResult(taskId: string): Promise<AudienceAnalysis> {
		return get(QUERY_SINGLE_AUDIENCE_ANALYSIS, { params: { taskId } })
			.then((e) => e.data)
			.then((e) => e.data)
	}

	// 根据单个视频的 audience
	private async createSingleAudienceTask(url: string) {
		return post(SINGLE_AUDIENCE_ANALYSIS, { url })
			.then((e) => e.data)
			.then((e) => e.data)
	}

	async getSingleAudienceAnalysis(url: string) {
		const { id: taskId } = await this.createSingleAudienceTask(url)
		await this.pollTaskResult(taskId)
		const data = await this.querySingleAudienceResult(taskId)
		if (!data.regionAnalysisResult) {
			throw new Error('Audience analysis failed')
		}
		return data
	}

	async getKOLRegion(params: SearchKOLParams) {
		return get(GET_KOL_REIGON, { params })
			.then((e) => e.data)
			.then((e) => e.data)
	}

	private async pollTaskResult(taskId: string) {
		// eslint-disable-next-line no-constant-condition
		while (true) {
			const task = await get<any>(GET_SIMILAR_TASK, {
				params: {
					id: taskId
				}
			}).then((res) => res.data)
			// source = task.params.source
			if (task.status === TaskStatus.FAILED) {
				throw new Error(task.message)
			}
			if (task.status === TaskStatus.COMPLETED) {
				break
			}
			await sleep(5000)
		}
	}

	async exportAudienceAnalysis(url: string) {
		const platform = getPlatform(url)
		if (platform !== Platform.INS) return
		const source = await this.getKOLHandler(url)
		const projectId = await currentProjectIdState.getValue()
		return get(AUDIENCE_ANALYSIS_EXPORT, {
			params: {
				projectId,
				platform,
				source
			}
		})
			.then((e) => e.data)
			.then((e) => {
				const url = e.data?.url
				if (!url) {
					throw new Error('Export audience analysis failed')
				}
				browser.downloads.download({ url })
			})
	}

	private youtubePageTypeCache = new Map<string, YTNodes.NavigationEndpoint>()
	async getYtbPageType(url: string) {
		const cached = this.youtubePageTypeCache.get(url)
		if (cached) {
			return cached
		}
		const res = await get<{ data: YTNodes.NavigationEndpoint }>(YTB_PAGE_TYPE, { params: { url } })
			.then((e) => e.data)
			.then((e) => e.data)
		if (this.youtubePageTypeCache.size > 10) {
			this.youtubePageTypeCache.clear()
		}
		this.youtubePageTypeCache.set(url, res)
		return res
	}

	async getIsYoutubeKOLPage(url: string) {
		const pageType = await this.getYtbPageType(url)
		return pageType.metadata.page_type === 'WEB_PAGE_TYPE_CHANNEL'
	}

	async getYoutubeChannelId(url: string) {
		const pageType = await this.getYtbPageType(url)
		return pageType.metadata.page_type === 'WEB_PAGE_TYPE_CHANNEL' ? pageType.payload?.browseId : null
	}

	async createFakeDetectionTask(handler: string) {
		const res = await post<{data:{task:FakeDetection,fromCache:boolean}}>(`${CREATE_FAKE_DETECTION_TASK}?handler=${handler}`,{},{
			headers:{
				contentType:'application/json'
			}
		})
		if(res?.data?.data?.task?.status === TaskStatus.COMPLETED && res?.data?.data?.task?.result){
			return {
				taskId:res.data.data.task.id,
				result:res.data.data.task.result
			}
		}
		const result = await this.pollNewTaskResult(res.data.data.task.id)
		return {
			taskId:res.data.data.task.id,
			result
		}
	}

	private async pollNewTaskResult(taskId:string){
		// eslint-disable-next-line no-constant-condition
		while (true) {
			const task = await get<{
				data:FakeDetection
			}>(QUERY_FAKE_DETECTION_TASK, {
				params: {
					taskId
				}
			}).then((res) => res.data.data)
			// source = task.params.source
			if (task.status === TaskStatus.FAILED) {
				throw new Error(task.errors)
			}
			if (task.status === TaskStatus.COMPLETED) {
				return task.result
			}
			await sleep(5000)
		}
	}

	async exportFakeDetectionTask(taskId:string){
		const {data} = await get<{
			data:{url:string}
		}>(EXPORT_FAKE_DETECTION_TASK, {
			params: {
				taskId
			}
		})
		if(data?.data?.url){
			browser.downloads.download({url:data.data.url})
		}
	}

	async getIsEnterpriseUser(){
		const cache = await isEnterpriseUserState.getValue()
		if(cache.expiredAt > Date.now()){
			return cache.value
		}else{
			const res = await get<{data:{allowAccess:boolean}}>(INFO_CARD_ACCESS_STATUS).then(e=>e.data).then(e=>e.data)
			await isEnterpriseUserState.setValue({
				value:res.allowAccess,
				expiredAt:Date.now()+1000*60*120 //2小时过期
			})
			return res.allowAccess
		}
	}
}

export const [registerKOLService, getKOLService] = defineProxyService('KOLService', () => new KOLService())
