import { GET_BLOGGER_UNIQUE_ID } from '@/constants/api'
import { makeModuleLog } from '@/utils/log'
import { get } from '@/utils/request'
import { defineProxyService } from '@webext-core/proxy-service'

const log = makeModuleLog('KolsIdService')

export interface KolsIdResponse {
	statusCode: number
	error: string
	message: string
	data: {
		kolId: string
		isNew: boolean
	}
}

export enum Platform {
	Douyin = 'DOUYIN',
	Instagram = 'INSTAGRAM',
	Tiktok = 'TIKTOK',
	Twitter = 'TWITTER',
	Xhs = 'XHS',
	Youtube = 'YOUTUBE'
}
class KolsIdService {
	/**
	 * 获取KOL的唯一ID
	 */
	async getKolsId(platform: Platform, platformAccount: string): Promise<KolsIdResponse['data']> {
		try {
			const response = await get<KolsIdResponse>(GET_BLOGGER_UNIQUE_ID, {
				params: { platform, platformAccount }
			})
			return response.data.data
		} catch (error) {
			log('getKolsId error:', error)
			throw error
		}
	}
}

const kolsIdService = new KolsIdService()

export const [registerKolsIdService, getKolsIdService] = defineProxyService('KolsIdService', () => kolsIdService)

export type KolsIdServiceType = typeof kolsIdService
