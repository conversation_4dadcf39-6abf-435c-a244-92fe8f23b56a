import { ModalProps } from "@/@types"
import { getEventBusService } from "./EventBusService"


const sendEvent = createSendEvent('modalShow')
class ModalService {
  private currentModal: ModalProps | null = null;
  private modalId = 0;

  show(options: ModalProps): string {
    // 如果当前显示的是 limit，则阻止其他类型显示
    if (this.currentModal?.type === 'limit' && options.type !== 'limit') {
      return '';
    }

    const id = `modal-${++this.modalId}`;
    this.currentModal = { ...options, id };
    
    const enhancedOptions = {
      ...options,
      id,
      onConfirm: () => {
        this.currentModal = null;
        options.onConfirm?.();
      },
      onCancel: () => {
        this.currentModal = null;
        options.onCancel?.();
      },
      onWarning: () => {
        this.currentModal = null;
        options.onWarning?.();
      }
    };

    getEventBusService().publish('showModal', enhancedOptions);

    sendEvent("show",options)
    return id;
  }

  close(modalId?: string) {
    if (!modalId || this.currentModal?.id === modalId) {
      this.currentModal = null;
      getEventBusService().publish('closeModal', modalId);
    }
  }
}

const modalService = new ModalService();
export const getModalService = () => modalService;