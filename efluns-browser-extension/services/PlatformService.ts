import { Platform } from '@/@types/platform'
import { defineProxyService } from '@webext-core/proxy-service'
import { getCommonService } from './CommonService'
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils'

const log = makeModuleLog('PlatformService')

class PlatformService {
	async getPlatform(url: string): Promise<Platform> {
		return getPlatform(url)
	}

	async getCurrentPlatform(): Promise<Platform> {
		const url = await getCommonService().getActiveTabURL()
		if (!url) {
			throw new Error('No active tab url found')
		}
		return this.getPlatform(url)
	}
}

export const [registerPlatformService, getPlatformService] = defineProxyService(
	'PlatformService',
	() => new PlatformService()
)
