import { defineProxyService } from '@webext-core/proxy-service'
import { v4 as uuidv4 } from 'uuid'

// const POSTHOG_API_KEY = 'phc_Jzpe2YLWnNKmk54hPimcluaQnKqnxO6gzw45K1p4CHG'
// const POSTHOG_HOST = 'https://us.i.posthog.com' 
// const log = makeModuleLog('PostHogService')

class PostHogService {
  // private readonly STORAGE_KEY = 'posthog_distinct_id'
  
  constructor() {
    
    this.initializeUserId()
  }

  private getFormattedOffset(){
    // const offset = new Date().getTimezoneOffset();
    // const utcOffset = -offset/60;
    // const formattedOffset = `UTC${utcOffset >= 0 ? '+' : ''}${utcOffset}`;
    // return formattedOffset
  }

  private async initializeUserId() {
    // const stored = await browser.storage.local.get(this.STORAGE_KEY)
    // let userId = stored[this.STORAGE_KEY]
    // const auth = await authSessionState.getValue()
    // if (!userId) {
    //   userId = uuidv4()
    //   await browser.storage.local.set({ [this.STORAGE_KEY]: userId })
    // }
    
    // this.identify(auth?.user?.id || "",{
    //   uuid:userId,
    //   ...auth?.user?.identities?.[0],
    //   version:browser.runtime.getManifest().version,
    //   timeZone:Intl.DateTimeFormat().resolvedOptions().timeZone,
    //   formatOffset:this.getFormattedOffset()
    // })
  }

  // 发送事件
  capture(eventName: string, properties?: Record<string, any>) {
    // log('Capturing event:', eventName, properties)
    // posthog.capture(eventName, properties)
  }

  // 设置用户属性
  identify(distinctId: string, properties?: Record<string, any>) {
    // posthog.identify(distinctId, properties)
  }

  // 重置用户信息
  reset() {
    // posthog.reset()
  }
}

export const [registerPostHogService, getPostHogService] = defineProxyService(
  'PostHogService',
  () => new PostHogService()
)