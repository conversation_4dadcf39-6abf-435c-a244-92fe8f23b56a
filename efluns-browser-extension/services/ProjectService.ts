import { Project } from '@/@types/project'
import { AiFilterParams } from '@/@types/similar'
import { PROJECT_DETAIL, PROJECT_RESET, PROJECT_TERMINATE_NEW, PROJECTS } from '@/constants'
import { $delete, get, patch, post } from '@/utils/request'
import { currentProjectIdState } from '@/utils/storages'
import { defineProxyService } from '@webext-core/proxy-service'
import toast from 'react-hot-toast'

const log = makeModuleLog('ProjectService')
class ProjectService {
	constructor() {
		this.refreshProjects()
	}

	async refreshProjects() {
		try {
			const data = await get(PROJECTS).then((res) => res.data.data as Project[])
			await projectsState.setValue(data)

			const id = await currentProjectIdState.getValue()
			if (id === null || !data.find((item) => item.id === id)) {
				await currentProjectIdState.setValue(data[0]?.id ?? null)
			}

			return data
		} catch (err) {
			toast.error(err instanceof Error ? err.message : String(err))
			log('refreshProjects', err)
			return []
		}
	}

	async selectProject(id: Project['id']) {
		await currentProjectIdState.setValue(id)
	}

	async terminateProject(taskType: 'SIMILAR' | 'KEYWORD' | 'LONG_CRAWLER' = 'SIMILAR') {
		const projectId = await currentProjectIdState.getValue()
		await post(PROJECT_TERMINATE_NEW, { projectId, taskType })
	}

	async deleteProject(projectId: Project['id']) {
		await $delete(PROJECT_DETAIL, { params: { projectId } })
	}

	async patchProject(projectId: Project['id'], data: any) {
		await patch(PROJECT_DETAIL, data, { params: { projectId } })
	}

	async resetProject(projectId: Project['id']) {
		await post(PROJECT_RESET, { projectId })
	}

	async getAiFilterParams() {
		const projectId = await currentProjectIdState.getValue()
		const project = (await projectsState.getValue()).find((item) => item.id === projectId)
		return project?.config
			? {
					allowList: project?.config.allowList || [],
					banList: project.config.banList || [],
					kolDescription: project.config.kolDescription || ''
				}
			: {
					allowList: [],
					banList: [],
					kolDescription: ''
				}
	}

	async setAiFilterParams(params: AiFilterParams) {
		const projectId = await currentProjectIdState.getValue()
		const projects = await projectsState.getValue()
		projectsState.setValue(projects.map((item) => (item.id === projectId ? { ...item, config: params } : item)))
		this.patchProject(projectId!, { ...params })
	}
}

export const [registerProjectService, getProjectService] = defineProxyService(
	'ProjectService',
	() => new ProjectService()
)
