import { ProfileDetail } from '@/@types/tiktok/ttshop'
import {
	createTTShopUrl,
	OFFSCREEN_URLS,
	TTSDomain,
	TTSErrorMessage,
	TTSOffscreenMessage,
	ttsRules
} from '@/entrypoints/offscreen/const'
import { ttsSearchHistoryDomainState } from '@/utils/storages'
import { defineProxyService } from '@webext-core/proxy-service'
import { getAuthService } from './AuthService'

const log = makeModuleLog('OffscreenService')

class TTShopService {
	private hasPermission: boolean = false
	private contentScriptId: string = 'wxt:content-scripts/TTShop.js'
	private currentDomain: keyof typeof TTSDomain | null = null

	private searchPromiseResolvers: Map<
		string,
		{
			resolve: (value: any) => void
			reject: (reason: any) => void
		}
	> = new Map()

	private initPromiseResolver: {
		resolve: (value: any) => void
		reject: (reason: any) => void
	} | null = null

	// 添加搜索队列
	private searchQueue: {
		query: string
		timeout: number
		resolve: (value: any) => void
		reject: (reason: any) => void
		domain: keyof typeof TTSDomain | null
	}[] = []

	getIsQuerying(query: string) {
		return this.searchPromiseResolvers.has(query)
	}

	// 是否正在处理搜索请求
	private isProcessingSearch = false

	constructor() {
		this.onmessage()
		this.initPermission()
		this.registerContentScript()
		// 监听tab变化
		browser.tabs.onRemoved.addListener(async () => {
			// 获取所有tiktok相关tab
			const tabs = await browser.tabs.query({
				url: 'https://www.tiktok.com/*'
			})

			// 如果没有tiktok相关tab了,关闭offscreen
			if (tabs.length === 0) {
				// @ts-expect-error 关闭offscreen文档
				if (browser.offscreen.hasDocument()) {
					// @ts-expect-error 关闭offscreen文档
					browser.offscreen.closeDocument()
				}
			}
		})
	}

	private async initPermission() {
		this.hasPermission = await this.checkPermission()
		browser.permissions.onAdded.addListener(async (permission: any) => {
			this.hasPermission = await this.checkPermission()
		})
	}

	private async registerContentScript() {
		if ((await this.checkPermission()) && !(await this.checkContentScript())) {
			browser.scripting.registerContentScripts([
				{
					id: this.contentScriptId,
					matches: OFFSCREEN_URLS,
					js: ['content-scripts/TiktokShop.js'],
					world: 'MAIN',
					allFrames: true,
					runAt: 'document_idle'
				}
			])
		}
	}

	private onmessage() {
		// 监听来自 offscreen 页面的消息
		browser.runtime.onMessage.addListener((message: any): any => {
			if (message.from === 'TIKTOK_SHOP') {
				switch (message.type) {
					case TTSOffscreenMessage.SCRAPED_DATA:
						log('状态', message.data.status)
						log(message.data.msg)

						if (message.data.status === 'failed') {
							if (message.data.msg === TTSErrorMessage.NEED_CAPTCHA) {
								this.showCaptcha(message.data.url)
							}
							// 处理所有查询的失败情况
							// 处理初始化失败
							if (this.initPromiseResolver) {
								this.initPromiseResolver.reject(new Error(message.data.msg || 'init failed'))
								this.initPromiseResolver = null
							}
							for (const [query, resolver] of this.searchPromiseResolvers.entries()) {
								log('删除查询并报错', query)
								resolver.reject(new Error(message.data.msg || 'init failed'))
								this.searchPromiseResolvers.delete(query)
							}
							// @ts-expect-error 关闭offscreen文档
							if (browser.offscreen.hasDocument()) {
								// @ts-expect-error 关闭offscreen文档
								browser.offscreen.closeDocument()
							}
						} else {
							// 初始化成功
							if (this.initPromiseResolver) {
								this.initPromiseResolver.resolve(true)
								this.initPromiseResolver = null
							}
						}
						break
					case TTSOffscreenMessage.CREATOR_SEARCH_COMPLETE:
						{
							log('搜索关键词:', message.data.query)
							log('搜索结果:', message.data.data)
							const response = message.data.data as ProfileDetail
							const resolver = this.searchPromiseResolvers.get(message.data.query)
							if (resolver) {
								if (response) {
									log('搜索结果命中')
									log(response)
									resolver.resolve(response)
								} else {
									resolver.reject(new Error(TTSErrorMessage.NO_DATA))
								}

								this.searchPromiseResolvers.delete(message.data.query)
							}
						}
						break
					case 'captchaHidden':
						browser.windows.remove(this.captchaWindowId!)
						this.captchaWindowId = null
						browser.tabs.query({ url: 'https://www.tiktok.com/*' }).then((tabs) => {
							tabs.forEach((tab) => {
								browser.tabs.sendMessage(tab.id!, {
									type: TTSOffscreenMessage.REFRESH_QUERY,
									from: 'EASYKOL',
									timestamp: Date.now()
								})
							})
						})
						break
				}
			}
		})

		onMessage('isQuerying', ({ data: query }) => {
			return this.getIsQuerying(query)
		})

		onMessage('complateInput', ({ data }) => {
			if (!data.cid) {
				const resolver = this.searchPromiseResolvers.get(data.query)
				if (resolver) {
					resolver.reject(new Error(TTSErrorMessage.NO_DATA))
					this.searchPromiseResolvers.delete(data.query)
				}
			}
		})
	}

	private async init(domain: keyof typeof TTSDomain) {
		// 如果已经有初始化进程在进行，直接返回现有的 Promise
		if (this.initPromiseResolver) {
			return new Promise((resolve, reject) => {
				this.initPromiseResolver = { resolve, reject }
			})
		}

		log('开始初始化')
		log('更新规则')
		await this.updateRules()

		log('开始注册内容脚本')
		await this.registerContentScript()

		if (!(await this.checkOffscreenReady(domain))) {
			log('开始初始化offscreen')
			await this.initOffScreen(domain)
		}
		log('初始化完成')
		// 等待 SCRAPED_DATA 响应
		return new Promise((resolve, reject) => {
			this.initPromiseResolver = { resolve, reject }
		})
	}

	private async initOffScreen(domain: keyof typeof TTSDomain) {
		try {
			// @ts-expect-error 关闭offscreen文档
			if (browser.offscreen.hasDocument()) {
				// @ts-expect-error 关闭offscreen文档
				await browser.offscreen.closeDocument()
			}
		} catch (error) {}
		// @ts-expect-error 创建offscreen文档
		await browser.offscreen.createDocument({
			url: browser.runtime.getURL(`/offscreen.html?domain=${domain}`),
			reasons: ['DOM_SCRAPING'],
			justification: 'Allows users to communicate directly with tts pages.'
		})
		this.currentDomain = domain
	}

	private async processSearchQueue() {
		if (this.isProcessingSearch || this.searchQueue.length === 0) {
			return
		}

		this.isProcessingSearch = true
		const { query, timeout, resolve, reject, domain } = this.searchQueue.shift()!

		try {
			const result = await this._searchCreator(query, timeout, domain)
			if (domain) {
				ttsSearchHistoryDomainState.setValue(domain)
			}
			resolve(result)
		} catch (error) {
			reject(error)
		} finally {
			this.isProcessingSearch = false
			this.processSearchQueue()
		}
	}

	// 重命名原来的searchCreator为_searchCreator作为内部方法
	private async _searchCreator(query: string, timeout: number, domain: keyof typeof TTSDomain | null) {
		log('开始运行' + query + '搜索任务')
		if (!query) {
			throw new Error('No query')
		}

		if (!this.hasPermission) {
			const userGranted = await this.requestPermission()
			if (!userGranted) {
				throw new Error('Please grant permission to use this feature')
			}
		}

		const userDomain = domain || (await ttsSearchHistoryDomainState.getValue())
		console.log('最终domain，准备启动offscreen,' + userDomain)

		if (!(await this.checkOffscreenReady(userDomain))) {
			log('offscreen未准备或者和当前domain不一致，准备初始化offscreen')
			if (!(await this.checkDomainIsLoginTTS(userDomain))) {
				throw new Error(TTSErrorMessage.NOT_LOGIN)
			}

			log('开始初始化offscreen')
			await this.init(userDomain)
		}

		log('开始搜索关键词:', query)

		const searchPromise = new Promise((resolve, reject) => {
			if (this.searchPromiseResolvers.has(query)) {
				this.searchPromiseResolvers.delete(query)
			}
			this.searchPromiseResolvers.set(query, { resolve, reject })
		})

		const timeoutPromise = new Promise((_, reject) => {
			setTimeout(() => {
				this.searchPromiseResolvers.delete(query)
				reject(new Error(`find creator timeout: ${timeout}ms`))
			}, timeout)
		})

		browser.runtime.sendMessage({
			type: TTSOffscreenMessage.CREATOR_SEARCH_START,
			from: 'EASYKOL',
			timestamp: Date.now(),
			query
		})

		return (await Promise.race([searchPromise, timeoutPromise])) as Promise<ProfileDetail>
	}

	// 新的公开searchCreator方法，使用队列机制
	async searchCreator(
		query: string | null,
		timeout: number = 30000,
		domain: keyof typeof TTSDomain | null
	): Promise<ProfileDetail> {
		log('创建tts搜索任务' + query, timeout, domain)
		if (!query) {
			throw new Error('No query')
		}

		return new Promise((resolve, reject) => {
			this.searchQueue.push({
				query,
				timeout,
				resolve,
				reject,
				domain
			})

			// 尝试处理队列
			this.processSearchQueue()
		})
	}

	private async checkOffscreenReady(domain: keyof typeof TTSDomain | null) {
		// @ts-expect-error offscreen
		return (await browser.offscreen.hasDocument()) && (!this.currentDomain || this.currentDomain === domain)
	}

	private async checkPermission() {
		const hasPermission = await browser.permissions.contains({
			origins: OFFSCREEN_URLS,
			permissions: ['history']
		})
		return hasPermission
	}

	private async checkContentScript() {
		return (await browser.scripting.getRegisteredContentScripts()).some((item) => item.id === this.contentScriptId)
	}

	private async requestPermission() {
		const hasPermission = await browser.permissions.request({
			origins: OFFSCREEN_URLS,
			permissions: ['history']
		})
		if (hasPermission) {
			// 如果网站没有登录，但是插件登录了，应该同步插件的登录状态到网站
			const currentSession = await authSessionState.getValue()
			if (currentSession) {
				log('同步插件的登录状态到网站', currentSession)
				await getAuthService().syncSessionToWeb(currentSession)
			}
		}
		return hasPermission
	}

	async cleanupRules() {
		const existingRules = await browser.declarativeNetRequest.getDynamicRules()
		const ruleIds = existingRules.filter(rule=>ttsRules.some(t=>t.id === rule.id)).map((rule) => rule.id)
		if (ruleIds.length > 0) {
			await browser.declarativeNetRequest.updateDynamicRules({
				removeRuleIds: ruleIds
			})
		}
	}

	private async updateRules() {
		try {
			// 先移除旧规则
			await this.cleanupRules()

			// 添加新规则
			await browser.declarativeNetRequest.updateDynamicRules({
				addRules: ttsRules
			})

			log('规则更新成功')
			// 更新规则后创建 offscreen
			log('开始等待tiktok shop页面加载....')
		} catch (error) {
			console.error('更新规则失败:', error)
			throw error
		}
	}

	async checkDomainIsLoginTTS(domain: keyof typeof TTSDomain) {
		const cookies = await browser.cookies.getAll({
			name: 'user_oec_info',
			domain
		})

		return cookies?.length > 0
	}

	private captchaWindowId: number | null = null
	private async showCaptcha(url: string) {
		if (!url.includes('detail')) {
			url = url.includes(TTSDomain['affiliate-us.tiktok.com'])
				? createTTShopUrl('affiliate-us.tiktok.com', '7494019494285903691')
				: createTTShopUrl('affiliate.tiktokglobalshop.com', '7495145736479738624')
		}
		const currentWindow = await browser.windows.get(this.captchaWindowId!).catch(() => {})
		if (currentWindow) {
			// 让已存在的验证码窗口聚焦
			await browser.windows.update(this.captchaWindowId!, {
				focused: true
			})
			return
		}
		// 打开一个新窗口显示验证码页面
		const width = 800
		const height = 600
		const left = 400
		const top = 200
		this.captchaWindowId = (
			await browser.windows.create({
				url: url,
				type: 'popup',
				width,
				height,
				left,
				top
			})
		).id!

		const tabs = await browser.tabs.query({ windowId: this.captchaWindowId })
		if (tabs.length > 0) {
			const tab = tabs[0]
			browser.scripting.executeScript({
				target: { tabId: tab.id! },
				world: 'ISOLATED',
				func: async () => {
					const observer = new MutationObserver(() => {
						const captcha = document.querySelector('#captcha_container')
						if (captcha) {
							const observerHidden = new MutationObserver((mutations) => {
								mutations.forEach((mutation) => {
									if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
										const displayStyle = window.getComputedStyle(captcha).display
										if (displayStyle === 'none') {
											// @ts-expect-error 在注入函数中引用函数无法被打包
											chrome.runtime.sendMessage({
												type: 'captchaHidden',
												from: 'TIKTOK_SHOP'
											})
										}
									}
								})
							})

							observerHidden.observe(captcha, {
								attributes: true,
								attributeFilter: ['style']
							})

							observer.disconnect()
						}
					})

					observer.observe(document.body, {
						childList: true,
						subtree: true
					})
				}
			})
		}
	}

	async getLastTTShopDomain(): Promise<keyof typeof TTSDomain> {
		if (!this.hasPermission) {
			const userGranted = await this.requestPermission()
			if (!userGranted) {
				throw new Error('Please grant permission to use this feature')
			}
		}
		const historys = await browser.history.search({
			text: 'Find creators',
			endTime: Date.now(),
			startTime: Date.now() - 14 * 24 * 60 * 60 * 1000,
			maxResults: 20
		})
		log('用户tiktok shop历史记录', historys)
		const url = historys?.[0]?.url || 'https://' + (await ttsSearchHistoryDomainState.getValue())
		return new URL(url).hostname as keyof typeof TTSDomain
	}

	// async confirmTTShopDomain(region?:string):Promise<keyof typeof TTSDomain>{
	//     if(!this.hasPermission){
	//         const userGranted = await this.requestPermission();
	//         if(!userGranted){
	//             throw new Error("Please grant permission to use this feature");
	//         }
	//     }
	//     const loginType = await this.getLastTTShopDomain()
	//     if(region !== "US"){
	//         return "affiliate.tiktokglobalshop.com"
	//     }else{
	//         return loginType === "affiliate" ? "affiliate-us.tiktok.com" : "partner.us.tiktokshop.com"
	//     }
	// }
}

export const [registerTTShopService, getTTShopService] = defineProxyService('TTShopService', () => new TTShopService())
