import {
	CREATE_TAG,
	DELETE_TAG,
	GET_NOTES_LIST,
	GET_TAGS_LIST,
	UPDATE_KOL_NOTES,
	UPDATE_KOL_TAGS,
	UPDATE_TAG
} from '@/constants/api'
import { makeModuleLog } from '@/utils/log'
import { $delete, get, post, put } from '@/utils/request'
import { defineProxyService } from '@webext-core/proxy-service'

const log = makeModuleLog('TagService')

export interface TagResponse {
	id: string
	name: string
	color: string
	createdAt: string
}

export interface TagsListResponse {
	statusCode: number
	error: string
	message: string
	data: TagResponse[]
}

class TagService {
	/**
	 * 获取所有标签列表
	 */
	async getTagsList(): Promise<TagResponse[]> {
		try {
			const response = await get<TagsListResponse>(GET_TAGS_LIST)
			return response.data.data || []
		} catch (error) {
			log('getTagsList error:', error)
			return []
		}
	}

	/**
	 * 创建新标签
	 */
	async createTag(name: string, color: string): Promise<TagResponse | null> {
		try {
			const response = await post<{ data: TagResponse }>(CREATE_TAG, { name, color })
			return response.data.data
		} catch (error) {
			log('createTag error:', error)
			throw error
		}
	}

	/**
	 * 更新标签
	 */
	async updateTag(id: string, name: string, color: string): Promise<TagResponse | null> {
		try {
			const response = await put<{ data: TagResponse }>(UPDATE_TAG, { id, name, color })
			return response.data.data
		} catch (error) {
			log('updateTag error:', error)
			throw error
		}
	}

	/**
	 * 删除标签
	 */
	async deleteTag(id: string): Promise<boolean> {
		try {
			await $delete(DELETE_TAG, {
				data: {
					id
				}
			})
			return true
		} catch (error) {
			log('deleteTag error:', error)
			return false
		}
	}

	/**
	 * 更新 KOL 的标签
	 */
	async updateKolTags(kolId: string, tagIds: string[]): Promise<boolean> {
		try {
			await post(UPDATE_KOL_TAGS, { kolId, tagIds })
			return true
		} catch (error) {
			log('updateKolTags error:', error)
			return false
		}
	}

	/**
	 * 更新 KOL 的笔记
	 */
	async updateKolNotes(kolId: string, note: string): Promise<boolean> {
		try {
			await post(UPDATE_KOL_NOTES, { kolId, note })
			return true
		} catch (error) {
			log('updateKolNotes error:', error)
			return false
		}
	}

	/**
	 * 获取 KOL 的笔记和标签
	 */
	async getKolNotesAndTags(kolId: string): Promise<{ note: string; tags: TagResponse[] } | null> {
		try {
			const response = await get<{ data: { note: string; tags: TagResponse[] } }>(`${GET_NOTES_LIST}`, {
				params: {
					kolId
				}
			})
			return response.data.data
		} catch (error) {
			log('getKolNotesAndTags error:', error)
			return null
		}
	}

	
}

const tagService = new TagService()

export const [registerTagService, getTagService] = defineProxyService('TagService', () => tagService)

export type TagServiceType = typeof tagService
