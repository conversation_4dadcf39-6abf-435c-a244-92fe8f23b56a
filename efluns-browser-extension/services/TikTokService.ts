import { CommentStatistics } from "@/@types/tiktok/comments";
import { TIKTOK_COMMENTS } from "@/constants";
import { get } from "@/utils/request";
import { defineProxyService } from "@webext-core/proxy-service";

class TikTokService {
    async getComments(uniqueId: string) {
        // console.log('getComments', uniqueId)
        return await get<{data:CommentStatistics}>(TIKTOK_COMMENTS, {
            params: {
                uniqueId
            }
        }).then(res => res.data?.data)
    }
}

export const [registerTikTokService, getTikTokService] = defineProxyService(
	'TikTokService',
	() => new TikTokService()
)
