import { defineProxyService } from "@webext-core/proxy-service";
import { getEasykolCookieService } from "./EasykolCookieService";
import { getCommonService } from "./CommonService";
import { TwitterProfilePage } from "@/@types/twitter/website";

class TwitterService {

    private twitterScriptId = 'TwitterScript'

    constructor(){
        this.registerTwitterScript()
    }

    async getTwitterProfilePage(){
        //获取cookie权限，并注册twitter脚本
        const activeId = await browser.tabs.query({active:true}).then(tabs=>tabs[0].id)
        const script = await browser.scripting.executeScript({
            target: {tabId: activeId!},
            func: () => {
                // @ts-expect-error 注入无法用ts
                return JSON.parse(document.querySelector('[data-testid="UserProfileSchema-test"]').innerText)

            }
        })
        const result = script[0].result as TwitterProfilePage
        return result
        // await this.registerTwitterScript()
    }

    private async registerTwitterScript(){
        await browser.scripting.registerContentScripts([{
            id: this.twitterScriptId,
            matches: ['https://x.com/*'],
            js: ['/content-scripts/AIFilterScript.js']
        }]).catch((err)=>{
            console.log("err",err)
        })
    }

    // async injectAIFilterScript(){
    //     const activeId = await browser.tabs.query({active:true}).then(tabs=>tabs[0].id)
    //     await browser.scripting.executeScript({
    //         target: {tabId: activeId!},
    //         files:['/content-scripts/AIFilterScript.js']
    //     })
    // }
}

export const [registerTwitterService, getTwitterService] = defineProxyService('TwitterService', () => new TwitterService())