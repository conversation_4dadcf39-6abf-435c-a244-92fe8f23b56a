import { GET_USER_INFO } from '@/constants/api'
import { makeModuleLog } from '@/utils/log'
import { get } from '@/utils/request'
import { defineProxyService } from '@webext-core/proxy-service'

const log = makeModuleLog('UserService')

export interface UserInfo {
	userId: string
	email: string
	createdAt: string
	membership: {
		id: string
		userId: string
		type: 'ENTERPRISE' | 'PERSONAL'
		timezone: string
		effectiveAt: string
		expireAt: string
		accountQuota: number
		usedQuota: number
		dailyUsage: number
		status: 'ACTIVE' | 'INACTIVE'
		lastResetAt: string
		createdAt: string
		updatedAt: string
		enterpriseId?: string
		isEnterpriseAdmin?: boolean
		enterpriseQuotaDailyLimit?: number
		enableEnterpriseQuotaDailyLimit?: boolean
		cardSubscriptionEffectiveAt?: string
		cardSubscriptionExpireAt?: string
		cardSubscriptionStatus?: string
		enterprise?: {
			id: string
			name: string
			contactPerson: string
			contactPhone: string
			contactEmail: string
			address: string
			industry: string
			scale: string
			accountQuota: number
			usedQuota: number
			dailyUsage: number
			dailyLimit: number
			memberUsageDailyLimit: number
			description: string
			status: 'ACTIVE' | 'INACTIVE'
			effectiveAt: string
			expireAt: string
			createdAt: string
			updatedAt: string
		}
	}
	stripeCustomer: any
	stripeSubscriptions: any[]
	isAdmin: boolean
}

export interface UserInfoResponse {
	statusCode: number
	error: string | null
	message: string
	data: UserInfo
}

class UserService {
	/**
	 * 获取用户信息
	 */
	async getUserInfo(): Promise<UserInfo | null> {
		try {
			const response = await get<UserInfoResponse>(GET_USER_INFO)
			return response.data.data
		} catch (error) {
			log('getUserInfo error:', error)
			return null
		}
	}

	/**
	 * 检查是否为企业用户
	 */
	isEnterpriseUser(userInfo: UserInfo | null): boolean {
		return !!userInfo?.membership?.enterprise?.id
	}

	isAdmin(userInfo: UserInfo | null): boolean {
		return !!userInfo?.membership?.isEnterpriseAdmin
	}

	/**
	 * 获取企业信息
	 */
	getEnterpriseInfo(userInfo: UserInfo | null) {
		return userInfo?.membership?.enterprise || null
	}
}

const userService = new UserService()

export const [registerUserService, getUserService] = defineProxyService('UserService', () => userService)

export type UserServiceType = typeof userService