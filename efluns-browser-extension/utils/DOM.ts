import { defaults } from 'lodash-es'

const log = makeModuleLog('Utils/DOM')
export function waitFor<T extends HTMLElement>(
	selector: string,
	options?: {
		timeout?: number
		interval?: number
		container?: Node
		suppressLog?: boolean
	}
) {
	const { container, timeout, interval, suppressLog } = defaults(options, {
		container: window.document,
		timeout: 30000,
		interval: 200,
		suppressLog: false
	})

	return new Promise<T>((resolve, reject) => {
		const checkExist = () => {
			const element = container.querySelector(selector) as T | null
			if (element) {
				!suppressLog && log(`Element with selector "${selector}" found`, element)
				resolve(element)
			} else {
				if (Date.now() - startTime > timeout) {
					reject(new Error(`Timeout: Element with selector "${selector}" not found within ${timeout}ms`))
				} else {
					setTimeout(checkExist, interval)
				}
			}
		}

		const startTime = Date.now()
		checkExist()
	})
}

export function markAllContainedEmailElement() {
	// 定义一个常见的邮箱地址正则表达式
	const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b/
	// 获取所有的元素节点
	const allElements = document.querySelectorAll<HTMLElement>('*:not(script)')
	// 存储包含邮箱地址的元素列表
	const elementsWithEmail = []
	// 使用 for...of 循环遍历所有元素
	for (const element of allElements) {
		const childNodes = Array.from(element.childNodes)

		// 检查是否存在直接的文本节点包含邮箱地址
		for (const node of childNodes) {
			const matches = node.textContent?.trim().match(emailRegex)
			const email = matches?.[0]
			if (node.nodeType === Node.TEXT_NODE && email) {
				// 将包含邮箱地址的元素添加到列表中
				elementsWithEmail.push(element)
				// 给元素添加自定义属性 efluns-email
				element.setAttribute('efluns-email-anchor', 'true')
				element.setAttribute('data-efluns-email', email)

				const { width, height } = element.getBoundingClientRect()
				const parentElement = element.parentElement
				// 将尺寸作为 CSS 变量注入到元素的内联样式中
				parentElement?.style.setProperty('--efluns-email-anchor-width', `${width}px`)
				parentElement?.style.setProperty('--efluns-email-anchor-height', `${height}px`)
				element.style.cursor = 'pointer'
				element.style.textDecoration = 'underline'
				element.style.textDecorationStyle = 'dashed'
			}
		}
	}

	return elementsWithEmail
}

export function waitForElement(selector:string):Promise<HTMLElement>{
	const element = document.querySelector(selector) as HTMLElement | null
	if(element){
		return Promise.resolve(element)
	}
	return new Promise((resolve)=>{
		const observer = new MutationObserver((mutations,observer)=>{
			const element = document.querySelector(selector) as HTMLElement | null
			if(element){
				observer.disconnect()
				resolve(element)
			}
		})
		observer.observe(document.documentElement,{
			childList:true,
			subtree:true
		})
	})
}

export function waitForElementToRunCallback({selector, callback}: {
	selector: string
	callback: (element: HTMLElement) => void
}) {
	const observer = new MutationObserver(() => {
		const element = document.querySelector(selector) as HTMLElement | null
		if (element) {
			callback(element)
				// 元素存在时，暂时停止观察
				observer.disconnect()
				// 开始观察元素是否被移除
				const removeObserver = new MutationObserver(() => {
					const currentElement = document.querySelector(selector)
					if (!currentElement) {
						// 元素被移除，重新开始观察 DOM
						removeObserver.disconnect()
						observer.observe(document.documentElement, {
							childList: true,
							subtree: true
						})
					}
				})
				// 观察整个文档，检测元素是否被移除
				removeObserver.observe(document.documentElement, {
					childList: true,
					subtree: true
				})
			
		}
	})

	observer.observe(document.documentElement, {
		childList: true,
		subtree: true
	})
}

/**
 * 计算嵌套元素的 border-radius
 * @param {number} outerRadius - 外部元素的 border-radius
 * @param {number} level - 嵌套层级，例如直接子元素为 1, 孙元素为 2，以此类推
 * @param {number} spacing - 两个嵌套元素之间的间距
 * @returns {number} - 内部元素的 border-radius
 */
export function calculateNestedBorderRadius(outerRadius: number, spacing: number, level: number) {
	// 计算内部元素的 border-radius
	const innerRadius = outerRadius - level * spacing
	// 确保 border-radius 不为负
	return Math.max(innerRadius, 0)
}

/**
 * 计算容器元素的 border-radius
 * @param {number} innerRadius - 内部元素的 border-radius
 * @param {number} padding - 内部元素与容器之间的 padding
 * @returns {number} - 容器元素的 border-radius
 */
export function calculateContainerBorderRadius(innerRadius: number, padding: number) {
	// 计算容器元素的 border-radius
	const outerRadius = innerRadius + padding
	return outerRadius
}

export function getClosestRootElement(element: Element): Element | null {
	let currentElement: Element | null = element

	while (currentElement) {
		if (currentElement.tagName === 'HTML') {
			return currentElement
		}
		currentElement = currentElement.parentElement
	}

	return currentElement
}


export function findParentElement(element:Element,selector:string){
	let currentElement:Element | null = element
	while(currentElement){
		if(currentElement.matches(selector)){
			return currentElement
		}
		currentElement = currentElement.parentElement
	}
	return null
}