import { getPostHogService } from "@/services/PostHogService"

const analysisPost = async (event: string,event_category?:string, eventProps = {}) => {
    await getPostHogService().capture(event, {
      event_category,
      ...eventProps,
      version:browser.runtime.getManifest().version
    })
  }

export const createAnalysisPost = (eventName: string) => {
    return async (event_category?:string, eventProps = {}) => null
}

export const createSendEvent = (eventName: string) => {
  return async (event_category?:string, eventProps = {}) => analysisPost(eventName,event_category,eventProps)
}