/**
 * 根据时区判断是否在中国
 * @returns {boolean} 如果在中国时区返回true，否则返回false
 */
export function isInChina(): boolean {
  try {
    // 获取当前时区偏移（以分钟为单位）
    const timezoneOffset = new Date().getTimezoneOffset();
    
    // 中国标准时间为UTC+8，即偏移-480分钟
    // 由于getTimezoneOffset返回的是本地时间与UTC的差值（以分钟计），东八区返回-480
    return timezoneOffset === -480;
  } catch (error) {
    console.error('判断时区出错:', error);
    return false;
  }
}
