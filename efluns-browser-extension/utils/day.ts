import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

export function initDayJS() {
}


export function formatTimeAgo(timestamp: number) {
	const date = dayjs(timestamp * 1000)
	return date.fromNow().replace('ago', '')
}

export function formatToTime(timestamp: number) {
	const date = dayjs(timestamp * 1000)
	return date.format('HH:mm')
}

export function parseTimeAgo(timeAgoString: string): number {
	const now = dayjs()
	const units: Record<string, number> = {
	  'second': 1,
	  'minute': 60,
	  'hour': 3600,
	  'day': 86400,
	  'week': 604800,
	  'month': 2592000,
	  'year': 31536000
	}
  
	const match = timeAgoString.match(/^(\d+)\s+(.*?)\s+ago$/)
	if (!match) return 0
  
	const [, amount, unit] = match
	const baseUnit = unit.replace(/s$/, '') // 处理复数形式，比如 months -> month
	const seconds = units[baseUnit] * parseInt(amount)
	
	return Math.floor(now.subtract(seconds, 'second').valueOf() / 1000)
  }

//   console.log(parseTimeAgo("3 months ago"));
  
  