import { isInsKOLPage } from './ins'
import { isTwitterKolPage } from './twitter/isTwitterKolPage'

export default function extractKOLHandler(url: string) {
	const match = url.match(/(?:@|\/user\/)([^\s/?]+)/)
	if (match?.[1]) {
		const username = decodeURIComponent(match[1])
		return `@${username}`
	} else {
		return null
	}
}

/**
 *
 *
 * @param url
 * @example input: https://www.instagram.com/explore/ output: null
 * @example input: https://www.instagram.com/reels/ output: null
 * @example input: https://www.instagram.com/direct/inbox/ output: null
 * @example input: https://www.instagram.com/p/ output: null
 * @example input: https://www.instagram.com/gittolakpop/ output: gittolakpop
 */
export function extractInsKOLHandler(url: string) {
	const isKOLPage = isInsKOLPage(url)

	if (isKOLPage) {
		const pathname = new URL(url).pathname
		const [kolHandler = null] = pathname.split('/').filter((item) => item !== '')
		return kolHandler
	} else {
		return null
	}
}

export function extractTwitterKOLHandler(url: string) {
	const isKOLPage = isTwitterKolPage(url)

	if (isKOLPage) {
		const pathname = new URL(url).pathname
		const [kolHandler = null] = pathname.split('/').filter((item) => item !== '')
		return kolHandler
	} else {
		return null
	}
}
