// utils/animation/flyingAvatar.ts

interface FlyingAvatarOptions {
    duration?: number;
    scale?: number;
    size?: number;
    easing?: string;
}

export function createFlyingAvatar(
    imageUrl: string,
    targetElement: HTMLElement,
    sourceElement: HTMLElement,
    options: FlyingAvatarOptions = {}
): Promise<void> {
    const {
        duration = 800,
        scale = 0.3,
        size = 50,
        easing = 'cubic-bezier(0.34, 1.56, 0.64, 1)'
    } = options;

    return new Promise((resolve) => {
        const flyingAvatar = document.createElement('img');
        flyingAvatar.src = imageUrl;

            const sourceRect = sourceElement.getBoundingClientRect();
            const startX = sourceRect.left + sourceRect.width / 2;
            const startY = sourceRect.top + sourceRect.height / 2;

            flyingAvatar.style.cssText = `
                position: fixed;
                width: ${size}px;
                height: ${size}px;
                border-radius: 50%;
                pointer-events: none;
                z-index: 99999;
                opacity: 1;
                left: ${startX - size/2}px;
                top: ${startY - size/2}px;
                transform: scale(1);
                transition: all ${duration}ms ${easing};
            `;

            document.body.appendChild(flyingAvatar);

            const targetRect = targetElement.getBoundingClientRect();
            const targetX = targetRect.left + size/2;
            const targetY = targetRect.top + size/2;

            flyingAvatar.offsetHeight;

            requestAnimationFrame(() => {
                flyingAvatar.style.transform = `translate(${targetX - startX}px, ${targetY - startY}px) scale(${scale})`;
                flyingAvatar.style.opacity = '0';
            });

            flyingAvatar.addEventListener('transitionend', () => {
                flyingAvatar.remove();
                resolve();
            }, { once: true });
        

    });
}