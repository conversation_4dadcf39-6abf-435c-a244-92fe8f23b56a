import {load} from '@fingerprintjs/fingerprintjs'
import { browserIdState } from './storages'

/**
 * 生成浏览器指纹
 * @returns Promise<string> 返回访客唯一标识符
 */
export async function generateFingerprint(): Promise<string> {
  try {
    // 初始化 FingerprintJS
    const fp = await load()
    
    // 获取访客标识信息
    const result = await fp.get()
    await browserIdState.setValue(result.visitorId)
    
    // 返回访客唯一标识符
    return result.visitorId
    
  } catch (error) {
    console.error( error)
    return ''
  }
}