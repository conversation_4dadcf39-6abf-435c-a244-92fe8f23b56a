import { getCountryCodeFromName } from "./tiktok/getCountryIcon"

export function getCountryPriority(regionCode:string | null | undefined):"T1" | "T2" | "T3" {
    if(!regionCode) return "T2"
    regionCode = getCountryCodeFromName(regionCode) || regionCode
    
    // T1 国家：北美和西欧发达国家
    const t1Countries = ['us', 'ca', 'gb', 'fr', 'de', 'it', 'es', 'nl', 'be', 'se', 'ch', 'at', 'ie', 'pt', 'dk', 'no', 'fi', 'lu', 'is', 'gr', 'cz', 'sk', 'ee', 'lv', 'lt', 'mt', 'hu', 'pl', 'hr', 'si', 'jp', 'kr', 'sg', 'il', 'au', 'nz', 'tw', 'hk', 'mo']

    
    // T2 国家：其他发达国家和发展中国家
    const t2Countries = ['ae', 'sa', 'qa', 'kw', 'br', 'ar', 'cl', 'co', 'pe', 'uy', 'mx', 'ru', 'tr', 'za', 'my', 'th', 'ph', 'id', 'vn', 'pk', 'bd', 'eg', 'ng', 'ke', 'gh', 'dz', 'ma', 'tn', 'cm', 'et', 'ua', 'by', 'kz', 'cn']
    
    const code = regionCode.toLowerCase()
    
    if(t1Countries.includes(code)) {
        return "T1"
    }
    
    if(t2Countries.includes(code)) {
        return "T2"
    }
    
    return "T3"
}