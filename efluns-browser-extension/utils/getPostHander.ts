// Platform.youtube:
// https://www.youtube.com/watch?v=085FWOYAV28
// https://www.youtube.com/shorts/vvDOEIKmdYw

import { getCommonService } from "@/services/CommonService";


// Platform.ins:
// https://www.instagram.com/p/CyGP_7HsGZY/?img_index=1
// https://www.instagram.com/reel/C7byUCvof_9/
enum PostPlatform {
  YOUTUBE_WATCH = 'youtube_watch',
  YOUTUBE_SHORTS = 'youtube_shorts',
  INS_POST = 'ins_post',
  INS_REEL = 'ins_reel',
}

export function checkIsPostPlatform(url?:string):PostPlatform | null{
  if (!url) return null;
  
  // YouTube 视频链接匹配
  if (/^https:\/\/www\.youtube\.com\/watch\?v=[\w-]+/.test(url)) {
    return PostPlatform.YOUTUBE_WATCH;
  }
  
  // YouTube Shorts 链接匹配
  if (/^https:\/\/www\.youtube\.com\/shorts\/[\w-]+/.test(url)) {
    return PostPlatform.YOUTUBE_SHORTS;
  }
  
  // Instagram 帖子链接匹配
  if (/^https:\/\/www\.instagram\.com\/p\/[\w-]+/.test(url)) {
    return PostPlatform.INS_POST;
  }
  
  // Instagram Reels 链接匹配
  if (/^https:\/\/www\.instagram\.com\/reel\/[\w-]+/.test(url)) {
    return PostPlatform.INS_REEL;
  }
  
  return null;
}
//针对帖子搜索，获取handler
export async function getCurrentPostHandler():Promise<string>{
  
  // 使用 scripting API 注入脚本到当前页面
  const activeTab = await getCommonService().getActiveTab()
  
  if (!activeTab?.id) {
    throw new Error("无法获取当前标签页");
  }

  const result = await getCommonService().sendMessageToCurrentContent({type: "getPostHandler"},true) as string
  console.log("页面中url",result)
  // 注入脚本并执行 getPostHandler 函数
  // const results = await browser.scripting.executeScript({
  //   target: { tabId: activeTab.id },
  //   func: getPostHandler
  // });
  // // 获取执行结果
  // console.log("results",results);
  
  // const result = results[0]?.result as string;

  
  
  
  return guard(activeTab.url?.includes("instagram")?extractInsKOLHandler(result):extractKOLHandler(result),"Unsupported post platform");
}





 export function getPostHandler(): string | null{
    const url = location.href
    const YOUTUBE_WATCH_LINK_SELECTOR = "ytd-video-owner-renderer a"
    const YOUTUBE_SHORTS_LINK_SELECTOR = "ytd-reel-video-renderer[is-active] yt-reel-channel-bar-view-model a"
    const INS_POST_LINK_SELECTOR = '[role="presentation"] a'
    const INS_REEL_LINK_SELECTOR = '[role="presentation"] a'

      // YouTube 视频链接匹配
  if (/^https:\/\/www\.youtube\.com\/watch\?v=[\w-]+/.test(url)) {
    return (document.querySelector(YOUTUBE_WATCH_LINK_SELECTOR) as HTMLAnchorElement)?.href
  }
  
  // YouTube Shorts 链接匹配
  if (/^https:\/\/www\.youtube\.com\/shorts\/[\w-]+/.test(url)) {
    return (document.querySelector(YOUTUBE_SHORTS_LINK_SELECTOR) as HTMLAnchorElement)?.href
  }
  
  // Instagram 帖子链接匹配
  if (/^https:\/\/www\.instagram\.com\/p\/[\w-]+/.test(url)) {
    return (document.querySelector(INS_POST_LINK_SELECTOR) as HTMLAnchorElement)?.href
  }
  
  // Instagram Reels 链接匹配
  if (/^https:\/\/www\.instagram\.com\/reel\/[\w-]+/.test(url)) {
    return (document.querySelector(INS_REEL_LINK_SELECTOR) as HTMLAnchorElement)?.href
  }

  return null
}

export function getPostHandlerOrId(url:string):string{
    const result = getPostHandler()
    return guard(url?.includes("instagram")?extractInsKOLHandler(result!):extractKOLHandler(result!),"Unsupported post platform");
}

