import { getKOLService } from '@/services/KOLService'
import { isInsKOLPage } from './ins'

export enum SupportPage {
	YOUTUBE_KOL = 'YOUTUBE_KOL',
	TIKTOK_TAG = 'TIKTOK_TAG',
	TIKTOK_SEARCH = 'TIK<PERSON><PERSON>_SEARCH',
	TIKTOK_KOL = 'TIKTOK_KOL',
	INS_KOL = 'INS_KOL',
}

export const YoutubeAllowList = ['youtube.com/@', 'youtube.com/channel/UC']

export function isYoutubeKOLPage(url: string): boolean {
	return YoutubeAllowList.some(allow => url.includes(allow))
}

export default async function getSupportPage(url: string): Promise<SupportPage | null> {
	if (!url) return null
	if (url.startsWith('https://www.youtube.com/')) {
		if(isYoutubeKOLPage(url)){
			return SupportPage.YOUTUBE_KOL
		}else if(await getKOLService().getIsYoutubeKOLPage(url)){
			return SupportPage.YOUTUBE_KOL
		}
	}

	if (url.startsWith('https://www.tiktok.com/tag')) {
		return SupportPage.TIKTOK_TAG
	}

	if (url.startsWith('https://www.tiktok.com/search')) {
		return SupportPage.TIKTOK_SEARCH
	}

	if (url.startsWith('https://www.tiktok.com/@')) {
		// Extract the path after the domain
		const urlObj = new URL(url)
		const pathSegments = urlObj.pathname.split('/').filter(segment => segment !== '')
		
		// Should only have one segment: @username
		if (pathSegments.length === 1 && pathSegments[0].startsWith('@')) {
			return SupportPage.TIKTOK_KOL
		}
		return null
	}

	if (isInsKOLPage(url)) {
		return SupportPage.INS_KOL
	}


	return null
}
