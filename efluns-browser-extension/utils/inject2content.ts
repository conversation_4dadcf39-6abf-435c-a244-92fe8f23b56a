// 定义事件类型
type EventName = "INS_AREA" | "FROM_TIKTOK_EARLY_SCRIPT" | "DATA_MONITOR_ERROR";
type EventData = any;

// 发送事件的辅助函数
export function sendEvent(name: EventName, data: EventData) {
  window.dispatchEvent(new CustomEvent(name, { detail: data }));
}

// 监听事件的辅助函数
export function listenEvent(name: EventName, callback: (data: EventData) => void) {
  const eventListener = ((event: CustomEvent) => {
    callback(event.detail);
  }) as EventListener;

  window.addEventListener(name, eventListener);
  return () => {
    window.removeEventListener(name, eventListener);
  };
}
