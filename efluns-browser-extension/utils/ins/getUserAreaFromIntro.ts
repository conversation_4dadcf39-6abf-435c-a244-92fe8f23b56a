const OPEN_INTRO_BTN_SELECTOR = "main section:nth-child(2)>div>div>div>div>a"
const CLOSE_INTRO_SELECTOR = '[role="dialog"]:has([data-bloks-name="bk.components.screen.Wrapper"]) button'
const cssRuleStr = 'div:has([data-bloks-name="bk.components.screen.Wrapper"]) { display: none !important; }'
// 隐藏简介 css  
function hideIntroCss() {
    document.body.setAttribute("hideIntro", "")
}

//恢复简介 css
function resetIntroCss() {
    document.body.removeAttribute("hideIntro")
}



async function openIntro(){
    const btn = await waitFor(OPEN_INTRO_BTN_SELECTOR)
    // console.log("找到按钮，打开");
    
    if(btn){
        // hideIntroCss()
        btn.click()
    }
}

async function closeIntro(){
    const btn = await waitFor(CLOSE_INTRO_SELECTOR)
    console.log("找到按钮，关闭");
    if(btn){
        btn.click()
        // resetIntroCss()
    }
}

const cache = new Map<string, string>()

export function getUserAreaFromIntroCache(key: string) {
    return cache.get(key)
}

export function setUserAreaFromIntroCache(key: string, value: string) {
   if(cache.size > 10){
    cache.delete(cache.keys().next().value!)
   }
   cache.set(key, value)
}

//无感知激活简介卡
export async function getUserAreaFromIntro() {
    // console.log("开始执行");
    
    hideIntroCss()
    try {
        await openIntro()
        await closeIntro()
        await new Promise(resolve => setTimeout(resolve, 200))
        window.scrollTo(0, 0);


    } catch (error) {
        
    }finally{
        resetIntroCss()
    }

    // console.log("执行完毕");
    
    // resetIntroCss()
}