export function isInsKOLPage(link: string) {
	const url = new URL(link)
	if (!['www.instagram.com', 'instagram.com'].includes(url.hostname)) {
		return false
	}

	const pathname = url.pathname
	const nonKOLPrefixPaths = ['/explore/', '/reels/', '/direct/', '/p/', '/stories/','/accounts/']
	const isNonKOLPath = pathname === '/' || nonKOLPrefixPaths.some((prefix) => pathname.startsWith(prefix)) || pathname.includes('/reel/')

	return !isNonKOLPath
}
