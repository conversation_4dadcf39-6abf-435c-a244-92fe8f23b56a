import { IS_DEV } from '@/constants/env'
import { createConsola } from 'consola/browser'

export function initConsole() {
	const cons = createConsola().withTag('Efluns')
	return cons
}

export function makeModuleLog(moduleName: string) {
	const instance = createConsola().withTag(`Efluns - ${moduleName}`)
	return (...args: Parameters<typeof console.log>) => {
		if (!IS_DEV) {
			return
		}
		instance.info(...args)
	}
}
