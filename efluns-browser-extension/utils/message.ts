import { defineExtensionMessaging } from '@webext-core/messaging'

interface ProtocolMap {
	openSidePanel: () => void
	navigateTo: (path: string) => void
	toogleEmailTemplateModal: (isShow: boolean) => void

	collectTikTokKOLs: (isLoadMore?: boolean) => Promise<string[]>

	getTikTokKOLOriginalFrom: (url:string) => string

	showEmailAuthModal: () => void

	isQuerying: (query:string) => boolean
	complateInput: (data:{query:string,cid:boolean}) => void

	submitAIFilter: (props:{
		cancel?:boolean
		ytbIds:string[]
	}) => void

	getYoutubeVideoIds: () => string[]
}

export const { sendMessage, onMessage } = defineExtensionMessaging<ProtocolMap>()
