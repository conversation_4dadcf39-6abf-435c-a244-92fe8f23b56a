import { orderBy } from "lodash-es";

/**
 * 格式化数字,添加千分位分隔符
 * @param num 需要格式化的数字
 * @returns 格式化后的字符串
 */
export function formatNumber(num: number | string): string {
    if (typeof num === 'string') {
        num = parseFloat(num);
    }
    return num.toLocaleString('en-US');
}

/**
 * 格式化大数字为带单位的字符串
 * @param num 需要格式化的数字
 * @returns 格式化后的字符串,如 1.2K, 1.5M 等
 */
export function formatLargeNumber(num: number | string): string {
    if (typeof num === 'string') {
        num = parseFloat(num);
    }
    
    if (num >= 1000000000) {
        return (num / 1000000000).toFixed(1) + 'B';
    }
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}


export function calculateApproximateUnitValue(total: string, units: number): string {
    console.log("total",total)
    console.log("units",units)
    // 检查是否是区间格式
    if (total.includes('-')) {
        const [minStr, maxStr] = total.split('-');
        const minValue = calculateApproximateUnitValue(minStr, units);
        const maxValue = calculateApproximateUnitValue(maxStr, units);
        return `${minValue}-${maxValue}`;
    }

    // 解析单个数值
    const match = total.match(/([A-Z]{2,3})?[\s]*([\d.]+)(K|M|B)?\+?/);
    if (!match) return "N/A";
    
    const [_, currency = "", numStr, magnitude = ""] = match;
    let baseNum = parseFloat(numStr);
    
    // 根据单位转换为基础数值
    switch(magnitude) {
        case 'K': baseNum *= 1000; break;
        case 'M': baseNum *= 1000000; break;
        case 'B': baseNum *= 1000000000; break;
    }
    
    // 计算每单价值
    const perUnit = baseNum / units;
    
    // 格式化结果
    let result = "";
    if (currency) result += currency;
    
    // 四舍五入到整数
    result += Math.round(perUnit);
    
    // 如果原始值带+号，结果也加上+
    if (total.includes('+')) result += '+';
    
    return result;
}


//根据数字数组计算中位数
export function getMedian(arr: number[]): number {
	if (arr.length === 0) return 0;
	const sorted = orderBy(arr);
	const mid = Math.floor(sorted.length / 2);
	return sorted.length % 2 === 0 
	  ? (sorted[mid - 1] + sorted[mid]) / 2 
	  : sorted[mid];
  }

export function formatNumberWithCommas(num: number): string {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}