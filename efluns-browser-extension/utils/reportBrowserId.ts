import { v4 as uuidv4 } from 'uuid'
import { browserIdState, hasReportBrowserIdState } from './storages'
import { CREDENTIALS_SIGNATURE } from '@/constants'
import { post } from './request'

 const getUid = async () => {
    const { uid } = await browser.storage.local.get("posthog_distinct_id")
    if(!uid){
        const uid = uuidv4()
        await browser.storage.local.set({ "posthog_distinct_id": uid })
        return uid
    }
    return uid
}

// 1.插件不变，切换账号：每次登录的时候上报可以察觉到
// 2.插件卸载重装，浏览器指纹不变，每个插件上报一次的时候可以察觉到
export const reportUserId = async () => {
    const uid = await getUid()
    const browserId = await browserIdState.getValue()
    const hasReport = await hasReportBrowserIdState.getValue()
    const authSession = await authSessionState.getValue()

    if(!browserId || !uid || hasReport || !authSession){
        return
    }

    post(CREDENTIALS_SIGNATURE,{
        browserId,
        extensionId:uid
    }).then(()=>{
        console.log("成功上报浏览器指纹");
        
        hasReportBrowserIdState.setValue(true)
    })
}
