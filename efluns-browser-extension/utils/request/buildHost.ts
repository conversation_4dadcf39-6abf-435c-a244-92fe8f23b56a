export enum DevEnv {
	BETA = 'BETA',
	PROD = 'PROD'
}

const log = makeModuleLog('buildHost')
export function toEnvHost(host: string, env: string) {
	return host.replace(/[\w|d|-]\./, (raw) => `${raw.slice(0, -1)}-${env}.`)
}

export default function buildHost(host: string) {
	const mode = import.meta.env.MODE
	const isMock = mode === 'mock'
	const isStaging = mode === 'staging'
	const isDev = mode === 'development'

	if (isMock && import.meta.env.VITE_API_SERVER) {
		return import.meta.env.VITE_API_SERVER
	}

	if (typeof window !== 'undefined') {
		const { hostname } = window.location

		if (hostname.includes('-beta.')) {
			return toEnvHost(host, 'beta')
		}

		if (hostname.includes('-test.')) {
			return toEnvHost(host, 'test')
		}
	}

	if (isDev || isStaging) {
		const env = 'beta'

		return toEnvHost(host, env)
	}

	return host
}
