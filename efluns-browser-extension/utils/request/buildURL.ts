import type { AxiosRequestConfig } from 'axios'
import { getBaseURL } from './utils'

export default function buildURL(config: AxiosRequestConfig): AxiosRequestConfig {
	const templatedPlaceholders = config.url?.match(/:\w+/g)

	templatedPlaceholders?.forEach((placeholder: string) => {
		if (!config.params) {
			return
		}

		const key = placeholder.replace(':', '')
		const param = config.params[key]

		config.url = config.url?.replace(placeholder, param)
		delete config.params[key]
	})

	config.baseURL = getBaseURL()

	return config
}
