import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios'
import axios from 'axios'
import { merge } from 'lodash-es'
import { useCallback, useState } from 'react'
import { IS_DEV } from '../../constants/env'
import withInterceptors from './interceptor'

const axiosInstance = axios.create({
	withCredentials: true
})

withInterceptors(axiosInstance)

export default axiosInstance

export const { get, post, patch, put, delete: $delete } = axiosInstance

const DEFAULT_OPTIONS: Partial<AxiosRequestConfig> = {
	method: 'GET'
}

export type UseRequestReturn<T = any> = [(options?: AxiosRequestConfig) => Promise<AxiosResponse<T>>, boolean]

export function useRequest<T>(url: string, options?: AxiosRequestConfig): UseRequestReturn<T> {
	const [loading, setLoading] = useState(false)
	const request: UseRequestReturn[0] = useCallback(
		(nextOptions?: AxiosRequestConfig) => {
			nextOptions = merge({}, DEFAULT_OPTIONS, options, nextOptions)

			setLoading(true)
			return axiosInstance(url, nextOptions)
				.catch((err: AxiosError) => {
					if (IS_DEV) {
						// eslint-disable-next-line no-console
						console.warn(`[request]: ${JSON.stringify(err)}`)
					}
					return Promise.reject(err.response)
				})
				.finally(() => {
					setLoading(false)
					return Promise.resolve({} as any)
				})
		},
		[options, url]
	)

	return [request, loading]
}

export function useGet<T>(url: string, params?: AxiosRequestConfig['params'], options?: AxiosRequestConfig) {
	return useRequest<T>(url, {
		...options,
		params
	})
}

export function useHead<T>(url: string, options?: AxiosRequestConfig) {
	return useRequest<T>(url, {
		...options,
		method: 'HEAD'
	})
}

export function useDelete<T>(url: string, options?: AxiosRequestConfig) {
	return useRequest<T>(url, {
		...options,
		method: 'DELETE'
	})
}

export function useOptions<T>(url: string, options?: AxiosRequestConfig) {
	return useRequest<T>(url, {
		...options,
		method: 'OPTIONS'
	})
}

export function usePost<T>(url: string, data?: AxiosRequestConfig['data'], options?: AxiosRequestConfig) {
	return useRequest<T>(url, {
		...options,
		method: 'POST',
		data
	})
}

export function usePut<T>(url: string, data?: AxiosRequestConfig['data'], options?: AxiosRequestConfig) {
	return useRequest<T>(url, {
		...options,
		method: 'PUT',
		data
	})
}

export function usePatch<T>(url: string, data?: AxiosRequestConfig['data'], options?: AxiosRequestConfig) {
	return useRequest<T>(url, {
		...options,
		method: 'PATCH',
		data
	})
}
