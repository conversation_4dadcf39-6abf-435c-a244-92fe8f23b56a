import { AI_SEARCH_CREATE, CREATE_SIMILARS_TASK, RATE_SIMILAR } from '@/constants'
import { LIMIT } from '@/constants/err'
import { getAuthService } from '@/services/AuthService'
import { CookieSessionManager } from '@/services/CookieSessionManager'
import { getModalService } from '@/services/ModalService'
import type { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import buildURL from './buildURL'

const log = makeModuleLog('AxiosInterceptor')
export default function withInterceptors(axiosInstance: AxiosInstance) {
	axiosInstance.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
		// Do something before request is sent

		config.withCredentials = true

		buildURL(config)
		const env = (await browser.storage.local.get({ env: '' })).env
		//TODO 记得删除
		if (env === 'localhost') {
			config.baseURL = `http://localhost:3000/api`
		} else {
			config.baseURL = `https://${env ? 'www-' + env + '.' : ''}easykol.com/api`
		}
		browser.storage.onChanged.addListener((changes) => {
			if (changes.env) {
				if (changes.env.newValue === 'localhost') {
					config.baseURL = `http://localhost:3000/api`
				} else {
					config.baseURL = `https://${changes.env.newValue ? 'www-' + changes.env.newValue + '.' : ''}easykol.com/api`
				}
				console.log('host 已切换：', config.baseURL)
			}
		})
		//TODO 记得删除

		const accessToken = (await authSessionState.getValue())?.access_token
		if (accessToken) {
			config.headers.Authorization = `Bearer ${accessToken}`
		}
		config.headers.easykolVersion = browser.runtime.getManifest().version

		return config
	})

	// Add a response interceptor
	axiosInstance.interceptors.response.use(
		(response: AxiosResponse) => {
			// Any status code that lie within the range of 2xx cause this function to trigger
			// Do something with response data

			const { status, config, request, data } = response as AxiosResponse
			const { method, url } = config ?? {}

			if (data?.toast) {
				//@todo toast info
			}

			return response
		},
		(
			error: AxiosError<{
				toast?: string
				error?: string
				errCode?: string
				statusCode?: number
				message?: string
			}>
		) => {
			// Any status codes that falls outside the range of 2xx cause this function to trigger
			// Do something with response error

			const { config, response } = error
			const { data, status, headers, request } = response ?? {}

			log({
				status,
				data,
				isTokenExpired: data?.errCode === 'TOKEN_EXPIRED',
				request,
				config
			})
			if (status === 401) {
				if (data?.errCode === 'TOKEN_EXPIRED') {
					const authService = getAuthService()
					authService.getSession().then((session) => {
						if (session) {
							console.log('进来了吗？')

							// authService
							// 	.refreshSession()
							// 	.then((session) => {
							// 		const accessToken = session?.access_token
							// 		if (accessToken) {
							// 			set(request.config, 'headers.Authorization', `Bearer ${accessToken}`)
							// 			return axiosInstance(config as AxiosRequestConfig)
							// 		}
							// 	})
							// 	.catch((err) => {
							// 		if (err.message.includes('Invalid Refresh Token: Already Used')) {
							// 			// logout
							// 			authService.logout()
							// 		}
							// 		throw err
							// 	})
						} else {
							CookieSessionManager.getSessionFromCookies().then((session) => {
								if (session) {
									CookieSessionManager.clearSessionCookies()
								}
							})
						}
					})
					return
				}

				if (data?.statusCode === 1411) {
					setTimeout(() => {
						supabase.auth.signOut()
						getAuthService().logout()
					}, 1000)
				}
			}

			if (status === 400) {
				if (data?.statusCode && data?.statusCode >= 1900 && data?.statusCode < 1949) {
					const sidePanelAPIs = [RATE_SIMILAR, CREATE_SIMILARS_TASK, AI_SEARCH_CREATE]
					const url = request.responseURL || request.url
					log('url', url)
					if (sidePanelAPIs.some((path) => url.includes(path))) {
						getModalService().show({
							type: 'limit',
							title: '',
							content: data?.message ?? LIMIT,
							statusCode: data?.statusCode
						})
						//TODO sidePanel 显示弹窗
					}
					const Err = new Error(data?.message ?? LIMIT)
					// @ts-expect-error 添加一个 code
					Err.statusCode = data?.statusCode
					throw Err
				}
			}

			if (data?.error) {
			}
			if (data?.toast) {
			}

			return Promise.reject(error?.response?.data?.message ? new Error(error?.response?.data?.message) : error)
		}
	)
}
