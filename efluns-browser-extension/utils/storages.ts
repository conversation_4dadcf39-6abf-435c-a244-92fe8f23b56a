import { EmailTemplate, Notifies } from '@/@types'
import { CardPositionState, DisplaySettings, PricingConfig, VideoCardSettings } from '@/@types/card'
import { Greeting } from '@/@types/easyDm'
import { GoogleSheetGetResponse } from '@/@types/googleSheet'
import { Platform } from '@/@types/platform'
import { Project } from '@/@types/project'
import { DefaultDisplaySettings } from '@/constants/infocard'
import { TTSDomain } from '@/entrypoints/offscreen/const'
import type { Session } from '@supabase/supabase-js'
import { storage } from 'wxt/storage'

export const authSessionState = storage.defineItem<Session | null>('local:authSession', {
	defaultValue: null
})

export const isUserConsentedState = storage.defineItem<boolean>('local:isUserConsented', {
	defaultValue: false
})

export const hasCompletedFirstLoginState = storage.defineItem<boolean>('local:hasCompletedFirstLogin', {
	defaultValue: false
})

export const projectsState = storage.defineItem<Project[]>('local:projects', {
	defaultValue: []
})

export const currentProjectIdState = storage.defineItem<Project['id'] | null>('local:currentProjectId', {
	defaultValue: null
})

export const emailTemplatesState = storage.defineItem<EmailTemplate[]>('local:emailTemplates', {
	defaultValue: []
})

export const currentEmailTemplateIdState = storage.defineItem<EmailTemplate['id'] | null>(
	'local:currentEmailTemplateId',
	{
		defaultValue: null
	}
)

// ------------------ Basic Service
export const activeTabURLState = storage.defineItem<string | null>('local:activeTabURLState', {
	defaultValue: null
})

export const envState = storage.defineItem<string>('local:env', {
	defaultValue: ''
})

export const cardPositionState = storage.defineItem<Omit<CardPositionState, Platform.TWITTER>>('local:cardPosition', {
	defaultValue: {
		[Platform.YOUTUBE]: { x: 0, y: 0 },
		[Platform.TIKTOK]: { x: 0, y: 0 },
		[Platform.INS]: { x: 0, y: 0 }
	}
})

export const notifiesState = storage.defineItem<Notifies>('local:notifies', {
	defaultValue: []
})

export const showCommonFieldsState = storage.defineItem<boolean>('local:showCommonFields', {
	defaultValue: true
})

// 记忆上次选择的地区
export const memorySelectRegionState = storage.defineItem<string[]>('local:memorySelectRegion', {
	defaultValue: []
})

export const hasWarnMaxInputState = storage.defineItem<boolean>('local:hasWarnMaxInput', {
	defaultValue: false
})

// 浏览器指纹
export const browserIdState = storage.defineItem<string>('local:browserId', {
	defaultValue: ''
})

// 是否已经上报过browserId
export const hasReportBrowserIdState = storage.defineItem<boolean>('local:hasReportBrowserId', {
	defaultValue: false
})

export const extensionEnvState = storage.defineItem<string>('local:env', {
	defaultValue: ''
})

export const ttsSearchOpen = storage.defineItem<boolean>('local:ttsSearchOpen', {
	defaultValue: false
})

export const ttsSearchHistoryDomainState = storage.defineItem<keyof typeof TTSDomain>('local:ttsSearchHistoryDomain', {
	defaultValue: 'affiliate.tiktokglobalshop.com'
})

export const googleSheetInfoState = storage.defineItem<GoogleSheetGetResponse>('local:googleSheetInfo', {
	defaultValue: undefined
})

export const autoInfoCardState = storage.defineItem<boolean>('local:autoInfoCard', {
	defaultValue: true
})

export const googleSheetMonthlyUpdateTimeState = storage.defineItem<string>('local:googleSheetMonthlyUpdateTime', {
	defaultValue: ''
})

export const isWorkingState = storage.defineItem<boolean>('local:isWorkingState', {
	defaultValue: false
})

// 更新跳转链接
export const updateJumpURLState = storage.defineItem<string>('local:updateJumpURL', {
	defaultValue: ''
})

export const speedModeState = storage.defineItem<Record<Exclude<Platform, Platform.TWITTER>, number>>('local:speedMode', {
	defaultValue: {
		[Platform.YOUTUBE]: 1,
		[Platform.TIKTOK]: 1,
		[Platform.INS]: 2
	}
})

export const easyDMGreetingsState = storage.defineItem<Greeting[]>('local:easyDMGreetings', {
	defaultValue: [
		{
			id: '1',
			title: '【Ask Email】',
			content: '[Paid collaboration🤑] May I have your email so I can send you the brief?',
			isVisible: true,
			isDefault: false
		}
	]
})

export const lastNotifiesTimeState = storage.defineItem<number>('local:lastNotifiesTime', {
	defaultValue: 0
})

export const isVersionState = storage.defineItem<Record<Platform, boolean>>('local:isVersion', {
	defaultValue: {
		[Platform.YOUTUBE]: false,
		[Platform.TIKTOK]: false,
		[Platform.INS]: true,
		[Platform.TWITTER]: true
	}
})

// Notes storage
export interface NotesTag {
	id: string
	name: string
	emoji: string
	color?: string
	isActive: boolean
	updatedBy?: {
		email: string
	}
}

export interface NotesItem {
	id: string
	url: string
	content: string
	tags: NotesTag[]
	createdAt: string
	updatedAt: string
}

export const notesState = storage.defineItem<NotesItem[]>('local:notes', {
	defaultValue: []
})

// KOL缓存数据
export interface KOLCacheItem {
	[key: string]: {
		url: string
		kolData: any // SEARCH_KOL_RES type from KOL service
		cachedAt: string
	}
}

export const kolCacheState = storage.defineItem<KOLCacheItem | null>('local:kolCache', {
	defaultValue: null
})

// 用户信息缓存
export interface UserInfoCache {
	userInfo: any // UserInfo type from UserService
	cachedAt: string
}


// Notes card minimized state
export const notesCardMinimizedState = storage.defineItem<boolean>('local:notesCardMinimized', {
	defaultValue: false
})

// Tags cache
export interface TagsCacheData {
	tags: NotesTag[]
	cachedAt: string
}

export const tagsCacheState = storage.defineItem<TagsCacheData | null>('local:tagsCache', {
	defaultValue: null
})


// ------------------ Info Card Settings ------------------
export const displaySettingsState = storage.defineItem<DisplaySettings>('local:displaySettings', {
  defaultValue: DefaultDisplaySettings
})

// ------------------ Video Card Settings ------------------
export const videoCardSettingsState = storage.defineItem<VideoCardSettings>('local:videoCardSettings', {
  defaultValue: {
    er: true,
    plays: true,
    comments: true,
    likes: true,
    favorites: true,
    publishTime: true,
    viralMultiplier: true,
  }
})

// ------------------ Pricing Config ------------------
export const pricingConfigState = storage.defineItem<PricingConfig>('local:pricingConfig', {
  defaultValue: {
    [Platform.YOUTUBE]: {
      T1: 30,
      T2: 10,
      T3: 5,
    },
    [Platform.TIKTOK]: {
      T1: 10,
      T2: 5,
      T3: 2,
    },
    [Platform.INS]: {
      T1: 10,
      T2: 5,
      T3: 2,
    }
  }
})

export const isEnterpriseUserState = storage.defineItem<{value:boolean,expiredAt:number}>('local:isEnterpriseUser', {
	defaultValue: {
		value: false,
		expiredAt: 0
	}
})