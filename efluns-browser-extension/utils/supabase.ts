import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY
const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
        storage: {
            getItem: async (key) => {
                // 从插件 storage 获取
                const session = await authSessionState.getValue()
                return session ? JSON.stringify(session) : null
            },
            setItem: async (key, value) => {                
                // 存储到插件 storage
                if(key.includes(new URL(supabaseUrl).host.split(".")[0])){
                    await authSessionState.setValue(JSON.parse(value))
                }
            },
            removeItem: async (key) => {
                // 从插件 storage 删除
                if(key.includes(new URL(supabaseUrl).host.split(".")[0])){
                    await authSessionState.removeValue()
                }
            }
        }
    }
})

export default supabase
