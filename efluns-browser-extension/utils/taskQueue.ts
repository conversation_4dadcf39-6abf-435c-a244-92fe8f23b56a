type Task<T = any> = () => Promise<T>

export class TaskQueue {
  private queue: Task[] = []
  private isProcessing = false

  /**
   * 添加任务到队列并返回Promise
   * @param task 要执行的任务函数
   * @returns Promise，任务执行完成后resolve
   */
  add<T>(task: Task<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      // 将任务包装成新的Promise，以便能够捕获错误并传递结果
      const wrappedTask = async () => {
        try {
          const result = await task()
          resolve(result)
          return result
        } catch (error) {
          reject(error)
          throw error
        }
      }

      this.queue.push(wrappedTask)
      this.processQueue()
    })
  }

  /**
   * 处理队列中的任务
   */
  private async processQueue() {
    if (this.isProcessing || this.queue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      while (this.queue.length > 0) {
        const task = this.queue.shift()
        if (task) {
          await task()
        }
      }
    } finally {
      this.isProcessing = false
    }
  }
}

