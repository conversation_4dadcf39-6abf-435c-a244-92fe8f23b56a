import { Link } from "@/@types";
import { SocialMediaIds } from "@/@types/kol";

// 首先定义一个新的接口来表示转换后的格式

  
  // 添加一个转换方法
  export function convertSocialMediaToLinks(socialMedia?: Partial<SocialMediaIds>): Link[] {
    const iconMap = {
      twitter: "https://encrypted-tbn1.gstatic.com/favicon-tbn?q=tbn:ANd9GcQYbMNQ3a7LqKzqhkvBsowNn_ZiyMTETw8naXxDTLsn12SAxcLwxr9aVzQcAb7yfXeaFg-H0NjXyKBK7kTt_tVF4RfZ3OtAiFZvpqW0lZB4iQ",
      instagram: "https://encrypted-tbn0.gstatic.com/favicon-tbn?q=tbn:ANd9GcSMItu-kpj9mUqJlYFLBMsNp279lOFn_WdIAoqsMZYtJYhjg8xQh9qCgB3BvVFbtYpLemKWr-ZbwnTCA2jDa_i60wjmuwUIX-vxhbaBmAQONd2D",
      youtube: "https://encrypted-tbn1.gstatic.com/favicon-tbn?q=tbn:ANd9GcTsSu9ICjgkilAXKsKKCu1_vLbGEwp8ChLoQuE5RB7Lv1oOZafk_GyFXKb8CawXEfpF50-jIO-ykUdO4DmGRYogIB4dGoTKTUf3vXy9s3B2iA",
        reddit: "https://encrypted-tbn2.gstatic.com/favicon-tbn?q=tbn:ANd9GcS7ZMDX_UdZ37jHdihEzZol3g2gzSt9uwWjlBZQH2TochQmtpv4NmVK8Diz734sq1hXisk9gniS8nsuhYP5KIzlUcPmmYg5qARt90z8sW7z",
  discord: "https://encrypted-tbn0.gstatic.com/favicon-tbn?q=tbn:ANd9GcQPlpyIwdg_YxCfg2zlbIR8wrjTNWlDct_1ThmrFZxj82CLly1tLvkhdvVWdx1v7wIOA36E7QsaQmXnLbhoGfqrO-Rr41K-HBQrEUT8DBZ9"
    };
  
    const links: Link[] = [];
  
    // 添加 Twitter
    if (socialMedia?.twitter_url) {
      links.push({
        title: "Twitter",
        url: socialMedia.twitter_url,
        icon: iconMap.twitter
      });
    }
  
    // 添加 Instagram
    if (socialMedia?.ins_url) {
      links.push({
        title: "Instagram",
        url: socialMedia.ins_url,
        icon: iconMap.instagram
      });
    }
  
    // 添加 YouTube
    if (socialMedia?.youtube_url) {
      links.push({
        title: "YouTube",
        url: socialMedia.youtube_url,
        icon: iconMap.youtube
      });
    }
    // 添加 Reddit
if (socialMedia?.reddit_url) {
    links.push({
      title: "Reddit",
      url: socialMedia.reddit_url,
      icon: iconMap.reddit
    });
  }
  
  // 添加 Discord
  if (socialMedia?.discord_url) {
    links.push({
      title: "Discord",
      url: socialMedia.discord_url,
      icon: iconMap.discord
    });
  }
  
    return links;
  }