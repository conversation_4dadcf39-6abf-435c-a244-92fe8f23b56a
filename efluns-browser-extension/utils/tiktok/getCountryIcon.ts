import countries from 'i18n-iso-countries'
import en from 'i18n-iso-countries/langs/en.json'
import zh from 'i18n-iso-countries/langs/zh.json'

// 初始化语言支持
countries.registerLocale(en)
countries.registerLocale(zh)

export function getCountryCode(region: string | null | undefined):string | null {
  if (!region) return null
    // 如果已经是双字母代码
    if (region.length === 2) {
      return region
    }
    return countries.getAlpha2Code(region, 'en') || region
}

export function getCountryIcon(region: string | null | undefined):string | null {
  if (!region) return null
  
  // 如果已经是双字母代码
  if (region.length === 2) {
    return `https://flagcdn.com/${region.toLowerCase()}.svg`
  }
  
  // 先尝试英文匹配
  let countryCode = countries.getAlpha2Code(region, 'en')
  // 如果英文没匹配到,尝试中文
  if (!countryCode) {
    countryCode = countries.getAlpha2Code(region, 'zh')
  }
  
  if (countryCode) {
    return `https://flagcdn.com/${countryCode.toLowerCase()}.svg`
  }
  
  // 如果找不到映射,返回原始输入
  return region
}

// 添加一个工具函数来转换国家代码为国旗emoji
export function getCountryFlag(countryCode: string): string {
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt(0));
  return String.fromCodePoint(...codePoints);
}

export function getChineseName(region: string | null | undefined){
  const countryCode = getCountryCode(region)
  if(!countryCode){
    return region
  }
  return countries.getName(countryCode, "zh")
}

export function getCountryCodeFromName(countryName: string | null | undefined): string | null {
  if (!countryName) return null
  
  // 依次尝试不同语言
  const languages = ['zh', 'en']
  for (const lang of languages) {
    const code = countries.getAlpha2Code(countryName, lang)
    if (code) return code
  }
  
  return null
}

