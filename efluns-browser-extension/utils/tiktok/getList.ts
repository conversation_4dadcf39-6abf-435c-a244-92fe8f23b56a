import { Platform } from "@/@types/platform";
import { getPlatform } from "@/entrypoints/Entrypoint.content/utils";
import { getTiktokVideoData } from "@/entrypoints/TiktokEarlyScript.content/utils";

export function getListCount(): [string, number][] | null{
    const platform = getPlatform(window.location.href)
    if(platform === Platform.TIKTOK) {
      const videos = getTiktokVideoData(window.location.href)
      const tags = videos?.videos.map(item=>item.userList).flat() as string[]
      if(!tags.length) return null
      const tagCounts: {[key: string]: number} = {};
      tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
      return Object.entries(tagCounts)
        .sort(([,a], [,b]) => b - a);
    }
    const imgElements =  [...document.querySelectorAll("main a img")].slice(0,20)
    
    if(!imgElements.length) return null
  
      const alts = imgElements
        .map((item) => (item as HTMLImageElement).alt)
        .join(" ");
  
      if(!alts.length) return null
  
      // 使用正则表达式匹配所有@开头的标签
      // The lookbehind assertion ensures the @ is preceded by a space or is at the start of the string.
      const tags = alts.match(/(?<=^|\s)@\p{L}+/gu) || [];
      
      
      // 统计每个标签的出现次数,同时排除不需要的标签
      const tagCounts: {[key: string]: number} = {};
      tags.forEach(tag => {
        const tagWithoutHash = tag.slice(1).toLowerCase();
        tagCounts[tagWithoutHash] = (tagCounts[tagWithoutHash] || 0) + 1;
      });
      
      // 将对象转换为数组并排序
      const sortedEntries = Object.entries(tagCounts)
        .sort(([,a], [,b]) => b - a);
      
      // 转回对象
      return sortedEntries;
  }