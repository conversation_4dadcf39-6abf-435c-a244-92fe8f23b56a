import { Platform } from '@/@types/platform';
import { getPlatform } from '@/entrypoints/Entrypoint.content/utils';

const GET_TAGS = "GET_TAGS"

export function getTagsCount(): [string, number][] | null{
  const platform = getPlatform(window.location.href)
  const imgElements = platform === Platform.TIKTOK ? [...document.querySelectorAll("picture img")].slice(0,20) : [...document.querySelectorAll("main a img")].slice(0,20)
  
  if(!imgElements.length) return null

    const alts = imgElements
      .map((item) => (item as HTMLImageElement).alt)
      .join(" ");

    if(!alts.length) return null

    // 使用正则表达式匹配所有#开头的标签
    const tags = alts.match(/#\p{L}+/gu) || [];
    
    // 需要排除的标签列表
    const excludeTags = ['foryou', 'foryoupage', 'fyp', 'viral', 'trending', 'viralvideo'];
    
    // 统计每个标签的出现次数,同时排除不需要的标签
    const tagCounts: {[key: string]: number} = {};
    tags.forEach(tag => {
      const tagWithoutHash = tag.slice(1).toLowerCase();
      if (!excludeTags.includes(tagWithoutHash) && !tagWithoutHash.startsWith('fyp')) {
        tagCounts[tagWithoutHash] = (tagCounts[tagWithoutHash] || 0) + 1;
      }
    });
    
    // 将对象转换为数组并排序
    const sortedEntries = Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a);
    
    // 转回对象
    return sortedEntries;
}


export async function getTagsFromContentScript(){
    const { getCommonService } = await import('@/services/CommonService')
    const res = await getCommonService().sendMessageToCurrentContent({
        type: GET_TAGS,
        timestamp: Date.now()
    })
    return res
}

