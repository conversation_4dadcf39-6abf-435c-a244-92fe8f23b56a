export async function getUserRegion(url:string) {
    const html = await fetch(url).then(res=>res.text())
    const doc = new DOMParser().parseFromString(html, "text/html");
    try {
        const region = JSON.parse((doc.querySelector("#__UNIVERSAL_DATA_FOR_REHYDRATION__") as HTMLDivElement)?.innerText || "{}")?.["__DEFAULT_SCOPE__"]?.["webapp.user-detail"]?.userInfo?.user?.region
        return region
    } catch (error) {
        return null
    }
}