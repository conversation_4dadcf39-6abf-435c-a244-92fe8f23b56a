
/**
 * Twitter/X 令牌存储库接口
 * 定义了获取 CSRF 令牌和访客令牌的方法
 */
export interface ITwitterTokenRepository {
  getCsrfToken(): Promise<string | null>
  getGuestToken(): Promise<string | null>
}

/**
 * 旧版 Twitter 令牌存储库实现
 * 同时支持 twitter.com 和 x.com 域名
 * @deprecated 已废弃，建议使用 XTokenRepository
 */
export class TwitterTokenRepository implements ITwitterTokenRepository {
  async getCsrfToken(): Promise<string | null> {
    // const twitterCookie = await browser.cookies.get({
    //   url: 'https://twitter.com',
    //   name: 'ct0',
    // })

    // if (twitterCookie) {
    //   return twitterCookie.value
    // }

    const xcomCookie = await browser.cookies.get({
      url: 'https://x.com',
      name: 'ct0',
    })

    return xcomCookie ? xcomCookie.value : null
  }

  async getGuestToken(): Promise<string | null> {
    // const twitterCookie = await browser.cookies.get({
    //   url: 'https://twitter.com',
    //   name: 'gt',
    // })

    // if (twitterCookie) {
    //   return twitterCookie.value
    // }

    const xcomCookie = await browser.cookies.get({
      url: 'https://x.com',
      name: 'gt',
    })
    return xcomCookie ? xcomCookie.value : null
  }
}

/**
 * 新版 X 平台令牌存储库实现
 * 专门处理 x.com 域名的令牌
 */
export class XTokenRepository implements ITwitterTokenRepository {
  async getCsrfToken(): Promise<string | null> {
    const cookie = await browser.cookies.get({
      url: 'https://x.com',
      name: 'ct0',
    })

    return cookie ? cookie.value : null
  }

  async getGuestToken(): Promise<string | null> {
    const cookie = await browser.cookies.get({
      url: 'https://x.com',
      name: 'gt',
    })
    return cookie ? cookie.value : null
  }
}
