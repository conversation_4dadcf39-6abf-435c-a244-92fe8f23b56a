export function isTwitterKolPage(link: string) {
	const url = new URL(link)
	if (!['www.x.com', 'x.com'].includes(url.hostname)) {
		return false
	}

	const pathname = url.pathname
	const nonKOLPrefixPaths = [
		"/home",
		"/explore", 
		"/notifications",
		"/messages",
		"/bookmarks",
		"/lists",
		"/communities",
		"/settings",
		"/search",
		"/i/",
		"/compose",
		"/logout",
		"/login",
		"/signup",
		"/tos",
		"/privacy",
		"/help",
		"/about",
		"/business",
		"/ads",
		"/developers",
		"/status/",
		"/hashtag/",
		"/trending",
		"/moments",
		"/live",
		"/spaces",
		"/analytics",
		"/professional",
		"/verified",
		"/blue",
		"/premium"
	]
	const isNonKOLPath = pathname === '/' || nonKOLPrefixPaths.some((prefix) => pathname.startsWith(prefix))

	return !isNonKOLPath
}
