import {
  ITwitterTokenRepository,
  TwitterTokenRepository,
  XTokenRepository,
} from '../cookie/repositories'



// 初始化请求头
const initHeaders = (
  tweetId: string,
  bearerToken: string,
  csrfToken: string,
  guestToken?: string
) =>
  new Headers([
    ['Content-Type', 'application/json'],
    ['Authorization', 'Bearer ' + bearerToken],
    ['User-Agent', navigator.userAgent],
    ['Referer', `https://x.com/i/web/status/${tweetId}`],
    ['x-twitter-active-user', 'yes'],
    ['x-csrf-token', csrfToken],
    guestToken ? ['x-guest-token', guestToken] : ['x-twitter-auth-type', 'OAuth2Session'],
  ])

// 初始化用户信息请求头
const initUserHeaders = (
  screenName: string,
  bearerToken: string,
  csrfToken: string,
  guestToken?: string
) =>
  new Headers([
    ['Content-Type', 'application/json'],
    ['Authorization', 'Bearer ' + bearerToken],
    ['User-Agent', navigator.userAgent],
    ['Referer', `https://x.com/${screenName}`],
    ['x-twitter-active-user', 'yes'],
    ['x-csrf-token', csrfToken],
    guestToken ? ['x-guest-token', guestToken] : ['x-twitter-auth-type', 'OAuth2Session'],
  ])

// 初始化用户关注列表请求头
const initUserConnectionsHeaders = (
  userId: string,
  bearerToken: string,
  csrfToken: string,
  guestToken?: string
) =>
  new Headers([
    ['Content-Type', 'application/json'],
    ['Authorization', 'Bearer ' + bearerToken],
    ['User-Agent', navigator.userAgent],
    ['Referer', `https://x.com/i/connect_people?user_id=${userId}`],
    ['x-twitter-active-user', 'yes'],
    ['x-csrf-token', csrfToken],
    guestToken ? ['x-guest-token', guestToken] : ['x-twitter-auth-type', 'OAuth2Session'],
  ])


// 初始化用户关注列表（Following）请求头
const initUserFollowingHeaders = (
  screenName: string,
  bearerToken: string,
  csrfToken: string,
  guestToken?: string
) =>
  new Headers([
    ['Content-Type', 'application/json'],
    ['Authorization', 'Bearer ' + bearerToken],
    ['User-Agent', navigator.userAgent],
    ['Referer', `https://x.com/${screenName}/following`],
    ['x-twitter-active-user', 'yes'],
    ['x-csrf-token', csrfToken],
    guestToken ? ['x-guest-token', guestToken] : ['x-twitter-auth-type', 'OAuth2Session'],
  ])

export interface ITweetUseCase {
  version: string
  fetchTweet(): Promise<any>
}

export interface IUserUseCase {
  version: string
  fetchUser(): Promise<any>
}

export interface IUserConnectionsUseCase {
  version: string
  fetchUserConnections(): Promise<any>
}


export interface IUserFollowingUseCase {
  version: string
  fetchUserFollowing(): Promise<any>
}

const xTokenRepo = new XTokenRepository()
const twitterTokenRepo = new TwitterTokenRepository()

type TwitterDomain =  'x.com'

abstract class TweetUseCase implements ITweetUseCase {
  abstract version: string

  protected domain: TwitterDomain = 'x.com'
  protected tokenRepo: ITwitterTokenRepository = xTokenRepo
  protected bearerToken =
    'AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA'

  constructor(readonly tweetId: string) {}

  abstract makeEndpoint(): string

  async fetchTweet(headers?: HeadersInit): Promise<any> {

    const csrfToken = await this.tokenRepo.getCsrfToken()
    const guestToken = await this.tokenRepo.getGuestToken()
    const endpoint = this.makeEndpoint()
    const resp = await fetch(endpoint, {
      method: 'GET',
      headers:
        headers || initHeaders(this.tweetId, this.bearerToken, csrfToken!, guestToken!),
      mode: 'cors',
    })

    if (resp.status === 200) {
      const body = await resp.json()
      console.log("请求成功", body);
      return body
    }

    throw new Error(resp.status.toString())
  }

}

abstract class UserUseCase implements IUserUseCase {
  abstract version: string

  protected domain: TwitterDomain = 'x.com'
  protected tokenRepo: ITwitterTokenRepository = xTokenRepo
  protected bearerToken =
    'AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA'

  constructor(readonly screenName: string) {}

  abstract makeEndpoint(): string

  async fetchUser(headers?: HeadersInit): Promise<any> {
    const csrfToken = await this.tokenRepo.getCsrfToken()
    const guestToken = await this.tokenRepo.getGuestToken()
    const endpoint = this.makeEndpoint()
    const resp = await fetch(endpoint, {
      method: 'GET',
      headers:
        headers || initUserHeaders(this.screenName, this.bearerToken, csrfToken!, guestToken!),
      mode: 'cors',
    })

    if (resp.status === 200) {
      const body = await resp.json()
      console.log("用户信息请求成功", body);
      return body
    }

    throw new Error(resp.status.toString())
  }
}

abstract class UserConnectionsUseCase implements IUserConnectionsUseCase {
  abstract version: string

  protected domain: TwitterDomain = 'x.com'
  protected tokenRepo: ITwitterTokenRepository = xTokenRepo
  protected bearerToken =
    'AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA'

  constructor(readonly userId: string, readonly count: number = 20) {}

  abstract makeEndpoint(): string

  async fetchUserConnections(headers?: HeadersInit): Promise<any> {
    const csrfToken = await this.tokenRepo.getCsrfToken()
    const guestToken = await this.tokenRepo.getGuestToken()
    const endpoint = this.makeEndpoint()
    const resp = await fetch(endpoint, {
      method: 'GET',
      headers:
        headers || initUserConnectionsHeaders(this.userId, this.bearerToken, csrfToken!, guestToken!),
      mode: 'cors',
    })

    if (resp.status === 200) {
      const body = await resp.json()
      console.log("用户关注列表请求成功", body);
      return body
    }

    throw new Error(resp.status.toString())
  }
}

abstract class UserFollowingUseCase implements IUserFollowingUseCase {
  abstract version: string

  protected domain: TwitterDomain = 'x.com'
  protected tokenRepo: ITwitterTokenRepository = xTokenRepo
  protected bearerToken =
    'AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA'

  constructor(readonly userId: string, readonly screenName: string, readonly count: number = 20) {}

  abstract makeEndpoint(): string

  async fetchUserFollowing(headers?: HeadersInit): Promise<any> {
    const csrfToken = await this.tokenRepo.getCsrfToken()
    const guestToken = await this.tokenRepo.getGuestToken()
    const endpoint = this.makeEndpoint()
    const resp = await fetch(endpoint, {
      method: 'GET',
      headers:
        headers || initUserFollowingHeaders(this.screenName, this.bearerToken, csrfToken!, guestToken!),
      mode: 'cors',
    })

    if (resp.status === 200) {
      const body = await resp.json()
      console.log("用户关注列表（Following）请求成功", body);
      return body
    }

    throw new Error(resp.status.toString())
  }
}

// 生成GraphQL查询变量
const makeGraphQlVars = (tweetId: string): TwitterGraphQLVariables => ({
  focalTweetId: tweetId,
  with_rux_injections: false,
  includePromotedContent: false,
  withCommunity: false,
  withQuickPromoteEligibilityTweetFields: false,
  withBirdwatchNotes: false,
  withVoice: false,
  withV2Timeline: true,
})

interface TwitterGraphQLVariables {
  focalTweetId: string
  with_rux_injections: boolean
  includePromotedContent: boolean
  withCommunity: boolean
  withQuickPromoteEligibilityTweetFields: boolean
  withBirdwatchNotes: boolean
  withVoice: boolean
  withV2Timeline: boolean
}

// 生成用户信息GraphQL查询变量
const makeUserGraphQlVars = (screenName: string): UserGraphQLVariables => ({
  screen_name: screenName,
})

interface UserGraphQLVariables {
  screen_name: string
}

// 生成用户关注列表GraphQL查询变量
const makeUserConnectionsGraphQlVars = (userId: string, count: number): UserConnectionsGraphQLVariables => ({
  count: count,
  context: JSON.stringify({
    contextualUserId: userId
  })
})

interface UserConnectionsGraphQLVariables {
  count: number
  context: string
}

// 生成搜索GraphQL查询变量
const makeSearchGraphQlVars = (query: string, count: number, product: string): SearchGraphQLVariables => ({
  rawQuery: query,
  count: count,
  querySource: 'typed_query',
  product: product,
})

interface SearchGraphQLVariables {
  rawQuery: string
  count: number
  querySource: string
  product: string
}

// 生成用户关注列表（Following）GraphQL查询变量
const makeUserFollowingGraphQlVars = (userId: string, count: number): UserFollowingGraphQLVariables => ({
  userId: userId,
  count: count,
  includePromotedContent: false,
})

interface UserFollowingGraphQLVariables {
  userId: string
  count: number
  includePromotedContent: boolean
}

const makeGuestEndpoint = (domain: TwitterDomain, tweetId: string): string => {
  const endpoint = new URL(
    `https://${domain}/i/api/graphql/0hWvDhmW8YQ-S_ib3azIrw/TweetResultByRestId`
  )
  endpoint.searchParams.append(
    'variables',
    JSON.stringify({
      tweetId: tweetId,
      withCommunity: false,
      includePromotedContent: false,
      withVoice: false,
    })
  )
  endpoint.searchParams.append(
    'features',
    JSON.stringify({
      creator_subscriptions_tweet_preview_api_enabled: false,
      tweetypie_unmention_optimization_enabled: true,
      responsive_web_edit_tweet_api_enabled: true,
      graphql_is_translatable_rweb_tweet_is_translatable_enabled: false,
      view_counts_everywhere_api_enabled: false,
      longform_notetweets_consumption_enabled: true,
      responsive_web_twitter_article_tweet_consumption_enabled: false,
      tweet_awards_web_tipping_enabled: false,
      freedom_of_speech_not_reach_fetch_enabled: true,
      standardized_nudges_misinfo: false,
      tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
      longform_notetweets_rich_text_read_enabled: false,
      longform_notetweets_inline_media_enabled: false,
      responsive_web_graphql_exclude_directive_enabled: true,
      verified_phone_label_enabled: false,
      responsive_web_media_download_video_enabled: false,
      responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
      responsive_web_graphql_timeline_navigation_enabled: false,
      responsive_web_enhance_cards_enabled: false,
    })
  )
  endpoint.searchParams.append(
    'fieldToggles',
    JSON.stringify({
      withArticleRichContentState: false,
      withAuxiliaryUserLabels: false,
    })
  )
  return endpoint.href
}

const makeUserEndpoint = (domain: TwitterDomain, screenName: string): string => {
  const endpoint = new URL(
    `https://${domain}/i/api/graphql/jUKA--0QkqGIFhmfRZdWrQ/UserByScreenName`
  )
  endpoint.searchParams.append(
    'variables',
    JSON.stringify(makeUserGraphQlVars(screenName))
  )
  endpoint.searchParams.append(
    'features',
    JSON.stringify({
      responsive_web_grok_bio_auto_translation_is_enabled: false,
      hidden_profile_subscriptions_enabled: true,
      payments_enabled: false,
      profile_label_improvements_pcf_label_in_post_enabled: true,
      rweb_tipjar_consumption_enabled: true,
      verified_phone_label_enabled: false,
      subscriptions_verification_info_is_identity_verified_enabled: true,
      subscriptions_verification_info_verified_since_enabled: true,
      highlights_tweets_tab_ui_enabled: true,
      responsive_web_twitter_article_notes_tab_enabled: true,
      subscriptions_feature_can_gift_premium: true,
      creator_subscriptions_tweet_preview_api_enabled: true,
      responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
      responsive_web_graphql_timeline_navigation_enabled: true,
    })
  )
  endpoint.searchParams.append(
    'fieldToggles',
    JSON.stringify({
      withAuxiliaryUserLabels: true,
    })
  )
  return endpoint.href
}

const makeUserConnectionsEndpoint = (domain: TwitterDomain, userId: string, count: number): string => {
  const endpoint = new URL(
    `https://${domain}/i/api/graphql/kq59bW5DYvbPyvMWgG7luQ/ConnectTabTimeline`
  )
  endpoint.searchParams.append(
    'variables',
    JSON.stringify(makeUserConnectionsGraphQlVars(userId, count))
  )
  endpoint.searchParams.append(
    'features',
    JSON.stringify({
      rweb_video_screen_enabled: false,
      payments_enabled: false,
      profile_label_improvements_pcf_label_in_post_enabled: true,
      rweb_tipjar_consumption_enabled: true,
      verified_phone_label_enabled: false,
      creator_subscriptions_tweet_preview_api_enabled: true,
      responsive_web_graphql_timeline_navigation_enabled: true,
      responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
      premium_content_api_read_enabled: false,
      communities_web_enable_tweet_community_results_fetch: true,
      c9s_tweet_anatomy_moderator_badge_enabled: true,
      responsive_web_grok_analyze_button_fetch_trends_enabled: false,
      responsive_web_grok_analyze_post_followups_enabled: true,
      responsive_web_jetfuel_frame: false,
      responsive_web_grok_share_attachment_enabled: true,
      articles_preview_enabled: true,
      responsive_web_edit_tweet_api_enabled: true,
      graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
      view_counts_everywhere_api_enabled: true,
      longform_notetweets_consumption_enabled: true,
      responsive_web_twitter_article_tweet_consumption_enabled: true,
      tweet_awards_web_tipping_enabled: false,
      responsive_web_grok_show_grok_translated_post: false,
      responsive_web_grok_analysis_button_from_backend: false,
      creator_subscriptions_quote_tweet_preview_enabled: false,
      freedom_of_speech_not_reach_fetch_enabled: true,
      standardized_nudges_misinfo: true,
      tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
      longform_notetweets_rich_text_read_enabled: true,
      longform_notetweets_inline_media_enabled: true,
      responsive_web_grok_image_annotation_enabled: true,
      responsive_web_enhance_cards_enabled: false,
    })
  )
  return endpoint.href
}

const makeSearchEndpoint = (domain: TwitterDomain, query: string, count: number, product: string): string => {
  const endpoint = new URL(
    `https://${domain}/i/api/graphql/cInpW5S3fsZfTCRNZMdkzA/SearchTimeline`
  )
  endpoint.searchParams.append(
    'variables',
    JSON.stringify(makeSearchGraphQlVars(query, count, product))
  )
  endpoint.searchParams.append(
    'features',
    JSON.stringify({
      rweb_video_screen_enabled: false,
      payments_enabled: false,
      profile_label_improvements_pcf_label_in_post_enabled: true,
      rweb_tipjar_consumption_enabled: true,
      verified_phone_label_enabled: false,
      creator_subscriptions_tweet_preview_api_enabled: true,
      responsive_web_graphql_timeline_navigation_enabled: true,
      responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
      premium_content_api_read_enabled: false,
      communities_web_enable_tweet_community_results_fetch: true,
      c9s_tweet_anatomy_moderator_badge_enabled: true,
      responsive_web_grok_analyze_button_fetch_trends_enabled: false,
      responsive_web_grok_analyze_post_followups_enabled: true,
      responsive_web_jetfuel_frame: false,
      responsive_web_grok_share_attachment_enabled: true,
      articles_preview_enabled: true,
      responsive_web_edit_tweet_api_enabled: true,
      graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
      view_counts_everywhere_api_enabled: true,
      longform_notetweets_consumption_enabled: true,
      responsive_web_twitter_article_tweet_consumption_enabled: true,
      tweet_awards_web_tipping_enabled: false,
      responsive_web_grok_show_grok_translated_post: false,
      responsive_web_grok_analysis_button_from_backend: false,
      creator_subscriptions_quote_tweet_preview_enabled: false,
      freedom_of_speech_not_reach_fetch_enabled: true,
      standardized_nudges_misinfo: true,
      tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
      longform_notetweets_rich_text_read_enabled: true,
      longform_notetweets_inline_media_enabled: true,
      responsive_web_grok_image_annotation_enabled: true,
      responsive_web_enhance_cards_enabled: false,
    })
  )
  endpoint.searchParams.append(
    'fieldToggles',
    JSON.stringify({
      withArticleRichContentState: false,
      withAuxiliaryUserLabels: false,
    })
  )
  return endpoint.href
}

const makeUserFollowingEndpoint = (domain: TwitterDomain, userId: string, count: number): string => {
  const endpoint = new URL(
    `https://${domain}/i/api/graphql/0HRVUaBSRLwHSp3nc4HdYg/Following`
  )
  endpoint.searchParams.append(
    'variables',
    JSON.stringify(makeUserFollowingGraphQlVars(userId, count))
  )
  endpoint.searchParams.append(
    'features',
    JSON.stringify({
      rweb_video_screen_enabled: false,
      payments_enabled: false,
      profile_label_improvements_pcf_label_in_post_enabled: true,
      rweb_tipjar_consumption_enabled: true,
      verified_phone_label_enabled: false,
      creator_subscriptions_tweet_preview_api_enabled: true,
      responsive_web_graphql_timeline_navigation_enabled: true,
      responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
      premium_content_api_read_enabled: false,
      communities_web_enable_tweet_community_results_fetch: true,
      c9s_tweet_anatomy_moderator_badge_enabled: true,
      responsive_web_grok_analyze_button_fetch_trends_enabled: false,
      responsive_web_grok_analyze_post_followups_enabled: true,
      responsive_web_jetfuel_frame: false,
      responsive_web_grok_share_attachment_enabled: true,
      articles_preview_enabled: true,
      responsive_web_edit_tweet_api_enabled: true,
      graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
      view_counts_everywhere_api_enabled: true,
      longform_notetweets_consumption_enabled: true,
      responsive_web_twitter_article_tweet_consumption_enabled: true,
      tweet_awards_web_tipping_enabled: false,
      responsive_web_grok_show_grok_translated_post: false,
      responsive_web_grok_analysis_button_from_backend: false,
      creator_subscriptions_quote_tweet_preview_enabled: false,
      freedom_of_speech_not_reach_fetch_enabled: true,
      standardized_nudges_misinfo: true,
      tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
      longform_notetweets_rich_text_read_enabled: true,
      longform_notetweets_inline_media_enabled: true,
      responsive_web_grok_image_annotation_enabled: true,
      responsive_web_enhance_cards_enabled: false,
    })
  )
  return endpoint.href
}

class GuestGraphQLTweetUseCase extends TweetUseCase {
  version: string = 'gql-guest'

  makeEndpoint(): string {
    return makeGuestEndpoint(this.domain, this.tweetId)
  }
}

class GuestGraphQLUserUseCase extends UserUseCase {
  version: string = 'gql-guest-user'

  makeEndpoint(): string {
    return makeUserEndpoint(this.domain, this.screenName)
  }
}

class GuestGraphQLUserConnectionsUseCase extends UserConnectionsUseCase {
  version: string = 'gql-guest-user-connections'

  makeEndpoint(): string {
    return makeUserConnectionsEndpoint(this.domain, this.userId, this.count)
  }
}


class GuestGraphQLUserFollowingUseCase extends UserFollowingUseCase {
  version: string = 'gql-guest-user-following'

  makeEndpoint(): string {
    return makeUserFollowingEndpoint(this.domain, this.userId, this.count)
  }
}

// 定义访客 Twitter GraphQL 推文用例
export class GuestTwitterGraphQLTweetUseCase extends GuestGraphQLTweetUseCase {
  version: string = 'gql-guest-twitter'
  protected domain: TwitterDomain = 'x.com'
  protected tokenRepo: ITwitterTokenRepository = twitterTokenRepo
}

// 定义访客 Twitter GraphQL 用户信息用例
export class GuestTwitterGraphQLUserUseCase extends GuestGraphQLUserUseCase {
  version: string = 'gql-guest-twitter-user'
  protected domain: TwitterDomain = 'x.com'
  protected tokenRepo: ITwitterTokenRepository = twitterTokenRepo
}

// 定义访客 Twitter GraphQL 用户关注列表用例
export class GuestTwitterGraphQLUserConnectionsUseCase extends GuestGraphQLUserConnectionsUseCase {
  version: string = 'gql-guest-twitter-user-connections'
  protected domain: TwitterDomain = 'x.com'
  protected tokenRepo: ITwitterTokenRepository = twitterTokenRepo
}


// 定义访客 Twitter GraphQL 用户关注列表（Following）用例
export class GuestTwitterGraphQLUserFollowingUseCase extends GuestGraphQLUserFollowingUseCase {
  version: string = 'gql-guest-twitter-user-following'
  protected domain: TwitterDomain = 'x.com'
  protected tokenRepo: ITwitterTokenRepository = twitterTokenRepo
}


