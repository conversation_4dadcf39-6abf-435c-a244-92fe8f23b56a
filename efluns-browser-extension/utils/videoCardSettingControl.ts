import { VideoCardSettings } from "@/@types/card";

export async function videoCardSettingControl(){
    const settings = await videoCardSettingsState.getValue()
    changeBodyAttributeBySettings(settings)
    videoCardSettingsState.watch(changeBodyAttributeBySettings)
}

function changeBodyAttributeBySettings(settings:VideoCardSettings){
    const body = document.body
    for(const key in settings){
        if(!settings[key as keyof VideoCardSettings]){
            body.setAttribute(key, "true")
        }else{
            body.removeAttribute(key) 
        }
    }
}