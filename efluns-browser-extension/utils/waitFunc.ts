/**
 * 等待函数返回true或超时
 * @param fn 要执行的函数
 * @param config 配置项
 * @param config.timeout 超时时间，默认5000ms
 * @param config.interval 检查间隔，默认100ms
 * @returns Promise<void>
 */
export const waitFunc = <T>(
  fn: () => T | Promise<T>,
  config: { timeout?: number; interval?: number } = {},
): Promise<T> => {
  const { timeout = 5000, interval = 100 } = config;

  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const check = async () => {
      try {
        const result = await fn();
        if (result) {
          resolve(result);
          return;
        }

        if (Date.now() - startTime > timeout) {
          reject(new Error(`等待超时: ${timeout}ms`));
          return;
        }

        setTimeout(check, interval);
      } catch (error) {
        reject(error);
      }
    };

    check();
  });
};
