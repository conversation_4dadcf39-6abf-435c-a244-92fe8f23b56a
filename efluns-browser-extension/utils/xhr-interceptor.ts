/* eslint-disable prefer-rest-params */

import { RequestConfig, ResponseConfig } from "@/@types/xhr";


interface XHRInterceptorOptions {
  targetUrl: string | RegExp | Array<string | RegExp>;
  onRequest?: (config: RequestConfig) => void;
  onResponse?: (config: ResponseConfig) => void;
  onError?: (error: Error, phase: 'request' | 'response') => void;
  beforeRequest?: (config: any) => any | Promise<any>;
  afterResponse?: (response: any) => any | Promise<any>;
}

export function interceptXHR(options: XHRInterceptorOptions) {
  const originalXHR = window.XMLHttpRequest;

  window.XMLHttpRequest = function () {
    const xhr = new originalXHR();
    const send = xhr.send;
    const open = xhr.open;
    let requestUrl = '';
    let requestBody: any;
    let requestConfig: any;
    const headers: Record<string, string> = {}; // 移到外层

    // 在实例创建时就重写 setRequestHeader
    const originalSetRequestHeader = xhr.setRequestHeader;
    xhr.setRequestHeader = function (key: string, value: string) {
      headers[key.toLowerCase()] = value;
      return originalSetRequestHeader.apply(this, [key, value]);
    };

    xhr.open = function (method, url) {
      requestUrl = url as string;
      (this as any)._method = method;
      // @ts-expect-error 111
      return open.apply(this, arguments);
    };

    xhr.send = function (body) {
      const isTargetUrl = Array.isArray(options.targetUrl)
        ? options.targetUrl.some(pattern =>
            pattern instanceof RegExp ? pattern.test(requestUrl) : requestUrl.includes(pattern),
          )
        : options.targetUrl instanceof RegExp
          ? options.targetUrl.test(requestUrl)
          : requestUrl.includes(options.targetUrl);

      if (isTargetUrl) {
        try {
          // 解析 URL 和查询参数
          const urlObj = new URL(requestUrl, window.location.origin);
          const query: Record<string, string> = {};
          urlObj.searchParams.forEach((value, key) => {
            query[key] = value;
          });

          requestConfig = {
            method: (this as any)._method || 'GET',
            url: requestUrl,
            query: Object.keys(query).length > 0 ? query : undefined,
            headers,
            body: body
              ? headers['content-type'] === 'application/json'
                ? JSON.parse(body as string)
                : body
              : null,
            withCredentials: xhr.withCredentials,
          };

          if (options.beforeRequest) {
            const modifiedConfig = options.beforeRequest(requestConfig);
            if (modifiedConfig) {
              body = JSON.stringify(modifiedConfig.body);
            }
          }

          options.onRequest?.(requestConfig);
        } catch (e) {
          options.onError?.(new Error('解析请求体失败: ' + e), 'request');
        }

        this.addEventListener('load', function () {
          try {
            const responseHeaders: Record<string, string> = {};
            this.getAllResponseHeaders()
              ?.split('\r\n')
              .forEach(line => {
                const [key, value] = line.split(': ');
                if (key && value) {
                  responseHeaders[key.toLowerCase()] = value;
                }
              });

            // 根据响应的 Content-Type 来解析响应体
            const contentType = responseHeaders['content-type'] || '';
            const response = contentType.includes('application/json')
              ? JSON.parse(this.response)
              : this.response;

            const responseConfig = {
              status: this.status,
              headers: responseHeaders,
              response,
              requestConfig,
            };

            if (options.afterResponse) {
              const modifiedResponse = options.afterResponse(responseConfig);
              if (modifiedResponse) {
                Object.defineProperty(this, 'response', {
                  writable: true,
                  value: JSON.stringify(modifiedResponse),
                });
              }
            }

            options.onResponse?.(responseConfig);
          } catch (e) {
            options.onError?.(new Error('解析响应失败: ' + e), 'response');
          }
        });
      }
      // @ts-expect-error 111
      return send.apply(this, arguments);
    };

    return xhr;
  } as any;
}

/*   interceptXHR({
    targetUrl: ['/api', /\.json$/],
    beforeRequest: (config) => {
      // 修改请求配置
      config.headers['X-Custom'] = 'value';
      return config;
    },
    afterResponse: (response) => {
      // 修改响应数据
      if (response.status === 200) {
        response.response.extraField = 'value';
      }
      return response;
    },
    onRequest: (config) => {
      console.log('请求配置:', config);
    },
    onResponse: (response) => {
      console.log('响应数据:', response);
    },
    onError: (error, phase) => {
      console.error(`${phase}阶段发生错误:`, error);
    }
  }); */
