const RELOAD_COUNT_KEY = 'youtube_channel_reload_count'
const MAX_RELOAD_ATTEMPTS = 1

/**
 * 直接获取当前页面的 channelId
 */
async function getCurrentPageChannelId(): Promise<string | null> {
	try {
		// 先检查 alternate link 是否与当前页面一致
		const alternateLink = document.querySelector<HTMLLinkElement>('link[rel="alternate"][media="handheld"]')
		if (alternateLink) {
			const alternateHref = alternateLink.getAttribute('href')
			const currentHref = window.location.href

			// 标准化 URL 进行比较
			const normalizeUrl = (url: string) => {
				// 移除协议和 www，统一格式
				return url?.split('.youtube.com')?.[1]
			}
			console.log(normalizeUrl(alternateHref || ''), normalizeUrl(currentHref || ''), 'alternateHref')
			if (alternateHref && normalizeUrl(alternateHref || '') !== normalizeUrl(currentHref || '')) {
				// URL 不一致，需要 reload
				const reloadCount = parseInt(sessionStorage.getItem(RELOAD_COUNT_KEY) || '0')

				if (reloadCount < MAX_RELOAD_ATTEMPTS) {
					console.log(
						`[getCurrentPageChannelId] Alternate link 不匹配，执行 reload (${reloadCount + 1}/${MAX_RELOAD_ATTEMPTS})`
					)
					sessionStorage.setItem(RELOAD_COUNT_KEY, String(reloadCount + 1))
					window.location.reload()
					return null
				} else {
					console.warn(`[getCurrentPageChannelId] 已达到最大 reload 次数 (${MAX_RELOAD_ATTEMPTS})`)
					// 清除计数器
					sessionStorage.removeItem(RELOAD_COUNT_KEY)
				}
			} else if (alternateHref) {
				// URL 一致，清除 reload 计数器
				sessionStorage.removeItem(RELOAD_COUNT_KEY)
			}
		}

		// 从 canonical URL 提取 channelId
		const canonical = document.querySelector('link[rel="canonical"]')
		if (canonical) {
			const href = canonical.getAttribute('href')
			if (href) {
				const match = href.match(/\/channel\/(UC[^/?]+)/)
				if (match && match[1]) {
					return match[1]
				}
			}
		}
		return null
	} catch (error) {
		console.error('[getCurrentPageChannelId] 获取channelId失败:', error)
		return null
	}
}
// 注入到window对象
;(window as any).getCurrentPageChannelId = getCurrentPageChannelId
export { getCurrentPageChannelId }
