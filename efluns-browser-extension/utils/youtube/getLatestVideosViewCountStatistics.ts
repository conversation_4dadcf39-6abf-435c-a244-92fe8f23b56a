import { orderBy } from 'lodash-es'
import { InsCardData, InstagramPost, TiktokCardData, YoutubeVideo } from '../../@types'
import { extractCount } from '../extarctCount'
import { StatsCardProps } from '@/@types/card'
import { videoLastDateList } from '@/constants/infocard'
import { getMedian } from '../numbers'
import { changeTiktokMedianNumber } from '@/entrypoints/TiktokEarlyScript.content/utils/changeTiktokCardDataStyle'
import { changeInsMedianNumber } from '@/entrypoints/InstagramEarlyScript.content/utils/changeInsPosterData'
import { changeYoutubeMedianNumber } from '@/entrypoints/YoutubeEarlyScript.content/utils/changeYoutubeCardData'

const log = makeModuleLog('getLatestVideosViewCountStatistics')
export default function getLatestVideosViewCountStatistics(videos: YoutubeVideo[] | InstagramPost[]) {
	log({ videos })
	const viewCounts = videos.map((item) => 'views' in item ? extractCount(item.views) : extractCount(item.like_count.toString()))
	const avgViewCount = getMedian(viewCounts)
	const sortedViewCounts = viewCounts.sort((a, b) => b - a)
	const trimmedViewCounts = sortedViewCounts.slice(1, -1)
	const trimmedAvgViewCount = getMedian(trimmedViewCounts)

	return {
		avgViewCount,
		trimmedAvgViewCount
	}
}
const countFormatter = new Intl.NumberFormat('en', { notation: 'compact' })

export function getTiktokStatistics(videos: TiktokCardData[], lastVideoDateId: string): StatsCardProps {
	const datas = videos.filter(createFilterFunc(lastVideoDateId))
	const avgViewCount = getMedian(datas.map(item => item.playCount))
	const avgEr = getMedian(datas.map(item => computeTiktokEr(item)))
	const argLikes = getMedian(datas.map(item => item.diggCount))
	const argComments = getMedian(datas.map(item => item.commentCount))

	changeTiktokMedianNumber(avgViewCount)
	
	return {
		stats: [
			{ label: "Estimated Price",key:"estimatedPrice", value: avgViewCount},
			{ label: "Med Likes", value: argLikes ? countFormatter.format(argLikes) : 0, key: "likes" },
			{ label: "Med Views", value: avgViewCount ? countFormatter.format(avgViewCount) : 0, key: "views" },
			{ label: "Med Comments", value: argComments ? countFormatter.format(argComments) : 0, key: "comments" },
			{ label: "Med ER", value: formatEr(avgEr), key: "er" },
		]
	}
}

export function getInstagramStatistics(videos: InsCardData[], lastVideoDateId: string): StatsCardProps {
	const datas = videos.filter(createFilterFunc(lastVideoDateId))
	const avgViewCount = getMedian(datas.map(item => item.play_count || 0))
	const avgEr = getMedian(datas.map(item => computeInsEr(item)))
	const argLikes = getMedian(datas.map(item => item.like_count || 0))
	const argComments = getMedian(datas.map(item => item.comment_count || 0))

	const isHideLikeKol = datas.filter(item => item.like_count === 3).length / datas.length > 0.3
	const isReelsPage = window.location.href.includes("/reels/")
	
	changeInsMedianNumber({
		argLikeNumber: argLikes,
		argCommentNumber: argComments,
		argPlayCount: avgViewCount,
		isHideLikeKol
	})

	return {
		stats: [
			{ label: "Estimated Price",key:"estimatedPrice", value: avgViewCount},
			{ label: "Med Likes", value: isHideLikeKol ? "hide" : argLikes ? countFormatter.format(argLikes) : 0, key: "likes" },
			{ label: "Med Views", value: avgViewCount ? countFormatter.format(avgViewCount) : 0, key: "views" },
			{ label: "Med Comments", value: argComments ? countFormatter.format(argComments) : 0, key: "comments" },
			{ label: "Med ER", value: isHideLikeKol ? "hide" : formatEr(avgEr), key: "er" }
		]
	}
}

export function getYoutubeStatistics(videos: YoutubeVideo[], lastVideoDateId: string): StatsCardProps {
	const datas = videos.filter(createFilterFunc(lastVideoDateId))
	const avgViewCount = getMedian(datas.map(item => item.views ? extractCount(item.views.toString()) : 0))
	changeYoutubeMedianNumber(avgViewCount)
	
	return {
		stats: [
			{ label: "Estimated Price",key:"estimatedPrice", value: avgViewCount},
			{ label: "Med Likes", value: "", key: "likes" },
			{ label: "Med Views", value: avgViewCount ? countFormatter.format(avgViewCount) : 0, key: "views" },
			{ label: "Med ER", value: "", key: "er" },
		]
	}
}

function createFilterFunc(lastVideoDateId:string){
	const videoOption = (location.href.includes("/reels")?videoLastDateList.filter(item=>item.type === "count"):videoLastDateList).find(item=>item.id === lastVideoDateId) || videoLastDateList[1]
	const currentTimeMs = Date.now()
	if(videoOption.type === "days"){
		const lastVideoDateMs = currentTimeMs - (videoOption.value * 24 * 60 * 60 * 1000)
		return (video: {created_time:number})=>{
			const videoTimeMs = video.created_time * 1000
			return videoTimeMs > lastVideoDateMs
		}
	}else{
		return (video: {created_time:number,isPinned?:boolean},index:number,array:any[])=>{
			const nonPinnedCount = array
				.slice(0, index + 1)
				.filter(v => !v.isPinned)
				.length;
			return nonPinnedCount <= videoOption.value && !video.isPinned;
		}
	}

}



function computeTiktokEr(tiktokCardData: TiktokCardData) {
    return (tiktokCardData.collectCount + tiktokCardData.diggCount + tiktokCardData.commentCount) / tiktokCardData.playCount * 100;
}

function computeInsEr(insCardData: InsCardData) {
	if(!insCardData.play_count) return 0;
   return(insCardData.like_count + insCardData.comment_count) / insCardData.play_count * 100;
}

function formatEr(er:number){
	if(!er) return '';
	return er.toFixed(1) + '%';
}
