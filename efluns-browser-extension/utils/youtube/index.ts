import { Link, YoutubeChannelData, YoutubeVideo } from '@/@types'
import { EMAIL_REG } from '@/constants/reg'
import { getCommonService } from '@/services/CommonService'
import { first } from 'lodash-es'
import { Innertube, YTNodes } from 'youtubei.js/web'
import { isPlugin } from '../isPlugin'
import { saveYoutubeData } from '@/entrypoints/YoutubeEarlyScript.content/utils/changeYoutubeCardData'
import { testAdDescription } from '../regular'

const log = makeModuleLog('YoutubeUtils')

let youtube: Innertube

export const  GET_YOUTUBE_DATA = "GET_YOUTUBE_DATA"


export async function getYoutubeService() {
	return (youtube ??= await Innertube.create({
		generate_session_locally: true,
		fetch: async (input: RequestInfo | URL, init?: RequestInit) => fetch(input, init)
	}))
}

const cache = new Map<string, YoutubeChannelData>()

const getCache = (url: string) => {
	if(cache.has(url)){
		return cache.get(url)
	}
	return null
}

const setCache = (url: string, data: YoutubeChannelData) => {
	if(!data){
		return
	}
	// 控制缓存大小，最多保存100个条目
	if (cache.size >= 10) {
		// 删除最早添加的条目
		const firstKey = Array.from(cache.keys())[0];
		cache.delete(firstKey);
	}
	log('setCache', {
		url,
		data
	})
	cache.set(url, data);
}

export async function getYoutubeData(url: string): Promise<YoutubeChannelData> {
	const cacheData = getCache(url)
	if(cacheData){
		return cacheData
	}
	if(isPlugin()){
		const res = await getCommonService().sendMessageToCurrentYoutubeContent({
			action: GET_YOUTUBE_DATA,
			url
		});
		setCache(url, res?.data)
		return res?.data
	}
	const navigationEndpoint = await (await getYoutubeService()).resolveURL(url)
	const channelId = navigationEndpoint.payload.browseId
	const channel = await youtube.getChannel(channelId)
	const about = await channel.getAbout()
	const description =
		(about.type === 'AboutChannel'
			? (about as YTNodes.AboutChannel).metadata?.description
			: (about as YTNodes.ChannelAboutFullMetadata).description.text) ?? ''
	const links = (
		about.type === 'AboutChannel'
			? (about as YTNodes.AboutChannel).metadata?.links.map((item) => ({
					title: item.title.text,
					url: `https://${item.link.text}`,
					icon: first(item.favicon)?.url
				}))
			: []
	) as Link[]

	const videos = (await channel.getVideos()).videos.map((item) => {
		const video = item as YTNodes.Video
		return {
			id: video.id,
			title: video.title.text,
			description: video.description,
			views: video.view_count.text,
			url: video.endpoint.metadata.url && new URL(video.endpoint.metadata.url, window.location.origin).toString(),
			created_time: parseTimeAgo(video.published.text || ''),
			is_paid_partnership:testAdDescription(video.description)
		}
	}) as YoutubeVideo[]

	const emailInDescription = description.match(EMAIL_REG)?.[0] ?? null

	const region = (about.type === 'AboutChannel' 
		? (about as YTNodes.AboutChannel).metadata?.country
		: (about as YTNodes.ChannelAboutFullMetadata).country?.text) || ''
	
	const data: YoutubeChannelData = {
		id: channelId,
		url: channel.metadata.url!,
		links,
		emailInDescription,
		videos,
		region
	}
	saveYoutubeData(data)
	setCache(url, data)
	return data
}

//检查视频中是否有广告
export async function checkVideoSponsors(videoId: string) {
    // 1. 首先需要计算视频ID的SHA256哈希值
    async function sha256(text: string) {
        const encoder = new TextEncoder();
        const data = encoder.encode(text);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return Array.from(new Uint8Array(hashBuffer))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
    }

    try {
        // 2. 获取哈希值的前4个字符
        const hash = await sha256(videoId);
        const hashPrefix = hash.substring(0, 4);
        
        // 3. 构建正确的URL
        const categories = encodeURIComponent(JSON.stringify(["sponsor"]));
        const url = `https://sponsor.ajay.app/api/skipSegments/${hashPrefix}?videoID=${videoId}&categories=${categories}`;
        
        const response = await fetch(url);
        
        if (response.status === 404) {
            console.log('该视频没有被标记的赞助商片段');
            return null;
        }
        
        if (response.status === 200) {
            const segments = await response.json();
            console.log('返回数据:', segments);
            return segments;
        }
    } catch (error) {
        console.error('请求出错:', error);
        return null;
    }
}

