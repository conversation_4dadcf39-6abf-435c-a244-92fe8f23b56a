import { Platform } from "@/@types/platform";
import { getPlatform } from "@/entrypoints/Entrypoint.content/utils";

export function startInjectSimilarFromCheckboxes() {
  if(getPlatform(location.href) !== Platform.YOUTUBE) return

  const observer = new MutationObserver((mutations) => {
    if(!isVideoTabSelected()) return
    // 每次变化都尝试注入
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node instanceof HTMLElement) {
          if(node.tagName === 'YTD-RICH-ITEM-RENDERER') {
            injectCheckboxesToVideo(node as HTMLElement)
          }
        }
      });
    });
  });

  observer.observe(document.body, { childList: true, subtree: true });

  // 首次也要注入一次
  injectCheckboxesToVideos();
  checkBoxBtnListener()
  onMessage("getYoutubeVideoIds",()=>getSelectedVideoIds())
}


//判断是不是视频tab被选中
  function isVideoTabSelected() {
    // const tabs = document.querySelectorAll('#tabsContent yt-tab-shape');
    // if (tabs.length >= 2) {
    //   return tabs[1].getAttribute('aria-selected') === 'true';
    // }
    // return false;
    return location.href.includes("/videos")
  }


  function createSelectCheckBox(videoId:string,index:number){
    return ` <div class="ek_similar_from__selection-container">
              <input type="checkbox" id="${videoId}" class="ek_similar_from__selection-checkbox" ${index < 6 ? "checked" : ""}>
              <label for="${videoId}" class="ek_similar_from__selection-label">
                  <div class="ek_similar_from__selection-box"></div>
                  <span class="ek_similar_from__selection-text">Similar from</span>
              </label>
            </div>`
  }

  function getElementIndex(el:HTMLElement) {
    if (!el || !el.parentNode) return -1;
    return Array.prototype.indexOf.call(el.parentNode.children, el);
  }

  function injectCheckboxesToVideos() {
    // 只在视频Tab选中时注入
    if (!isVideoTabSelected()) return;
  
    document.querySelectorAll('ytd-rich-item-renderer').forEach(item => {

      
      injectCheckboxesToVideo(item as HTMLElement)
    });
  }
  

  function injectCheckboxesToVideo(el:HTMLElement){
    
    // 防止重复注入
    if (el.querySelector('.ek_similar_from__selection-container')) return console.log("已注入");
    
    // 获取videoId
    const a = el.querySelector('a#thumbnail');
    if (!a) return;
    const href = a.getAttribute('href');
    const match = href && href.match(/v=([^&]+)/);
    if (!match) return;
    const videoId = match[1];
    const checkbox = createSelectCheckBox(videoId,getElementIndex(el));
    // 你可以选择插入到item的合适位置
    el.insertAdjacentHTML('beforeend',checkbox);
  }

  function checkBoxBtnListener(){
    document.body.addEventListener('click',(e)=>{
      if( e.target instanceof HTMLElement && e.target.classList.contains('ek_similar_from__selection-checkbox')){
        setSimilarFromMode()
      }
    })
  }


  let timer:any = null;

 export function setSimilarFromMode(){
  if(timer) clearTimeout(timer)    
  document.querySelector('#contents')?.setAttribute('similar_from_videos',"1")
  timer = setTimeout(()=>{
    removeSimilarFromMode()
  },60000)
 }

 export function removeSimilarFromMode(){
  if(timer) clearTimeout(timer)
  document.querySelector('#contents')?.removeAttribute('similar_from_videos')
 }

 export function getSelectedVideoIds(){
  if(getPlatform(location.href) !== Platform.YOUTUBE || !location.href.includes("/videos")) return []
  return Array.from(document.querySelectorAll(".ek_similar_from__selection-checkbox:checked")).map(e=>e.id)
 }