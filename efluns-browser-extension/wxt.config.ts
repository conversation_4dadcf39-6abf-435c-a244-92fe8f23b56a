import react from '@vitejs/plugin-react'
import { defineConfig } from 'wxt'

// See https://wxt.dev/api/config.html
export default defineConfig({
	vite: () => ({
		plugins: [
			react({
				jsxImportSource: '@emotion/react',
				babel: {
					presets: ['jotai/babel/preset']
				}
			})
		],
		build: {
			minify: false, // 禁用压缩
			sourcemap: true,
			rollupOptions: {
				output: {
					compact: false, // 禁用紧凑化
					format: 'es'
				}
			}
		}
	}),

	manifest: {
		name: '__MSG_extensionName__',
		description: '__MSG_extensionDescription__',
		default_locale: 'en',
		permissions: [
			'tabs',
			'scripting',
			'storage',
			'downloads',
			// 'offscreen',
			'declarativeNetRequestWithHostAccess',
			'cookies'
		],
		minimum_chrome_version: '116',
		// key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArvZ9tDqD7PdYZ9Y5Hq3NzlaTWLcB6RgWZMmD5Z6RZVQlLJh3sYDbFb2Qwkol",
		host_permissions: ['<all_urls>'],
		//    optional_host_permissions: [
		//   '*://*.tiktokglobalshop.com/*',
		//   '*://*.tiktok.com/*',
		//   '*://*.tiktokshop.com/*',
		//   '*://*.easykol.com/*',
		//   'https://docs.google.com/*'
		// ],
		optional_permissions: ['sessions'],
		content_security_policy: {
			extension_pages:
				"script-src 'self' 'wasm-unsafe-eval' http://localhost:3000 http://localhost:3001; object-src 'self'"
		},
		web_accessible_resources: [
			{
				resources: ['content-scripts/AIFilterScript.css'],
				matches: ['<all_urls>']
			}
		]
	},
	imports: {
		imports: [
			{
				name: 'css',
				from: '@emotion/react'
			}
		],
		presets: ['react']
	},

	runner: {
		disabled: true
	}
})
