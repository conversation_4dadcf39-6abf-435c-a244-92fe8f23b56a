node_modules/
dist/
build/
.DS_Store
*.log
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.pnpm-store/
pnpm-lock.yaml
package-lock.json
yarn.lock

# TypeScript
*.tsbuildinfo
.tsc-cache/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Configuration files with sensitive data
packages/extension/src/config/config.ts
packages/backend/wrangler.toml

# Cloudflare Workers
.wrangler/
packages/backend/dist/
packages/backend/*.js
packages/backend/*.js.map
packages/backend/middleware-insertion-facade.js
packages/backend/middleware-loader.entry.ts

# Release files (generated)
packages/extension/release/

# Scripts (generated)
packages/extension/scripts/