# Instagram Influencer Finder Chrome Extension

一个用于查找 Instagram 相关影响者的 Chrome 扩展插件，采用 monorepo 架构，使用 Hono + Cloudflare Workers 后端服务。

## 项目结构

```
my-app/
├── packages/
│   ├── extension/      # Chrome 扩展前端
│   ├── backend/        # Hono + Cloudflare Workers 后端服务
│   └── shared/         # 共享类型定义
└── pnpm-workspace.yaml
```

## 安装步骤

### 1. 安装依赖

```bash
# 在项目根目录执行
cd my-app
pnpm install
```

### 2. 启动后端服务

```bash
# 在新的终端窗口中
cd packages/backend
pnpm dev
```

后端服务将在 `http://localhost:8787` 启动。

### 3. 构建扩展

```bash
# 在另一个终端窗口中
cd packages/extension
pnpm build
```

### 4. 加载扩展到 Chrome

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `/my-app/packages/extension/dist` 文件夹
6. 扩展将出现在扩展列表中

## 使用方法

### 基本使用

1. **访问 Instagram**
   - 打开 Instagram 网站 (https://www.instagram.com)
   - 导航到任意影响者的个人主页（例如：https://www.instagram.com/username/）

2. **打开侧边栏**
   - 点击 Chrome 工具栏中的扩展图标
   - 或者右键点击扩展图标，选择"打开侧边面板"
   - 侧边栏将在浏览器右侧打开

3. **查看相关影响者**
   - 扩展会自动检测当前页面的 Instagram 用户名
   - 加载并显示相关的影响者列表
   - 每次显示一个影响者卡片

### 功能说明

- **Next 按钮**: 查看下一个影响者
- **Save 按钮**: 收藏当前影响者（数据保存在本地）
- **More 按钮**: 重新加载相关影响者列表
- **点击卡片**: 在新标签页中打开该影响者的 Instagram 主页

### 注意事项

1. 必须在 Instagram 影响者的个人主页上使用
2. 确保后端服务正在运行（`http://localhost:8787`）
3. 首次使用可能需要刷新页面

## 开发相关

### 技术栈

- **前端**: React 18 + TypeScript + shadcn/ui + Tailwind CSS
- **后端**: Hono + TypeScript + Cloudflare Workers + D1 Database
- **ORM**: Drizzle ORM + Zod 数据验证
- **构建工具**: Vite + pnpm workspaces
- **Chrome API**: Manifest V3 + Side Panel API

### 开发模式

```bash
# 前端开发（支持热重载）
cd packages/extension
pnpm dev

# 后端开发（支持热重载）
cd packages/backend
pnpm dev
```

### API 配置

后端使用 RapidAPI 获取 Instagram 数据。需要在 `packages/backend/.env` 文件中配置：

```env
RAPIDAPI_KEY=your_rapidapi_key_here
RAPIDAPI_HOST=instagram-scraper-api2.p.rapidapi.com
```

## 故障排除

1. **"This is not available here" 错误**
   - 确保在 Instagram 影响者的个人主页上
   - URL 格式应为：`https://www.instagram.com/username/`

2. **无法加载影响者**
   - 检查后端服务是否运行
   - 检查控制台是否有错误信息
   - 确认 RapidAPI 密钥配置正确

3. **扩展图标未显示**
   - 确保扩展已正确加载
   - 尝试固定扩展到工具栏

## License

MIT