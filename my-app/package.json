{"name": "my-app", "version": "1.0.0", "private": true, "description": "Monorepo for Chrome Extension and Backend", "scripts": {"dev": "pnpm --parallel run dev", "build": "pnpm -r run build", "lint": "pnpm run lint", "type-check": "pnpm run type-check", "clean": "pnpm run clean", "test": "pnpm run test"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}