# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
*.js
*.js.map

# Wrangler generated files
.wrangler/
wrangler.toml.bak

# Environment files
.env
.env.local
.env.production
.dev.vars

# Logs
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Temporary files
*.tmp
*.temp

# Cloudflare Workers specific
middleware-insertion-facade.js
middleware-loader.entry.ts
