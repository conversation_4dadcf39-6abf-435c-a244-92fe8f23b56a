# 🚀 后端部署管理文档

## 📋 **部署信息**

### **生产环境**
- **URL**: https://my-app-backend-prod.lyoolio1o1.workers.dev
- **账号**: <EMAIL>'s Account
- **账号 ID**: 9456d7b520b2400f94d6cd8cf9c1ad64
- **Worker 名称**: my-app-backend-prod
- **数据库**: kol_prod (bc36fcc4-93e0-41a9-8bee-8e24cf52e187)

### **开发环境**
- **本地端口**: http://localhost:8787
- **数据库**: my-app-db-local (594a01bb-fb77-4baa-9b06-cb04549d59c3)

## 🛠️ **部署命令**

### **日常开发**
```bash
# 启动本地开发服务器
npm run dev

# 构建代码
npm run build

# 生成数据库迁移
npm run db:generate

# 应用本地数据库迁移
npm run db:migrate
```

### **生产部署**
```bash
# 1. 构建代码
npm run build

# 2. 应用生产数据库迁移（如有新迁移）
wrangler d1 migrations apply kol_prod --env production --remote

# 3. 部署到生产环境
wrangler deploy --env production

# 4. 验证部署
curl https://my-app-backend-prod.lyoolio1o1.workers.dev/health
```

## 🗄️ **数据库管理**

### **本地数据库**
```bash
# 查看本地数据库状态
npm run quota:list

# 设置用户配额
npm run quota:set <EMAIL> 1000

# 重置配额
npm run quota:reset <EMAIL>
```

### **生产数据库**
```bash
# 查看生产数据库信息
wrangler d1 info kol_prod --env production

# 执行生产数据库查询
wrangler d1 execute kol_prod --env production --remote --command="SELECT COUNT(*) FROM users"

# 备份生产数据库
wrangler d1 export kol_prod --env production --remote --output=prod-backup-$(date +%Y%m%d).sql
```

## 🔧 **配置文件说明**

### **wrangler.toml 关键配置**
```toml
# 账号配置
account_id = "9456d7b520b2400f94d6cd8cf9c1ad64"

# 生产环境
[env.production]
name = "my-app-backend-prod"

# 生产数据库
[[env.production.d1_databases]]
binding = "DB"
database_name = "kol_prod"
database_id = "bc36fcc4-93e0-41a9-8bee-8e24cf52e187"
```

## 📝 **部署检查清单**

### **部署前检查**
- [ ] 代码已提交到 Git
- [ ] 本地测试通过
- [ ] 数据库迁移文件已生成
- [ ] 环境变量配置正确

### **部署步骤**
1. [ ] `npm run build` - 构建代码
2. [ ] `wrangler d1 migrations apply kol_prod --env production --remote` - 数据库迁移
3. [ ] `wrangler deploy --env production` - 部署 Worker
4. [ ] 测试健康检查端点
5. [ ] 验证关键功能

### **部署后验证**
- [ ] 健康检查：`curl https://my-app-backend-prod.lyoolio1o1.workers.dev/health`
- [ ] 数据库连接正常
- [ ] 认证功能正常
- [ ] API 响应正常

## 🚨 **故障排除**

### **常见问题**

1. **部署失败**
   ```bash
   # 检查账号权限
   wrangler whoami
   
   # 重新登录
   wrangler auth login
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   wrangler d1 info kol_prod --env production
   
   # 重新应用迁移
   wrangler d1 migrations apply kol_prod --env production --remote
   ```

3. **环境变量问题**
   ```bash
   # 检查配置
   cat wrangler.toml
   
   # 验证环境变量
   wrangler deploy --env production --dry-run
   ```

## 📊 **监控和日志**

### **查看日志**
```bash
# 实时查看生产日志
wrangler tail my-app-backend-prod --env production

# 查看特定时间段的日志
wrangler tail my-app-backend-prod --env production --since 1h
```

### **性能监控**
- Cloudflare Dashboard: https://dash.cloudflare.com/
- Workers Analytics: 查看请求量、错误率、响应时间
- D1 Analytics: 查看数据库查询性能

## 🔄 **回滚策略**

### **快速回滚**
```bash
# 查看部署历史
wrangler deployments list my-app-backend-prod --env production

# 回滚到上一个版本
wrangler rollback my-app-backend-prod --env production
```

### **数据库回滚**
```bash
# 备份当前数据
wrangler d1 export kol_prod --env production --remote --output=rollback-backup.sql

# 如需回滚数据库，需要手动执行 SQL 脚本
```

## 📞 **联系信息**

- **Cloudflare 账号**: <EMAIL>
- **项目仓库**: [您的 Git 仓库地址]
- **文档更新**: 2025-07-20

---

**⚠️ 重要提醒**：
- 生产环境操作前请务必备份
- 数据库迁移不可逆，请谨慎操作
- 定期检查 Cloudflare 配额使用情况
