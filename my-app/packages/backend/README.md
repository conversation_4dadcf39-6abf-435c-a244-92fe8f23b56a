# Instagram KOL Finder - Backend API

基于 Hono 框架构建的 Instagram 影响者发现工具后端 API，支持用户认证、集合管理、影响者数据获取等完整功能。

## 🚀 技术栈

- **Web 框架**: Hono - 极简、高性能的 Web 框架
- **数据库**: Cloudflare D1 - 基于 SQLite 的边缘数据库
- **ORM**: Drizzle ORM - TypeScript 优先的 ORM
- **认证**: Supabase Auth - 完整的用户认证解决方案
- **数据验证**: Zod - TypeScript 优先的数据验证库
- **外部 API**: RapidAPI Instagram Looter - Instagram 数据获取
- **部署**: Cloudflare Workers - 边缘计算平台

## 📁 项目结构

```
src/
├── db/
│   ├── schema.ts              # 数据库表结构 (用户、集合、用户行为)
│   └── migrations/            # 数据库迁移文件
├── routes/
│   └── api/
│       ├── auth.ts           # 用户认证 API (登录/注册)
│       ├── collections.ts    # 集合管理 API (CRUD、收藏、导出)
│       └── instagram.ts      # Instagram 数据 API (相似账号发现)
├── services/
│   ├── auth.service.ts       # 认证服务 (Supabase 集成)
│   ├── collection.service.ts # 集合业务逻辑 (数据管理、去重)
│   └── instagram.service.ts  # Instagram API 服务 (RapidAPI 集成)
├── middleware/
│   ├── auth.middleware.ts    # JWT 认证中间件
│   └── cors.middleware.ts    # CORS 跨域处理
├── utils/
│   ├── response.ts           # 统一 API 响应格式
│   ├── validation.ts         # Zod 数据验证工具
│   ├── errors.ts            # 自定义错误类型
│   └── id.ts                # ID 生成工具
└── index.ts                 # Hono 应用入口
```

## 🛠️ 开发环境设置

### 1. 安装依赖

```bash
pnpm install
```

### 2. 环境配置

⚠️ **安全提醒**: `wrangler.toml` 文件包含敏感信息，已被添加到 `.gitignore` 中。

复制配置模板文件：
```bash
cp wrangler.toml.example wrangler.toml
```

编辑 `wrangler.toml` 文件，填入您的真实配置信息。

复制环境配置文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入必要的配置：

```env
NODE_ENV=development
CORS_ORIGIN=*
RAPIDAPI_KEY=your_rapidapi_key_here
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. 数据库设置

生成数据库迁移：

```bash
pnpm db:generate
```

运行本地数据库迁移：

```bash
pnpm db:migrate
```

### 4. 启动开发服务器

```bash
pnpm dev
```

服务器将在 `http://localhost:8787` 启动。

## 🔌 API 端点

### 🔐 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/signup` - 用户注册
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### 📁 集合管理
- `GET /api/collections` - 获取用户的所有集合
- `POST /api/collections` - 创建或更新集合
- `GET /api/collections/:id` - 获取指定集合详情
- `DELETE /api/collections/:id` - 删除集合
- `GET /api/collections/:id/unviewed` - 获取集合中未查看的影响者
- `GET /api/collections/:id/export` - 导出集合数据

### 💖 收藏功能
- `GET /api/collections/:id/favorites` - 获取集合中的收藏列表
- `POST /api/collections/:id/favorites` - 添加收藏
- `DELETE /api/collections/:id/favorites/:username` - 取消收藏

### 📊 用户行为记录
- `POST /api/collections/:id/actions` - 记录用户行为 (查看/收藏)

### 📱 Instagram 数据
- `GET /api/instagram/influencers/:username` - 获取相似影响者
  - 查询参数: `collectionId` (可选) - 添加到指定集合

### 🏥 系统状态
- `GET /health` - 服务器健康检查

## 🚀 部署到 Cloudflare Workers

### 1. 配置 Cloudflare

确保你已经：
- 注册了 Cloudflare 账户
- 安装了 Wrangler CLI: `npm install -g wrangler`
- 登录 Wrangler: `wrangler login`

### 2. 创建 D1 数据库

```bash
wrangler d1 create ins-kol-finder-db
```

更新 `wrangler.toml` 中的数据库 ID。

### 3. 配置环境变量

在 Cloudflare Workers 控制台中设置环境变量：

```bash
wrangler secret put RAPIDAPI_KEY
wrangler secret put SUPABASE_URL
wrangler secret put SUPABASE_ANON_KEY
wrangler secret put SUPABASE_SERVICE_ROLE_KEY
```

### 4. 运行生产环境数据库迁移

```bash
pnpm db:migrate:prod
```

### 5. 部署

```bash
pnpm deploy
```

部署成功后，API 将在 `https://your-worker.your-subdomain.workers.dev` 可用。

## 🗄️ 数据库结构

### 表结构

#### `users` - 用户表
- `id` - 用户唯一标识
- `email` - 用户邮箱
- `name` - 用户姓名
- `created_at` - 创建时间
- `updated_at` - 更新时间

#### `collections` - 集合表
- `id` - 集合唯一标识
- `user_id` - 所属用户ID
- `name` - 集合名称
- `platform` - 平台类型 (instagram)
- `result` - 影响者数据 (JSON)
- `created_at` - 创建时间
- `updated_at` - 更新时间

#### `user_actions` - 用户行为表
- `id` - 行为记录ID
- `user_id` - 用户ID
- `collection_id` - 集合ID
- `influencer_username` - 影响者用户名
- `influencer_id` - 影响者ID
- `action_type` - 行为类型 (viewed/favorited)
- `created_at` - 行为时间

## 🔧 环境变量

### 必需变量
- `RAPIDAPI_KEY` - RapidAPI 密钥（Instagram 数据获取）
- `SUPABASE_URL` - Supabase 项目 URL
- `SUPABASE_ANON_KEY` - Supabase 匿名密钥
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase 服务角色密钥

### 可选变量
- `NODE_ENV` - 环境模式 (development/production)
- `CORS_ORIGIN` - CORS 允许的源 (默认: *)

## 🔍 功能特性

### ✅ 已实现功能
- 🔐 **用户认证系统** - 基于 Supabase Auth 的完整认证
- 📁 **集合管理** - 创建、查看、删除影响者集合
- 💖 **收藏功能** - 收藏/取消收藏影响者
- 📊 **行为追踪** - 记录用户查看和收藏行为
- 🔄 **数据去重** - 智能去重避免重复数据
- 📤 **数据导出** - 支持 CSV 格式导出
- 🎯 **相似推荐** - 基于当前影响者发现相似账号
- 🚀 **高性能** - 边缘计算部署，全球低延迟

### 🎨 技术亮点
- **TypeScript 全栈** - 类型安全的开发体验
- **边缘计算** - Cloudflare Workers 全球部署
- **现代 ORM** - Drizzle ORM 提供类型安全的数据库操作
- **数据验证** - Zod 确保 API 数据完整性
- **统一响应** - 标准化的 API 响应格式
- **错误处理** - 完善的错误处理和日志记录

## 📝 注意事项

1. **API 密钥安全** - 确保 RapidAPI 密钥和 Supabase 密钥安全存储
2. **CORS 配置** - 生产环境建议限制 CORS 源
3. **数据库限制** - D1 数据库有一定的查询限制，注意优化查询
4. **扩展配置** - 部署后需要更新 Chrome 扩展中的 API 地址
5. **监控日志** - 建议配置 Cloudflare Workers 的日志监控

## 🤝 开发贡献

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 提交 Pull Request
