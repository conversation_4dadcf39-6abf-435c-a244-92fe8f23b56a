#!/bin/bash

# Deployment script for Cloudflare Workers
set -e

echo "🚀 Starting deployment to Cloudflare Workers..."

# Build the project
echo "📦 Building project..."
pnpm build

# Run database migrations on production
echo "🗄️ Running database migrations..."
pnpm db:migrate:prod

# Deploy to Cloudflare Workers
echo "☁️ Deploying to Cloudflare Workers..."
pnpm start

echo "✅ Deployment completed successfully!"
echo "🌐 Your API is now available at: https://my-app-backend.your-subdomain.workers.dev"
