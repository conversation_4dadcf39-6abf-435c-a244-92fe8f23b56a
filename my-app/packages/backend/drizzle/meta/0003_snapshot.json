{"version": "6", "dialect": "sqlite", "id": "4a742f0a-f4ae-4341-be7f-0d0478cff15f", "prevId": "01d25231-1fe8-4d2b-b3c0-e93b4bd9e5fa", "tables": {"collections": {"name": "collections", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'instagram'"}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"collections_user_id_users_id_fk": {"name": "collections_user_id_users_id_fk", "tableFrom": "collections", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "favorites": {"name": "favorites", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "collection_id": {"name": "collection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "influencer_username": {"name": "influencer_username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform_user_id": {"name": "platform_user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'instagram'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"favorites_user_id_users_id_fk": {"name": "favorites_user_id_users_id_fk", "tableFrom": "favorites", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "favorites_collection_id_collections_id_fk": {"name": "favorites_collection_id_collections_id_fk", "tableFrom": "favorites", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_actions": {"name": "user_actions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "collection_id": {"name": "collection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "influencer_username": {"name": "influencer_username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'instagram'"}, "platform_user_id": {"name": "platform_user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action_type": {"name": "action_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"user_actions_user_id_users_id_fk": {"name": "user_actions_user_id_users_id_fk", "tableFrom": "user_actions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_actions_collection_id_collections_id_fk": {"name": "user_actions_collection_id_collections_id_fk", "tableFrom": "user_actions", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_admin": {"name": "is_admin", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "total_quota": {"name": "total_quota", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 5}, "used_quota": {"name": "used_quota", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}