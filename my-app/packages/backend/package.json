{"name": "@my-app/backend", "version": "1.0.0", "description": "<PERSON><PERSON> backend for Chrome Extension on Cloudflare Workers", "main": "dist/index.js", "scripts": {"dev": "wrangler dev --port 8787", "build": "esbuild src/index.ts --bundle --format=esm --outfile=dist/index.js --platform=neutral --target=es2022 --tsconfig=tsconfig.json --external:@supabase/supabase-js", "start": "wrangler deploy", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "test": "vitest", "db:generate": "drizzle-kit generate", "db:migrate": "wrangler d1 migrations apply my-app-db-local --local", "db:migrate:prod": "wrangler d1 migrations apply my-app-db-local", "db:studio": "drizzle-kit studio", "quota:list": "node scripts/quota-manager.js list", "quota:show": "node scripts/quota-manager.js show", "quota:set": "node scripts/quota-manager.js set", "quota:reset": "node scripts/quota-manager.js reset", "quota:add": "node scripts/quota-manager.js add", "quota:admin": "node scripts/quota-manager.js admin", "quota:help": "node scripts/quota-manager.js help"}, "dependencies": {"@hono/zod-validator": "^0.7.2", "@my-app/shared": "workspace:*", "@supabase/supabase-js": "^2.52.0", "drizzle-orm": "^0.44.3", "drizzle-zod": "^0.8.2", "hono": "^3.12.0", "xlsx": "^0.18.5", "zod": "^3.22.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20231218.0", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "drizzle-kit": "^0.31.4", "esbuild": "^0.25.7", "eslint": "^8.56.0", "typescript": "^5.3.3", "vitest": "^1.1.0", "wrangler": "^4.25.0"}}