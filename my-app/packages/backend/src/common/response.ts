import { Context } from 'hono';
import { Response, ResponseCode } from './types';

/**
 * 创建响应对象
 */
export function createResponse<T>(
  code: number,
  message: string,
  data?: T,
  error?: any
): Response<T> {
  return {
    code,
    message,
    data,
    error,
  };
}

/**
 * 成功响应工具函数
 */
export function success<T>(
  c: Context,
  data?: T,
  message: string = '操作成功'
): globalThis.Response {
  const response: Response<T> = {
    code: ResponseCode.SUCCESS,
    message,
    data,
  };
  
  return c.json(response, 200);
}

/**
 * 失败响应工具函数
 */
export function error(
  c: Context,
  code: number = ResponseCode.SERVER_ERROR,
  message: string = '操作失败',
  error?: any,
  httpStatus: number = 500
): globalThis.Response {
  const response: Response = {
    code,
    message,
    error,
  };
  
  return c.json(response, httpStatus);
}

/**
 * 参数错误响应
 */
export function paramError(
  c: Context,
  message: string = '参数错误',
  error?: any
): globalThis.Response {
  return c.json({
    code: ResponseCode.PARAM_ERROR,
    message,
    error,
  }, 400);
}

/**
 * 服务器错误响应
 */
export function serverError(
  c: Context,
  message: string = '服务器内部错误',
  error?: any
): globalThis.Response {
  return c.json({
    code: ResponseCode.SERVER_ERROR,
    message,
    error,
  }, 500);
}
