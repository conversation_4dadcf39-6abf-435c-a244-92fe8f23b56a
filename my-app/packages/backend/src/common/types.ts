/**
 * 统一响应格式接口
 */
export interface Response<T = any> {
  code: number;           // 自定义业务状态码
  message: string;        // 提示信息
  error?: any;            // 错误信息
  data?: T;              // 返回数据
}

/**
 * 业务状态码常量
 */
export const ResponseCode = {
  SUCCESS: 1000,          // 成功
  PARAM_ERROR: 1400,      // 参数错误
  QUOTA_EXCEEDED: 1403,   // 配额不足
  NOT_FOUND: 1404,        // 资源未找到
  SERVER_ERROR: 1500,     // 服务器错误
} as const;

/**
 * 错误类型枚举
 */
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',        // 验证错误
  DATABASE_ERROR = 'DATABASE_ERROR',            // 数据库错误
  THIRD_PARTY_API_ERROR = 'THIRD_PARTY_API_ERROR', // 第三方API错误
  BUSINESS_ERROR = 'BUSINESS_ERROR',            // 业务逻辑错误
  SYSTEM_ERROR = 'SYSTEM_ERROR',                // 系统错误
}

/**
 * 自定义错误类
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly code: number;
  public readonly httpStatus: number;

  constructor(
    message: string,
    type: ErrorType = ErrorType.SYSTEM_ERROR,
    code: number = ResponseCode.SERVER_ERROR,
    httpStatus: number = 500
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.httpStatus = httpStatus;
  }
}

/**
 * 预定义错误类
 */
export class ValidationError extends AppError {
  constructor(message: string = '参数验证失败') {
    super(message, ErrorType.VALIDATION_ERROR, ResponseCode.PARAM_ERROR, 400);
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = '数据库操作失败') {
    super(message, ErrorType.DATABASE_ERROR, ResponseCode.SERVER_ERROR, 500);
  }
}

export class ThirdPartyApiError extends AppError {
  constructor(message: string = '第三方API调用失败') {
    super(message, ErrorType.THIRD_PARTY_API_ERROR, ResponseCode.SERVER_ERROR, 500);
  }
}

export class BusinessError extends AppError {
  constructor(message: string, code: number = ResponseCode.PARAM_ERROR) {
    super(message, ErrorType.BUSINESS_ERROR, code, 400);
  }
}
