import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { z } from 'zod';

// Users table
export const users = sqliteTable('users', {
  id: text('id').primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name'),
  isAdmin: integer('is_admin', { mode: 'boolean' }).notNull().default(false),
  totalQuota: integer('total_quota').notNull().default(5), // 总配额，默认5个
  usedQuota: integer('used_quota').notNull().default(0),   // 已使用配额，默认0个
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
});

// Collections table - 博主集合
export const collections = sqliteTable('collections', {
  id: text('id').primaryKey(),
  userId: text('user_id').notNull().references(() => users.id),
  name: text('name').notNull(), // 集合名称（首个博主用户名）
  platform: text('platform').notNull().default('instagram'), // 平台
  result: text('result', { mode: 'json' }).notNull(), // JSON字段存储博主信息
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
});

// User actions table - 用户行为记录
export const userActions = sqliteTable('user_actions', {
  id: text('id').primaryKey(),
  userId: text('user_id').notNull().references(() => users.id),
  collectionId: text('collection_id').notNull().references(() => collections.id),
  influencerUsername: text('influencer_username').notNull(),
  platform: text('platform').notNull().default('instagram'),
  platformUserId: text('platform_user_id').notNull(),
  actionType: text('action_type').notNull(), // 'viewed', 'skipped' 等
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
});

// Favorites table - 收藏表
export const favorites = sqliteTable('favorites', {
  id: text('id').primaryKey(),
  userId: text('user_id').notNull().references(() => users.id),
  collectionId: text('collection_id').notNull().references(() => collections.id),
  influencerUsername: text('influencer_username').notNull(),
  platformUserId: text('platform_user_id').notNull(),
  platform: text('platform').notNull().default('instagram'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().$defaultFn(() => new Date()),
});

// Basic validation schemas
export const apiRequestSchema = z.object({
  action: z.enum(['get', 'save', 'update', 'delete']),
  data: z.any().optional(),
});

export const instagramUsernameSchema = z.object({
  username: z.string().min(1).max(30).regex(/^[a-zA-Z0-9._]+$/, 'Invalid username format'),
});

// Collection validation schemas
export const createCollectionSchema = z.object({
  name: z.string().min(1).max(100),
  platform: z.string().default('instagram'),
  result: z.object({
    influencers: z.array(z.object({
      id: z.string(),
      username: z.string(),
      full_name: z.string().optional(),
      profile_pic_url: z.string().optional(),
      is_verified: z.boolean().default(false),
      is_private: z.boolean().default(false),
    }))
  })
});

export const userActionSchema = z.object({
  collectionId: z.string(),
  influencerUsername: z.string(),
  platformUserId: z.string(),
  actionType: z.enum(['viewed', 'skipped']),
  platform: z.string().default('instagram'),
});

export const favoriteSchema = z.object({
  collectionId: z.string(),
  influencerUsername: z.string(),
  platformUserId: z.string(),
  platform: z.string().default('instagram'),
});
