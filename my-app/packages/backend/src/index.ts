import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { createDB, type DB } from '@/db';
import { globalErrorHandler } from '@/middleware/errorHandler';
import { success } from '@/common';
import { registerRoutes } from './routes';
import { verificationPageTemplate } from './templates/verification';


// Define the environment bindings
export interface Env {
  DB: any;
  NODE_ENV?: string;
  CORS_ORIGIN?: string;
  RAPIDAPI_KEY?: string;
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  SUPABASE_JWT_SECRET: string;
  [key: string]: any;
}

// Extend Hono context with database
type Variables = {
  db: DB;
};

export const app = new Hono<{ Bindings: Env; Variables: Variables }>();

// 处理根路径的 Supabase 验证重定向（在所有中间件之前）
app.get('/', (c) => {
  return c.html(verificationPageTemplate);
});

// Middleware
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// Add database to context
app.use('*', async (c, next) => {
  try {
    if (c.env.DB) {
      c.set('db', createDB(c.env.DB));
    }
  } catch (error) {
    console.error('Database connection error:', error);
  }
  await next();
});

// 全局错误处理器
app.onError(globalErrorHandler);

// Register routes
registerRoutes(app);

// Health check
app.get('/health', (c) => {
  return success(c, {
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: c.env.NODE_ENV || 'development',
    database: c.get('db') ? 'connected' : 'not connected',
  }, '服务健康检查');
});

// 404 handler
app.notFound((c) => {
  return c.json({
    code: 1400,
    message: `路由 ${c.req.method} ${c.req.path} 未找到`,
    error: {
      type: 'NOT_FOUND',
      path: c.req.path,
    },
  }, 404);
});

export default app;