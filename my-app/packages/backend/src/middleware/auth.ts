import { Context, Next } from 'hono';
import { createSupabaseAdmin } from '../services/supabase';
import { ResponseCode, ErrorType  } from '../common/types';
import { createResponse } from '@/common';
import { users } from '../db/schema';
import { eq } from 'drizzle-orm';

interface SupabaseJwt {
  sub: string;
  email?: string;
  session_id?: string;
  exp: number;
  iat: number;
  role?: string;
  aal?: string;
  amr?: Array<{ method: string; timestamp: number }>;
}

export interface AuthenticatedContext extends Context {
  user?: {
    id: string;
    email?: string;
    isAdmin: boolean;
  };
}

/**
 * 认证中间件
 */
export async function authMiddleware(c: Context, next: Next) {
  try {
    const authorization = c.req.header('Authorization');

    if (!authorization) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '需要登录访问', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Authorization header is required',
        }),
        401
      );
    }

    const [type, token] = authorization.split(' ');

    if (type !== 'Bearer' || !token) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '无效的认证格式', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Invalid authorization format',
        }),
        401
      );
    }

    // 验证 JWT token
    let decoded: SupabaseJwt | null = null;
    try {
      // 使用 Web Crypto API 验证 JWT
      const jwtSecret = c.env.SUPABASE_JWT_SECRET;
      const encoder = new TextEncoder();
      const key = await crypto.subtle.importKey(
        'raw',
        encoder.encode(jwtSecret),
        { name: 'HMAC', hash: 'SHA-256' },
        false,
        ['verify']
      );

      const [header, payload, signature] = token.split('.');
      const data = `${header}.${payload}`;
      const signatureBytes = Uint8Array.from(atob(signature.replace(/-/g, '+').replace(/_/g, '/')), c => c.charCodeAt(0));
      
      const isValid = await crypto.subtle.verify(
        'HMAC',
        key,
        signatureBytes,
        encoder.encode(data)
      );

      if (!isValid) {
        throw new Error('Invalid signature');
      }

      decoded = JSON.parse(atob(payload)) as SupabaseJwt;
    } catch (error) {
      console.error('JWT verification error:', error);
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '无效或过期的令牌', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Invalid or expired token',
        }),
        401
      );
    }

    if (!decoded || !decoded.sub) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '无效的令牌：缺少用户ID', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Invalid token: no user ID',
        }),
        401
      );
    }

    // 从 Supabase 获取用户信息
    const supabaseAdmin = createSupabaseAdmin(c.env as any);
    const { user: supabaseUser } = await supabaseAdmin.getUser(token);

    if (!supabaseUser) {
      console.error('Failed to get user from Supabase');
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '用户未找到', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'User not found',
        }),
        401
      );
    }

    // 检查用户是否存在于我们的数据库中
    const db = c.get('db');
    let dbUser = await db
      .select()
      .from(users)
      .where(eq(users.id, supabaseUser.id))
      .get();

    // 如果用户不存在，创建用户
    if (!dbUser) {
      await db.insert(users).values({
        id: supabaseUser.id,
        email: supabaseUser.email || '',
        name: supabaseUser.user_metadata?.name || null,
        isAdmin: false,
      });

      dbUser = await db
        .select()
        .from(users)
        .where(eq(users.id, supabaseUser.id))
        .get();
    }

    // 将用户信息附加到上下文
    c.set('user', {
      id: dbUser.id,
      email: dbUser.email,
      isAdmin: !!dbUser.isAdmin,
    });

    // 同时设置 userId 以便于在路由中使用
    c.set('userId', dbUser.id);

    await next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return c.json(
      createResponse(ResponseCode.SERVER_ERROR, '服务器内部错误', null, {
        type: ErrorType.SYSTEM_ERROR,
        details: 'Internal server error',
      }),
      500
    );
  }
}
