import { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';
import {
  AppError,
  ValidationError,
  DatabaseError,
  ThirdPartyApiError,
  ErrorType,
  ResponseCode,
  error
} from '@/common';

/**
 * 全局错误处理器
 */
export function globalErrorHandler(err: Error, c: Context): globalThis.Response {
  console.error('Global error handler caught:', {
    name: err.name,
    message: err.message,
    stack: err.stack,
    path: c.req.path,
    method: c.req.method,
  });

  // 处理 Hono 的 HTTPException
  if (err instanceof HTTPException) {
    return error(
      c,
      ResponseCode.PARAM_ERROR,
      err.message || '请求错误',
      { type: 'HTTP_EXCEPTION' },
      err.status
    );
  }

  // 处理自定义应用错误
  if (err instanceof AppError) {
    return error(
      c,
      err.code,
      err.message,
      { type: err.type },
      err.httpStatus
    );
  }

  // 处理验证错误
  if (err instanceof ValidationError) {
    return error(
      c,
      ResponseCode.PARAM_ERROR,
      err.message,
      { type: ErrorType.VALIDATION_ERROR },
      400
    );
  }

  // 处理数据库错误
  if (err instanceof DatabaseError) {
    return error(
      c,
      ResponseCode.SERVER_ERROR,
      '数据库操作失败',
      { type: ErrorType.DATABASE_ERROR },
      500
    );
  }

  // 处理第三方API错误
  if (err instanceof ThirdPartyApiError) {
    return error(
      c,
      ResponseCode.SERVER_ERROR,
      '外部服务调用失败',
      { type: ErrorType.THIRD_PARTY_API_ERROR },
      500
    );
  }

  // 处理 Zod 验证错误
  if (err.name === 'ZodError') {
    return error(
      c,
      ResponseCode.PARAM_ERROR,
      '参数验证失败',
      { 
        type: ErrorType.VALIDATION_ERROR,
        details: err.message 
      },
      400
    );
  }

  // 处理数据库相关错误（通过错误消息判断）
  if (err.message.includes('database') || 
      err.message.includes('SQL') || 
      err.message.includes('SQLITE')) {
    return error(
      c,
      ResponseCode.SERVER_ERROR,
      '数据库操作失败',
      { type: ErrorType.DATABASE_ERROR },
      500
    );
  }

  // 处理网络请求错误
  if (err.message.includes('fetch') || 
      err.message.includes('network') ||
      err.message.includes('timeout')) {
    return error(
      c,
      ResponseCode.SERVER_ERROR,
      '网络请求失败',
      { type: ErrorType.THIRD_PARTY_API_ERROR },
      500
    );
  }

  // 默认系统错误
  return error(
    c,
    ResponseCode.SERVER_ERROR,
    '系统内部错误',
    { 
      type: ErrorType.SYSTEM_ERROR,
      originalMessage: err.message 
    },
    500
  );
}
