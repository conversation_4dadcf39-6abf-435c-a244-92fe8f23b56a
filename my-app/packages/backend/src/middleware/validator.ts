import { Context, Next } from 'hono';
import { z } from 'zod';
import { paramError } from '@/common';

/**
 * 自定义验证中间件，使用统一的响应格式
 */
export function customValidator<T extends z.ZodSchema>(
  target: 'json' | 'query' | 'param' | 'header',
  schema: T
) {
  return async (c: Context, next: Next): Promise<Response | void> => {
    let data: any;

    try {
      switch (target) {
        case 'json':
          data = await c.req.json();
          break;
        case 'query':
          data = c.req.query();
          break;
        case 'param':
          data = c.req.param();
          break;
        case 'header':
          data = c.req.header();
          break;
        default:
          throw new Error(`Unsupported validation target: ${target}`);
      }

      const result = schema.safeParse(data);

      if (!result.success) {
        const errorDetails = result.error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
          received: issue.code === 'invalid_type' ? (issue as any).received : undefined,
        }));

        return paramError(c, '参数验证失败', {
          type: 'VALIDATION_ERROR',
          details: errorDetails,
        });
      }

      // 将验证后的数据存储到上下文中
      c.set('validatedData', result.data);

      await next();
    } catch (error) {
      console.error('Validation middleware error:', error);
      return paramError(c, '参数解析失败', {
        type: 'PARSE_ERROR',
        originalError: error instanceof Error ? error.message : String(error),
      });
    }
  };
}

/**
 * 获取验证后的数据
 */
export function getValidatedData<T>(c: Context): T {
  return c.get('validatedData') as T;
}
