import { Hono } from 'hono';
import { CollectionService } from '@/services/collection.service';
import { success, error, ThirdPartyApiError, ResponseCode } from '@/common';
import { customValidator, getValidatedData } from '@/middleware/validator';
import { authMiddleware } from '@/middleware/auth';
import { createCollectionSchema, userActionSchema, favoriteSchema } from '@/db/schema';
import { z } from 'zod';
import type { DB } from '@/db';

// 定义环境变量类型
interface Env {
  DB?: any;
  NODE_ENV?: string;
  CORS_ORIGIN?: string;
  RAPIDAPI_KEY?: string;
  [key: string]: any;
}

type Variables = {
  db: DB;
  userId: string;
};

export const collectionsRoutes = new Hono<{ Bindings: Env; Variables: Variables }>();

// 获取用户的所有集合
collectionsRoutes.get('/',
  authMiddleware,
  async (c) => {
    try {
      const userId = c.get('userId');
      const collectionService = new CollectionService(c.get('db'));
      
      const collections = await collectionService.getUserCollections(userId);
      
      return success(c, {
        collections,
        meta: {
          total: collections.length
        }
      }, '集合列表获取成功');
    } catch (err) {
      console.error('Error fetching collections:', err);
      throw new ThirdPartyApiError('获取集合列表失败');
    }
  }
);

// 获取特定集合详情
collectionsRoutes.get('/:collectionId',
  authMiddleware,
  async (c) => {
    try {
      const userId = c.get('userId');
      const collectionId = c.req.param('collectionId');
      const collectionService = new CollectionService(c.get('db'));
      
      const collection = await collectionService.getCollection(collectionId, userId);
      
      if (!collection) {
        return error(c, ResponseCode.PARAM_ERROR, '集合不存在', null, 404);
      }
      
      return success(c, { collection }, '集合详情获取成功');
    } catch (err) {
      console.error('Error fetching collection:', err);
      throw new ThirdPartyApiError('获取集合详情失败');
    }
  }
);

// 获取集合中未查看的博主
collectionsRoutes.get('/:collectionId/unviewed',
  authMiddleware,
  async (c) => {
    try {
      const userId = c.get('userId');
      const collectionId = c.req.param('collectionId');
      const collectionService = new CollectionService(c.get('db'));
      
      const unviewedInfluencers = await collectionService.getUnviewedInfluencers(userId, collectionId);
      
      return success(c, {
        influencers: unviewedInfluencers,
        meta: {
          total: unviewedInfluencers.length,
          collectionId
        }
      }, '未查看博主列表获取成功');
    } catch (err) {
      console.error('Error fetching unviewed influencers:', err);
      throw new ThirdPartyApiError('获取未查看博主列表失败');
    }
  }
);

// 创建或更新集合
collectionsRoutes.post('/',
  authMiddleware,
  customValidator('json', createCollectionSchema),
  async (c) => {
    try {
      const userId = c.get('userId');
      const { name, platform, result } = getValidatedData<{
        name: string;
        platform: string;
        result: { influencers: any[] };
      }>(c);
      
      const collectionService = new CollectionService(c.get('db'));
      
      const collection = await collectionService.createOrGetCollection(
        userId,
        name,
        platform,
        result.influencers
      );
      
      return success(c, { collection }, '集合创建/更新成功');
    } catch (err) {
      console.error('Error creating/updating collection:', err);
      throw new ThirdPartyApiError('集合创建/更新失败');
    }
  }
);

// 记录用户行为（查看博主）
collectionsRoutes.post('/actions',
  authMiddleware,
  customValidator('json', userActionSchema),
  async (c) => {
    try {
      const userId = c.get('userId');
      const { collectionId, influencerUsername, platformUserId, actionType, platform } = 
        getValidatedData<{
          collectionId: string;
          influencerUsername: string;
          platformUserId: string;
          actionType: 'viewed' | 'skipped';
          platform: string;
        }>(c);
      
      const collectionService = new CollectionService(c.get('db'));
      
      await collectionService.recordUserAction(
        userId,
        collectionId,
        influencerUsername,
        platformUserId,
        actionType,
        platform
      );
      
      return success(c, null, '用户行为记录成功');
    } catch (err) {
      console.error('Error recording user action:', err);
      throw new ThirdPartyApiError('用户行为记录失败');
    }
  }
);

// 添加到收藏
collectionsRoutes.post('/favorites',
  authMiddleware,
  customValidator('json', favoriteSchema),
  async (c) => {
    try {
      const userId = c.get('userId');
      const { collectionId, influencerUsername, platformUserId, platform } = 
        getValidatedData<{
          collectionId: string;
          influencerUsername: string;
          platformUserId: string;
          platform: string;
        }>(c);
      
      const collectionService = new CollectionService(c.get('db'));
      
      await collectionService.addToFavorites(
        userId,
        collectionId,
        influencerUsername,
        platformUserId,
        platform
      );
      
      return success(c, null, '添加收藏成功');
    } catch (err) {
      console.error('Error adding to favorites:', err);
      throw new ThirdPartyApiError('添加收藏失败');
    }
  }
);

// 移除收藏
collectionsRoutes.delete('/favorites',
  authMiddleware,
  customValidator('json', z.object({
    collectionId: z.string(),
    influencerUsername: z.string()
  })),
  async (c) => {
    try {
      const userId = c.get('userId');
      const { collectionId, influencerUsername } = getValidatedData<{
        collectionId: string;
        influencerUsername: string;
      }>(c);
      
      const collectionService = new CollectionService(c.get('db'));
      
      await collectionService.removeFromFavorites(userId, collectionId, influencerUsername);
      
      return success(c, null, '移除收藏成功');
    } catch (err) {
      console.error('Error removing from favorites:', err);
      throw new ThirdPartyApiError('移除收藏失败');
    }
  }
);

// 获取收藏列表
collectionsRoutes.get('/:collectionId/favorites',
  authMiddleware,
  async (c) => {
    try {
      const userId = c.get('userId');
      const collectionId = c.req.param('collectionId');
      const collectionService = new CollectionService(c.get('db'));
      
      const favorites = await collectionService.getFavorites(userId, collectionId);
      
      return success(c, {
        favorites,
        meta: {
          total: favorites.length,
          collectionId
        }
      }, '收藏列表获取成功');
    } catch (err) {
      console.error('Error fetching favorites:', err);
      throw new ThirdPartyApiError('获取收藏列表失败');
    }
  }
);

// 导出全部数据（包含全部数据和收藏列表两个sheet）
collectionsRoutes.get('/:collectionId/export-excel',
  authMiddleware,
  async (c) => {
    try {
      const userId = c.get('userId');
      const collectionId = c.req.param('collectionId');
      const collectionService = new CollectionService(c.get('db'));

      // 并发获取集合详情和收藏列表数据
      const [collection, favoriteUsernames] = await Promise.all([
        collectionService.getCollection(collectionId, userId),
        collectionService.getFavorites(userId, collectionId)
      ]);

      if (!collection) {
        throw new ThirdPartyApiError('集合不存在');
      }

      // 第一个sheet：全部数据
      const allData = collection.result.influencers.map(influencer => ({
        fullname: influencer.full_name || influencer.username,
        url: `https://www.instagram.com/${influencer.username}/`
      }));

      // 第二个sheet：收藏列表数据
      const favoritesData = favoriteUsernames.map(username => {
        const influencer = collection.result.influencers.find(inf => inf.username === username);
        return {
          fullname: influencer?.full_name || username,
          url: `https://www.instagram.com/${username}/`
        };
      });

      return success(c, {
        sheets: {
          '全部数据': allData,
          '收藏列表': favoritesData
        },
        meta: {
          totalAll: allData.length,
          totalFavorites: favoritesData.length,
          collectionId
        }
      }, '导出数据获取成功');

    } catch (err) {
      console.error('Error exporting data:', err);
      throw new ThirdPartyApiError('导出数据获取失败');
    }
  }
);
