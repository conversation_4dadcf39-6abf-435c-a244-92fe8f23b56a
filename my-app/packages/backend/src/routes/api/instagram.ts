import { Hono } from 'hono';
import { InstagramService } from '@/services/instagram.service';
import { CollectionService } from '@/services/collection.service';
import { instagramUsernameSchema, type DB, users } from '@/db';
import { success, ThirdPartyApiError, error, ResponseCode } from '@/common';
import { customValidator, getValidatedData } from '@/middleware/validator';
import { authMiddleware } from '../../middleware/auth';
import { eq } from 'drizzle-orm';

// 定义环境变量类型
interface Env {
  DB?: any;
  NODE_ENV?: string;
  CORS_ORIGIN?: string;
  RAPIDAPI_KEY?: string;
  [key: string]: any;
}

type Variables = {
  db: DB;
  userId: string;
};

export const instagramRoutes = new Hono<{ Bindings: Env; Variables: Variables }>();

// Get related influencers and create/update collection
instagramRoutes.get('/influencers/:username',
  authMiddleware,
  customValidator('param', instagramUsernameSchema),
  async (c) => {
    const { username } = getValidatedData<{ username: string }>(c);
    const userId = c.get('userId');
    const collectionId = c.req.query('collectionId'); // 可选的集合ID
    const db = c.get('db');

    try {
      // 1. 检查用户配额
      const user = await db
        .select()
        .from(users)
        .where(eq(users.id, userId))
        .get();

      if (!user) {
        return error(c, ResponseCode.NOT_FOUND, '用户不存在', null, 404);
      }

      const remainingQuota = (user.totalQuota || 5) - (user.usedQuota || 0);
      if (remainingQuota <= 0) {
        return error(c, ResponseCode.QUOTA_EXCEEDED, '配额已用完，请联系管理员升级套餐', {
          totalQuota: user.totalQuota || 5,
          usedQuota: user.usedQuota || 0,
          remainingQuota: 0
        }, 403);
      }

      // 2. 调用 Instagram API
      const instagramService = new InstagramService(c.env?.RAPIDAPI_KEY as string);
      const collectionService = new CollectionService(c.get('db'));

      const influencers = await instagramService.getRelatedInfluencers(username);

      let collection;
      if (collectionId) {
        // 如果传入集合ID，添加到现有集合
        const existingCollection = await collectionService.getCollection(collectionId, userId);
        if (existingCollection) {
          // 合并新博主到现有集合
          const existingInfluencers = existingCollection.result.influencers;
          const allInfluencers = [...existingInfluencers, ...influencers]
            .filter((influencer, index, self) =>
              index === self.findIndex(i => i.username === influencer.username)
            ); // 去重

          collection = await collectionService.updateCollectionInfluencers(collectionId, allInfluencers);
        } else {
          throw new Error('集合不存在');
        }
      } else {
        // 首次搜索，创建新集合
        collection = await collectionService.createOrGetCollection(
          userId,
          username, // 集合名称使用博主用户名
          'instagram',
          influencers
        );
      }

      // 获取未查看的博主
      const unviewedInfluencers = await collectionService.getUnviewedInfluencers(userId, collection.id);

      // 3. 成功后扣除配额
      await db
        .update(users)
        .set({
          usedQuota: (user.usedQuota || 0) + 1,
          updatedAt: new Date()
        })
        .where(eq(users.id, userId));

      const newRemainingQuota = remainingQuota - 1;

      return success(c, {
        influencers: unviewedInfluencers,
        collection: {
          id: collection.id,
          name: collection.name,
          platform: collection.platform
        },
        meta: {
          total: unviewedInfluencers.length,
          totalInCollection: collection.result.influencers.length,
          viewedCount: collection.result.influencers.length - unviewedInfluencers.length,
          username,
          collectionId: collection.id
        },
        quota: {
          totalQuota: user.totalQuota || 5,
          usedQuota: (user.usedQuota || 0) + 1,
          remainingQuota: newRemainingQuota
        }
      }, `Instagram 相关影响者获取成功，剩余配额: ${newRemainingQuota}`);
    } catch (error) {
      console.error('Error fetching influencers:', error);
      throw new ThirdPartyApiError('Instagram API 调用失败');
    }
  }
);