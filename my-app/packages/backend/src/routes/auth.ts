import { Hono } from 'hono';
import { createSupabaseClient, createSupabaseAdmin } from '../services/supabase';
import { ResponseCode, ErrorType } from '../common/types';
import { createResponse } from '../common/response';
import { users } from '../db/schema';
import { eq } from 'drizzle-orm';
import type { DB } from '../db';

interface LoginBody {
  email: string;
  password: string;
}

interface SignupBody {
  email: string;
  password: string;
  name?: string;
}

// 定义环境变量类型
interface Env {
  DB?: any;
  NODE_ENV?: string;
  CORS_ORIGIN?: string;
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  SUPABASE_JWT_SECRET: string;
  [key: string]: any;
}

type Variables = {
  db: DB;
};

const auth = new Hono<{ Bindings: Env; Variables: Variables }>();

// 注册路由
auth.post('/signup', async (c) => {
  try {
    const body = await c.req.json<SignupBody>();
    const { email, password, name } = body;

    if (!email || !password) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '邮箱和密码不能为空', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Email and password are required',
        }),
        400
      );
    }

    const supabase = createSupabaseClient(c.env as any);

    try {
      const { user, session } = await supabase.signUp(email, password, { name });

      // 创建用户记录到我们的数据库
      if (user) {
        const db = c.var.db;
        try {
          await db.insert(users).values({
            id: user.id,
            email: user.email!,
            name: name || null,
            isAdmin: false,
            totalQuota: 5,
            usedQuota: 0,
          });
        } catch (dbError) {
          console.error('Database insert error:', dbError);
        }
      }

      if (session) {
        return c.json(
          createResponse(ResponseCode.SUCCESS, '注册成功，已自动登录', {
            user,
            session,
          })
        );
      } else {
        // 如果没有session，说明需要邮箱验证
        return c.json(
          createResponse(ResponseCode.SUCCESS, '注册成功！请检查您的邮箱并点击验证链接，然后重新登录。', {
            user,
            session: null,
          })
        );
      }
    } catch (error: any) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, error.message, null, {
          type: ErrorType.BUSINESS_ERROR,
          details: error.message,
        }),
        400
      );
    }
  } catch (error) {
    console.error('Signup error:', error);
    return c.json(
      createResponse(ResponseCode.SERVER_ERROR, '服务器内部错误', null, {
        type: ErrorType.SYSTEM_ERROR,
        details: 'Internal server error',
      }),
      500
    );
  }
});

// 登录路由
auth.post('/login', async (c) => {
  try {
    const body = await c.req.json<LoginBody>();
    const { email, password } = body;

    if (!email || !password) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '邮箱和密码不能为空', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Email and password are required',
        }),
        400
      );
    }

    const supabase = createSupabaseClient(c.env as any);

    try {
      const { user, session } = await supabase.signInWithPassword(email, password);

      if (user && !session) {
        return c.json(
          createResponse(ResponseCode.PARAM_ERROR, '请先验证您的邮箱地址后再登录。请检查您的邮箱并点击验证链接。', null, {
            type: ErrorType.BUSINESS_ERROR,
            details: 'Email verification required',
          }),
          400
        );
      }

      // 检查是否登录成功
      if (!user || !session) {
        return c.json(
          createResponse(ResponseCode.PARAM_ERROR, '邮箱或密码错误', null, {
            type: ErrorType.BUSINESS_ERROR,
            details: 'Invalid credentials',
          }),
          401
        );
      }

      // 确保用户存在于我们的数据库中
      const db = c.var.db;
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.id, user.id))
        .get();

      if (!existingUser) {
        try {
          await db.insert(users).values({
            id: user.id,
            email: user.email!,
            name: user.user_metadata?.name || null,
            isAdmin: false,
            totalQuota: 5,
            usedQuota: 0,
          });
        } catch (dbError) {
          console.error('Database insert error during login:', dbError);
        }
      }

      return c.json(
        createResponse(ResponseCode.SUCCESS, '登录成功', {
          user,
          session,
        })
      );
    } catch (error: any) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '邮箱或密码错误', null, {
          type: ErrorType.BUSINESS_ERROR,
          details: error.message,
        }),
        401
      );
    }
  } catch (error) {
    return c.json(
      createResponse(ResponseCode.SERVER_ERROR, '服务器内部错误', null, {
        type: ErrorType.SYSTEM_ERROR,
        details: 'Internal server error',
      }),
      500
    );
  }
});

// 登出路由
auth.post('/logout', async (c) => {
  try {
    const authorization = c.req.header('Authorization');

    if (!authorization) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '需要认证头', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Authorization header is required',
        }),
        400
      );
    }

    const [, token] = authorization.split(' ');
    const supabase = createSupabaseClient(c.env as any);

    try {
      await supabase.signOut(token);
      return c.json(createResponse(ResponseCode.SUCCESS, '登出成功', { success: true }));
    } catch (error: any) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, error.message, null, {
          type: ErrorType.BUSINESS_ERROR,
          details: error.message,
        }),
        400
      );
    }
  } catch (error) {
    console.error('Logout error:', error);
    return c.json(
      createResponse(ResponseCode.SERVER_ERROR, '服务器内部错误', null, {
        type: ErrorType.SYSTEM_ERROR,
        details: 'Internal server error',
      }),
      500
    );
  }
});

// 获取当前用户信息
auth.get('/me', async (c) => {
  try {
    const authorization = c.req.header('Authorization');

    if (!authorization) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '需要认证头', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Authorization header is required',
        }),
        401
      );
    }

    const [, token] = authorization.split(' ');

    // 验证 JWT token 并获取用户信息
    const supabaseAdmin = createSupabaseAdmin(c.env as any);
    const { user } = await supabaseAdmin.getUser(token);

    if (!user) {
      return c.json(
        createResponse(ResponseCode.PARAM_ERROR, '无效的令牌', null, {
          type: ErrorType.VALIDATION_ERROR,
          details: 'Invalid token',
        }),
        401
      );
    }

    const db = c.var.db;
    let dbUser = await db
      .select()
      .from(users)
      .where(eq(users.id, user.id))
      .get();

    // 如果用户不存在于数据库中，自动创建用户记录
    if (!dbUser) {
      console.log(`Creating new user record for ${user.email}`);
      await db.insert(users).values({
        id: user.id,
        email: user.email!,
        name: user.user_metadata?.name || null,
        isAdmin: false,
        totalQuota: 5,  // 新用户默认5个配额
        usedQuota: 0,   // 已使用配额为0
      });

      // 重新查询用户信息
      dbUser = await db
        .select()
        .from(users)
        .where(eq(users.id, user.id))
        .get();

      if (!dbUser) {
        return c.json(
          createResponse(ResponseCode.SERVER_ERROR, '创建用户失败', null, {
            type: ErrorType.SYSTEM_ERROR,
            details: 'Failed to create user record',
          }),
          500
        );
      }
    }

    return c.json(
      createResponse(ResponseCode.SUCCESS, '获取用户信息成功', {
        id: dbUser.id,
        email: dbUser.email,
        name: dbUser.name,
        isAdmin: !!dbUser.isAdmin,
        totalQuota: dbUser.totalQuota || 5,
        usedQuota: dbUser.usedQuota || 0,
        remainingQuota: (dbUser.totalQuota || 5) - (dbUser.usedQuota || 0),
      })
    );
  } catch (error) {
    console.error('Get user error:', error);
    return c.json(
      createResponse(ResponseCode.SERVER_ERROR, '服务器内部错误', null, {
        type: ErrorType.SYSTEM_ERROR,
        details: 'Internal server error',
      }),
      500
    );
  }
});

export { auth };
