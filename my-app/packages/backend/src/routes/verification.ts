import { Hono } from 'hono';
import { verificationSuccessTemplate, verificationPageTemplate } from '../templates/verification';

const verification = new Hono();

// 健康检查
verification.get('/health', (c) => {
  return c.json({ status: 'ok', service: 'verification' });
});

// 主验证页面 - 处理 Supabase 重定向
verification.get('/', async (c) => {
  return c.html(verificationPageTemplate);
});

// 邮箱验证成功页面
verification.get('/success', async (c) => {
  return c.html(verificationSuccessTemplate);
});

export default verification;