import { eq, and } from 'drizzle-orm';
import type { DB } from '@/db';
import { collections, userActions, favorites } from '@/db/schema';

// Cloudflare Workers兼容的ID生成函数
function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export interface InfluencerData {
  id: string;
  username: string;
  full_name?: string;
  profile_pic_url?: string;
  is_verified: boolean;
  is_private: boolean;
}

export interface CollectionResult {
  influencers: InfluencerData[];
}

export interface Collection {
  id: string;
  userId: string;
  name: string;
  platform: string;
  result: CollectionResult;
  createdAt: Date;
  updatedAt: Date;
}

export class CollectionService {
  constructor(private db: DB) {}

  // 创建或获取集合
  async createOrGetCollection(
    userId: string,
    name: string,
    platform: string = 'instagram',
    influencers: InfluencerData[]
  ): Promise<Collection> {
    // 先检查是否已存在同名集合
    const existingCollection = await this.db
      .select()
      .from(collections)
      .where(and(
        eq(collections.userId, userId),
        eq(collections.name, name),
        eq(collections.platform, platform)
      ))
      .get();

    if (existingCollection) {
      // 如果存在，更新结果并返回
      const updatedResult: CollectionResult = {
        influencers: [...JSON.parse(existingCollection.result as string).influencers, ...influencers]
          .filter((influencer, index, self) => 
            index === self.findIndex(i => i.username === influencer.username)
          ) // 去重
      };

      await this.db
        .update(collections)
        .set({
          result: JSON.stringify(updatedResult),
          updatedAt: new Date()
        })
        .where(eq(collections.id, existingCollection.id));

      return {
        ...existingCollection,
        result: updatedResult,
        updatedAt: new Date()
      };
    }

    // 创建新集合
    const collectionId = generateId();
    const result: CollectionResult = { influencers };

    await this.db
      .insert(collections)
      .values({
        id: collectionId,
        userId,
        name,
        platform,
        result: JSON.stringify(result),
        createdAt: new Date(),
        updatedAt: new Date()
      });

    return {
      id: collectionId,
      userId,
      name,
      platform,
      result,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  // 获取用户的所有集合
  async getUserCollections(userId: string): Promise<Collection[]> {
    const userCollections = await this.db
      .select()
      .from(collections)
      .where(eq(collections.userId, userId))
      .orderBy(collections.updatedAt);

    return userCollections.map(collection => ({
      ...collection,
      result: JSON.parse(collection.result as string)
    }));
  }

  // 获取集合详情
  async getCollection(collectionId: string, userId: string): Promise<Collection | null> {
    const collection = await this.db
      .select()
      .from(collections)
      .where(and(
        eq(collections.id, collectionId),
        eq(collections.userId, userId)
      ))
      .get();

    if (!collection) return null;

    return {
      ...collection,
      result: JSON.parse(collection.result as string)
    };
  }

  // 更新集合中的博主列表
  async updateCollectionInfluencers(collectionId: string, influencers: InfluencerData[]): Promise<Collection> {
    const result: CollectionResult = { influencers };

    await this.db
      .update(collections)
      .set({
        result: JSON.stringify(result),
        updatedAt: new Date()
      })
      .where(eq(collections.id, collectionId));

    const updatedCollection = await this.db
      .select()
      .from(collections)
      .where(eq(collections.id, collectionId))
      .get();

    if (!updatedCollection) {
      throw new Error('更新后的集合未找到');
    }

    return {
      ...updatedCollection,
      result: JSON.parse(updatedCollection.result as string)
    } as Collection;
  }

  // 记录用户行为
  async recordUserAction(
    userId: string,
    collectionId: string,
    influencerUsername: string,
    platformUserId: string,
    actionType: 'viewed' | 'skipped',
    platform: string = 'instagram'
  ): Promise<void> {
    await this.db
      .insert(userActions)
      .values({
        id: generateId(),
        userId,
        collectionId,
        influencerUsername,
        platformUserId,
        actionType,
        platform,
        createdAt: new Date()
      });
  }

  // 获取用户在集合中的已查看博主
  async getViewedInfluencers(userId: string, collectionId: string): Promise<string[]> {
    const actions = await this.db
      .select({ influencerUsername: userActions.influencerUsername })
      .from(userActions)
      .where(and(
        eq(userActions.userId, userId),
        eq(userActions.collectionId, collectionId),
        eq(userActions.actionType, 'viewed')
      ));

    return actions.map(action => action.influencerUsername);
  }

  // 添加收藏
  async addToFavorites(
    userId: string,
    collectionId: string,
    influencerUsername: string,
    platformUserId: string,
    platform: string = 'instagram'
  ): Promise<void> {
    await this.db
      .insert(favorites)
      .values({
        id: generateId(),
        userId,
        collectionId,
        influencerUsername,
        platformUserId,
        platform,
        createdAt: new Date()
      });
  }

  // 移除收藏
  async removeFromFavorites(
    userId: string,
    collectionId: string,
    influencerUsername: string
  ): Promise<void> {
    await this.db
      .delete(favorites)
      .where(and(
        eq(favorites.userId, userId),
        eq(favorites.collectionId, collectionId),
        eq(favorites.influencerUsername, influencerUsername)
      ));
  }

  // 获取收藏列表
  async getFavorites(userId: string, collectionId: string): Promise<string[]> {
    const favoriteList = await this.db
      .select({ influencerUsername: favorites.influencerUsername })
      .from(favorites)
      .where(and(
        eq(favorites.userId, userId),
        eq(favorites.collectionId, collectionId)
      ));

    return favoriteList.map(fav => fav.influencerUsername);
  }

  // 获取集合中未查看的博主（排除已查看的）
  async getUnviewedInfluencers(userId: string, collectionId: string): Promise<InfluencerData[]> {
    const collection = await this.getCollection(collectionId, userId);
    if (!collection) return [];

    const viewedUsernames = await this.getViewedInfluencers(userId, collectionId);
    
    return collection.result.influencers.filter(
      influencer => !viewedUsernames.includes(influencer.username)
    );
  }
}
