interface InstagramInfluencer {
  id: string;
  username: string;
  full_name: string;
  profile_pic_url: string;
  is_verified: boolean;
  is_private: boolean;
}

interface InstagramNode {
  id: string;
  username: string;
  full_name: string;
  profile_pic_url: string;
  is_verified: boolean;
  is_private: boolean;
}

interface InstagramEdge {
  node: InstagramNode;
}

interface InstagramApiResponse {
  data: {
    user: {
      edge_related_profiles: {
        edges: InstagramEdge[];
      };
    };
  };
  status: string;
}

export class InstagramService {
  private readonly apiKey: string;
  private readonly apiHost: string;

  constructor(apiKey?: string) {
    // Store API credentials in environment variables for security
    this.apiKey = apiKey || '54c199e1e7msh810956bb47c1a46p12dfa6jsnaa848725e50e';
    this.apiHost = 'instagram-looter2.p.rapidapi.com';
  }

  async getRelatedInfluencers(username: string): Promise<InstagramInfluencer[]> {
    try {
      const response = await fetch(
        `https://${this.apiHost}/web-profile?username=${encodeURIComponent(username)}`,
        {
          method: 'GET',
          headers: {
            'x-rapidapi-host': this.apiHost,
            'x-rapidapi-key': this.apiKey,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const result = await response.json() as InstagramApiResponse;

      // Parse the response and extract influencers
      if (result.data?.user?.edge_related_profiles?.edges) {
        return result.data.user.edge_related_profiles.edges
          .map((edge: InstagramEdge) => ({
            id: edge.node.id || '',
            username: edge.node.username || '',
            full_name: edge.node.full_name || '',
            profile_pic_url: edge.node.profile_pic_url || '',
            is_verified: edge.node.is_verified || false,
            is_private: edge.node.is_private || false,
          }))
          .filter((influencer: InstagramInfluencer) => influencer.username); // Filter out empty usernames
      }

      return [];
    } catch (error) {
      console.error('Instagram API error:', error);
      throw error;
    }
  }
}