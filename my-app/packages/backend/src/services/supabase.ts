export interface Env {
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  SUPABASE_JWT_SECRET: string;
}

export interface SupabaseUser {
  id: string;
  email?: string;
  user_metadata?: {
    name?: string;
    [key: string]: any;
  };
}

export interface SupabaseSession {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  user: SupabaseUser;
}

export interface SupabaseAuthResponse {
  user: SupabaseUser | null;
  session: SupabaseSession | null;
}

/**
 * Supabase Auth API 客户端（使用 fetch）
 */
export class SupabaseAuth {
  private baseUrl: string;
  private apiKey: string;

  constructor(supabaseUrl: string, apiKey: string) {
    this.baseUrl = `${supabaseUrl}/auth/v1`;
    this.apiKey = apiKey;
  }

  private async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'apikey': this.apiKey,
        'Authorization': `Bearer ${this.apiKey}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json() as any;
      throw new Error(error.msg || error.message || 'Supabase request failed');
    }

    return response.json();
  }

  async signUp(email: string, password: string, userData?: any): Promise<SupabaseAuthResponse> {
    const response = await this.request('/signup', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        data: userData,
      }),
    });

    // 处理注册响应
    if (response.access_token && response.user) {
      // 如果有access_token，说明邮箱验证已关闭，直接返回session
      return {
        user: response.user,
        session: {
          access_token: response.access_token,
          refresh_token: response.refresh_token,
          expires_in: response.expires_in,
          token_type: response.token_type || 'bearer',
          user: response.user,
        }
      };
    }

    // 如果没有access_token，说明需要邮箱验证
    return {
      user: response.user || null,
      session: null,
    };
  }

  async signInWithPassword(email: string, password: string): Promise<SupabaseAuthResponse> {
    const response = await this.request('/token?grant_type=password', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
      }),
    });

    // Supabase返回的格式可能是 { access_token, refresh_token, user, ... }
    // 我们需要将其转换为我们期望的格式
    if (response.access_token && response.user) {
      return {
        user: response.user,
        session: {
          access_token: response.access_token,
          refresh_token: response.refresh_token,
          expires_in: response.expires_in,
          token_type: response.token_type || 'bearer',
          user: response.user,
        }
      };
    }

    // 如果没有access_token，说明登录失败
    return {
      user: response.user || null,
      session: null,
    };
  }

  async signOut(accessToken: string): Promise<void> {
    await this.request('/logout', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });
  }

  async getUser(accessToken: string): Promise<{ user: SupabaseUser | null }> {
    try {
      const result = await this.request('/user', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });
      return { user: result };
    } catch (error) {
      return { user: null };
    }
  }
}

/**
 * 创建 Supabase Auth 客户端（匿名访问）
 */
export function createSupabaseClient(env: Env) {
  return new SupabaseAuth(env.SUPABASE_URL, env.SUPABASE_ANON_KEY);
}

/**
 * 创建 Supabase Auth 管理员客户端（服务端访问）
 */
export function createSupabaseAdmin(env: Env) {
  return new SupabaseAuth(env.SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY);
}
