/**
 * 邮箱验证页面模板
 */

export const verificationPageTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证 - WaveInflu</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .icon { font-size: 64px; margin-bottom: 20px; }
        h1 { color: #1e293b; font-size: 28px; margin-bottom: 16px; }
        p { color: #64748b; font-size: 16px; line-height: 1.6; margin-bottom: 30px; }
        .loading { color: #667eea; }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🌊</div>
        <h1>WaveInflu</h1>
        <p class="loading">正在处理邮箱验证...</p>
    </div>
    
    <script>
        // 检查 URL fragment 中的验证参数
        function handleVerification() {
            const hash = window.location.hash;
            if (hash) {
                const params = new URLSearchParams(hash.substring(1));
                const accessToken = params.get('access_token');
                const type = params.get('type');
                const error = params.get('error');
                
                if (error) {
                    // 处理错误
                    document.querySelector('.icon').textContent = '❌';
                    document.querySelector('h1').textContent = '验证失败';
                    document.querySelector('.loading').innerHTML = '验证链接已过期或无效。<br><a href="#" onclick="window.close()">关闭页面</a>';
                } else if (accessToken && type === 'signup') {
                    // 验证成功
                    document.querySelector('.icon').textContent = '✅';
                    document.querySelector('h1').textContent = '验证成功！';
                    document.querySelector('.loading').innerHTML = '您的邮箱已验证成功！<br>现在可以登录 WaveInflu 扩展了。<br><a href="#" onclick="window.close()">关闭页面</a>';
                } else {
                    // 重定向到详细页面
                    window.location.href = '/verification/success';
                }
            } else {
                // 没有参数，重定向到详细页面
                window.location.href = '/verification/success';
            }
        }
        
        // 页面加载时处理验证
        window.addEventListener('load', handleVerification);
    </script>
</body>
</html>
`;

export const verificationSuccessTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证成功 - WaveInflu</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .icon { font-size: 64px; margin-bottom: 20px; }
        h1 { color: #1e293b; font-size: 28px; margin-bottom: 16px; }
        .subtitle { color: #64748b; font-size: 16px; line-height: 1.6; margin-bottom: 40px; }
        .steps {
            text-align: left;
            max-width: 400px;
            margin: 0 auto;
        }
        .step {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        .step-number {
            background: #667eea;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .step-text { color: #374151; font-size: 14px; }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #9ca3af;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">✅</div>
        <h1>🌊 邮箱验证成功！</h1>
        <p class="subtitle">
            恭喜！您的 WaveInflu 账户已成功激活。现在可以开始探索创作者的世界了！
        </p>
        
        <div class="steps">
            <h3 style="color: #1e293b; margin-bottom: 20px;">📱 如何使用 WaveInflu 扩展：</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击浏览器工具栏中的 WaveInflu 图标</div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">使用您的邮箱和密码登录</div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">访问 Instagram 个人主页并开始发现相似创作者</div>
            </div>
        </div>
        
        <div class="footer">
            WaveInflu - 发现无限创作可能
        </div>
    </div>
</body>
</html>
`;
