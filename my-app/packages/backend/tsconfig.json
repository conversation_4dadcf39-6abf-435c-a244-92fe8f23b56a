{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "lib": ["ES2022"], "target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "types": ["@cloudflare/workers-types"], "baseUrl": "./src", "paths": {"@/*": ["*"], "@/common/*": ["common/*"], "@/routes/*": ["routes/*"], "@/services/*": ["services/*"], "@/db/*": ["db/*"], "@/middleware/*": ["middleware/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}]}