name = "my-app-backend"
main = "dist/index.js"
compatibility_date = "2024-09-25"
account_id = "your-cloudflare-account-id"

[dev]
port = 8787

[env.development]
name = "my-app-backend-dev"

[env.production]
name = "my-app-backend-prod"

[env.production.vars]
NODE_ENV = "production"
CORS_ORIGIN = "*"
SUPABASE_URL = "your-supabase-url"
SUPABASE_JWT_SECRET = "your-supabase-jwt-secret"
SUPABASE_ANON_KEY = "your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY = "your-supabase-service-role-key"

# D1 Database configuration for development (local)
[[env.development.d1_databases]]
binding = "DB"
database_name = "my-app-db-local"
database_id = "your-dev-database-id"
migrations_dir = "drizzle"

# D1 Database configuration for production
[[env.production.d1_databases]]
binding = "DB"
database_name = "kol_prod"
database_id = "your-prod-database-id"
migrations_dir = "drizzle"

# Default D1 Database configuration (for local dev)
[[d1_databases]]
binding = "DB"
database_name = "my-app-db-local"
database_id = "your-dev-database-id"
migrations_dir = "drizzle"

# Environment variables
[vars]
NODE_ENV = "production"
CORS_ORIGIN = "*"
SUPABASE_URL = "your-supabase-url"
SUPABASE_JWT_SECRET = "your-supabase-jwt-secret"
SUPABASE_ANON_KEY = "your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY = "your-supabase-service-role-key"

# KV namespace for caching (optional)
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"
