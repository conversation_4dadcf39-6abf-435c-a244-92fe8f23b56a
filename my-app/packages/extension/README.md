# WaveInflu - Instagram 地区信息显示插件

一个专门用于显示 Instagram 用户地区信息的 Chrome 浏览器插件。

## 🎯 核心功能

### Instagram 地区信息显示
- **自动检测** Instagram 用户主页
- **实时获取** 用户地区信息
- **小巧显示** 简洁的地区信息组件
- **无感操作** 完全隐藏后台获取过程

### 显示效果
```
┌─────────────────┐
│ 📍 账户所在地    │
│ 🇺🇸 美国        │
└─────────────────┘
```

## 🌍 支持的国家和地区

支持全球 150+ 个国家和地区，包括：
- **北美洲**: 美国、加拿大、墨西哥
- **欧洲**: 英国、法国、德国、意大利、西班牙等
- **亚洲**: 中国、日本、韩国、印度、东南亚各国等
- **大洋洲**: 澳大利亚、新西兰等
- **非洲**: 南非、埃及、尼日利亚等
- **南美洲**: 巴西、阿根廷、智利等

## 🚀 技术特性

- **React 18** + TypeScript 构建
- **Manifest V3** 现代 Chrome 扩展 API
- **网络拦截** 智能获取地区数据
- **LRU 缓存** 提升性能和用户体验
- **事件驱动** 高效的数据通信
- **CSS 隐藏** 完全无感的后台操作

## 📦 安装使用

1. **构建插件**:
```bash
npm install
npm run build
```

2. **加载到 Chrome**:
   - 打开 `chrome://extensions/`
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `dist` 文件夹

3. **使用方法**:
   - 访问任意 Instagram 用户主页
   - 插件会自动显示地区信息
   - 无需任何手动操作

## 🛠️ 开发命令

- `npm run dev` - 开发模式（支持热重载）
- `npm run build` - 生产构建
- `npm run lint` - 代码检查
- `npm run type-check` - TypeScript 类型检查

## 📁 项目结构

```
src/
├── components/         # React 组件
│   ├── InfoCard.tsx           # 主要信息卡片组件
│   └── RegionFloatingWindow.tsx # 地区显示组件
├── content/           # 内容脚本
│   ├── content.ts             # 主内容脚本
│   └── instagram-injected-script.js # 网络拦截脚本
├── utils/             # 工具函数
│   ├── instagram-utils.ts     # Instagram 相关工具
│   ├── getUserAreaFromIntro.ts # 地区信息获取
│   ├── dom-utils.ts          # DOM 操作工具
│   └── event-system.ts       # 事件系统
└── background/        # 后台脚本
    └── background.ts          # 服务工作者
```

## ⚡ 性能优化

- **智能缓存**: LRU 缓存机制，避免重复请求
- **事件驱动**: 高效的跨脚本通信
- **按需加载**: 只在 Instagram 页面激活
- **CSS 优化**: 完全隐藏模拟操作，无闪烁
- **代码分割**: 优化打包大小和加载速度

## 🔧 技术实现

### 网络拦截机制
- 拦截 Instagram 的 `/async/wbloks/fetch/` 请求
- 解析 `bloks_payload` 数据结构
- 提取用户地区信息

### 跨脚本通信
- MAIN world: 网络拦截和数据提取
- ISOLATED world: UI 组件和用户交互
- CustomEvent: 安全的数据传递

### 智能隐藏
- 提前应用 CSS 隐藏规则
- 多层级选择器确保完全隐藏
- 延迟恢复避免闪烁

## 📄 许可证

MIT License