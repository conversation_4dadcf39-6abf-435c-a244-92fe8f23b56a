{"name": "@my-app/extension", "version": "1.0.0", "description": "Modern Chrome Extension with React and TypeScript", "scripts": {"dev": "webpack --watch --mode development", "build": "webpack --mode production", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/chrome": "^0.0.258", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "autoprefixer": "^10.4.16", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "prettier": "^3.1.1", "style-loader": "^3.3.3", "tailwindcss": "^3.4.0", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@my-app/shared": "workspace:*", "@supabase/supabase-js": "^2.52.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.309.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.0", "xlsx": "^0.18.5"}}