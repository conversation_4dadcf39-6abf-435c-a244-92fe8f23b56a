chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed');
  chrome.storage.local.set({ count: 0 });
});

chrome.runtime.onMessage.addListener((request, sender) => {
  console.log('Message received:', request);
  
  if (request.type === 'CONTENT_SCRIPT_LOADED') {
    console.log('Content script loaded on:', sender.tab?.url);
  }
  
  return true;
});

chrome.tabs.onUpdated.addListener((_tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    console.log('Tab updated:', tab.url);
  }
});

chrome.action.onClicked.addListener(async (tab) => {
  console.log('Extension icon clicked on tab:', tab.url);
  
  // Open side panel when extension icon is clicked
  if (tab.windowId) {
    await chrome.sidePanel.open({ windowId: tab.windowId });
  }
});