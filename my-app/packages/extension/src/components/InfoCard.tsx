
import React, { useState, useEffect } from 'react';
import { RegionFloatingWindow } from './RegionFloatingWindow';
import { isInInsKOLPage, extractInsKOLHandler } from '../utils/dom-utils';
import { getUserAreaFromIntroCache, setUserAreaFromIntroCache, getUserAreaFromIntro } from '../utils/getUserAreaFromIntro';
import { listenEvent } from '../utils/event-system';

interface RegionInfo {
  region: string;
}

export const InfoCard: React.FC = () => {
  const [regionInfo, setRegionInfo] = useState<RegionInfo | null>(null);
  const [currentUrl, setCurrentUrl] = useState(window.location.href);
  const [autoTriggerEnabled] = useState(true); // 简单的开关控制

  useEffect(() => {
    let unlistenEvent: (() => void) | null = null;

    const checkAndLoadRegion = () => {
      // 只在Instagram KOL页面显示
      if (!isInInsKOLPage()) {
        setRegionInfo(null);
        return;
      }

      const handler = extractInsKOLHandler(window.location.href);
      if (!handler) return;

      // 检查缓存
      const cachedRegion = getUserAreaFromIntroCache(handler);
      if (cachedRegion) {
        setRegionInfo({ region: cachedRegion });
        return;
      }

      // 监听地区数据事件
      if (unlistenEvent) unlistenEvent();

      unlistenEvent = listenEvent('INS_AREA', (data: string) => {
        if (data && typeof data === 'string') {
          setRegionInfo({ region: data });
          setUserAreaFromIntroCache(handler, data);
        }
      });

      // 完全按照efluns的逻辑：只有在特定条件下才触发获取地区信息
      // 添加条件控制，避免过度触发
      if (autoTriggerEnabled) {
        setTimeout(() => {
          getUserAreaFromIntro().catch(console.error);
        }, 1000);
      }
    };

    // 监听URL变化 - 借鉴efluns的方法
    const handleUrlChange = () => {
      const newUrl = window.location.href;
      if (newUrl !== currentUrl) {
        setCurrentUrl(newUrl);
        // 清理当前状态
        setRegionInfo(null);
        if (unlistenEvent) {
          unlistenEvent();
          unlistenEvent = null;
        }
        // 延迟检查新页面
        setTimeout(checkAndLoadRegion, 800);
      }
    };

    // 初始检查
    checkAndLoadRegion();

    // 使用现代Navigation API（如果支持）
    if ('navigation' in window) {
      (window as any).navigation.addEventListener('navigatesuccess', handleUrlChange);
    }

    // 备用方案：监听popstate和定期检查
    window.addEventListener('popstate', handleUrlChange);
    const intervalId = setInterval(handleUrlChange, 500);

    return () => {
      if ('navigation' in window) {
        (window as any).navigation.removeEventListener('navigatesuccess', handleUrlChange);
      }
      window.removeEventListener('popstate', handleUrlChange);
      clearInterval(intervalId);
      if (unlistenEvent) unlistenEvent();
    };
  }, [currentUrl]);

  // 只在有地区信息时显示
  if (!regionInfo) return null;

  return <RegionFloatingWindow regionInfo={regionInfo} />;
};
