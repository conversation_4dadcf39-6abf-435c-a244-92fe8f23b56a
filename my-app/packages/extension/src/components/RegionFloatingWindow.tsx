/**
 * 小巧的地区显示组件
 * 只显示国旗和国家名称，简洁美观
 */

import React from 'react';
import { getCountryCodeFromName, getCountryIcon } from '../utils/instagram-utils';

interface RegionInfo {
  region: string;
}

interface RegionDisplayProps {
  regionInfo: RegionInfo;
}

export const RegionFloatingWindow: React.FC<RegionDisplayProps> = ({ regionInfo }) => {
  const countryCode = getCountryCodeFromName(regionInfo.region);
  const flagUrl = getCountryIcon(countryCode); // 不设置默认值

  return (
    <div style={{
      position: 'relative',
      zIndex: 999999,
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      padding: '12px 16px',
      animation: 'slideIn 0.3s ease-out',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: '14px',
      minWidth: '160px',
      maxWidth: '200px',
      margin: '0 auto'
    }}>
      {/* 标题 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        marginBottom: '8px',
        color: '#6b7280',
        fontSize: '12px',
        fontWeight: '500'
      }}>
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
          <circle cx="12" cy="10" r="3"></circle>
        </svg>
        账户所在地
      </div>

      {/* 地区信息 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        {/* 只有找到国旗时才显示 */}
        {flagUrl && (
          <img
            src={flagUrl}
            alt={regionInfo.region}
            style={{
              width: '20px',
              height: '15px',
              borderRadius: '2px',
              border: '1px solid rgba(0, 0, 0, 0.1)',
              flexShrink: 0
            }}
          />
        )}
        <span style={{
          color: '#1f2937',
          fontWeight: '600',
          fontSize: '14px'
        }}>
          {regionInfo.region}
        </span>
      </div>

      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
      `}</style>
    </div>
  );
};
