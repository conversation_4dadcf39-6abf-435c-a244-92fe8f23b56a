import React, { useState } from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import { <PERSON>ertCircle, Eye, EyeOff, Loader2, UserCheck } from 'lucide-react';
import { authService, type LoginCredentials, type SignupCredentials } from '../../services/auth.service';
import { collectionService } from '../../services/collection.service';

interface LoginFormProps {
  onLoginSuccess: (user: any) => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onLoginSuccess }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // 第一步：认证
      if (isLogin) {
        const credentials: LoginCredentials = {
          email: formData.email,
          password: formData.password,
        };
        await authService.login(credentials);
      } else {
        const credentials: SignupCredentials = {
          email: formData.email,
          password: formData.password,
          name: formData.name || undefined,
        };
        await authService.signup(credentials);
      }

      // 第二步：初始化用户信息
      setIsLoading(false);
      setIsInitializing(true);

      // 等待用户创建完成并获取完整信息
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('无法获取用户信息，请重试');
      }

      // 第三步：检查集合信息
      let hasCollections = false;
      try {
        const collections = await collectionService.getUserCollections();
        hasCollections = collections.length > 0;
      } catch (error) {
        console.error('获取集合列表失败:', error);
      }

      // 完成初始化，传递用户信息
      onLoginSuccess({ user: currentUser, hasCollections });

    } catch (err: any) {
      setError(err.message || '操作失败，请重试');
      setIsLoading(false);
      setIsInitializing(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    setError(null);
    setFormData({
      email: '',
      password: '',
      name: '',
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-300/10 to-purple-300/10 rounded-full blur-3xl"></div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* 顶部品牌区域 */}
        <div className="flex-shrink-0 pt-8 pb-4">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-2xl shadow-2xl mb-4 transform hover:scale-105 transition-all duration-300 p-2">
              <img
                src="/icon-128.png"
                alt="WaveInflu Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
              WaveInflu
            </h1>
            <p className="text-gray-600 text-sm font-medium">一波一波的发现创作者</p>
          </div>
        </div>

        {/* 登录表单容器 */}
        <div className="flex-1 flex items-center justify-center px-6 pb-8">
          <div className="w-full max-w-sm">
            <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-xl overflow-hidden transform hover:scale-[1.02] transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/50 to-pink-50/50"></div>
              <CardContent className="relative p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
                    {isLogin ? '欢迎回来' : '开始使用'}
                  </h2>
                  <p className="text-gray-600 text-sm">
                    {isLogin ? '请登录您的账户' : '开始您的创作者发现之旅'}
                  </p>
                </div>

                {error && (
                  <div className="mb-6 p-4 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl flex items-center gap-3 animate-in slide-in-from-top-2 fade-in duration-300">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <AlertCircle className="w-4 h-4 text-red-500" />
                    </div>
                    <span className="text-red-700 text-sm font-medium">{error}</span>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  {!isLogin && (
                    <div className="animate-in slide-in-from-top-2 fade-in duration-300">
                      <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                        姓名 (可选)
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 text-sm bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 focus:bg-white transition-all duration-300 placeholder-gray-400"
                        placeholder="请输入您的姓名"
                      />
                    </div>
                  )}

                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                      邮箱地址
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 text-sm bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 focus:bg-white transition-all duration-300 placeholder-gray-400"
                      placeholder="请输入邮箱地址"
                    />
                  </div>

                  <div>
                    <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-2">
                      密码
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        required
                        minLength={6}
                        className="w-full px-4 py-3 pr-12 text-sm bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 focus:bg-white transition-all duration-300 placeholder-gray-400"
                        placeholder="请输入密码"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-500 hover:text-gray-700 transition-colors duration-200 rounded-lg hover:bg-gray-100/50"
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                    {!isLogin && (
                      <p className="text-xs text-gray-500 mt-2 ml-1">密码至少需要 6 个字符</p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    disabled={isLoading || isInitializing}
                    className="w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 text-white py-4 text-sm rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl disabled:transform-none disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center gap-3">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        <span>{isLogin ? '登录中...' : '注册中...'}</span>
                      </div>
                    ) : isInitializing ? (
                      <div className="flex items-center justify-center gap-3">
                        <UserCheck className="w-5 h-5 animate-pulse" />
                        <span>正在初始化用户...</span>
                      </div>
                    ) : (
                      <span className="flex items-center justify-center gap-2">
                        {isLogin ? '登录账户' : '创建账户'}
                      </span>
                    )}
                  </Button>
                </form>

                <div className="mt-8 text-center">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-200/50"></div>
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-white/90 px-4 text-gray-500 font-medium">或者</span>
                    </div>
                  </div>

                  <div className="mt-6">
                    <p className="text-gray-600 text-sm">
                      {isLogin ? '还没有账户？' : '已有账户？'}
                      <button
                        type="button"
                        onClick={toggleMode}
                        className="ml-2 text-blue-600 hover:text-purple-600 font-semibold transition-all duration-200 hover:underline"
                      >
                        {isLogin ? '立即注册' : '立即登录'}
                      </button>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
