/**
 * 环境配置
 */
export interface EnvironmentConfig {
  apiUrl: string;
  isDevelopment: boolean;
  requestTimeout: number;
  supabase: {
    url: string;
    anonKey: string;
  };
  defaultQuota: {
    total: number;
    used: number;
  };
}

/**
 * 检测当前环境
 */
function detectEnvironment(): boolean {
  // 强制使用生产环境配置
  // 如果需要本地开发，请手动修改此函数返回 true
  return false;

  // 原来的检测逻辑（已注释）
  // const checks = [
  //   // 检查是否在本地开发
  //   window.location.hostname === 'localhost',
  //   window.location.hostname === '127.0.0.1',
  //
  //   // 检查扩展 ID（开发时通常是临时 ID）
  //   chrome?.runtime?.id?.startsWith('temp-') || false,
  //
  //   // 检查 manifest 版本是否包含 dev 标识
  //   chrome?.runtime?.getManifest?.()?.version?.includes('dev') || false,
  //
  //   // 检查是否是未打包的扩展
  //   chrome?.runtime?.getManifest?.()?.update_url === undefined,
  // ];
  //
  // return checks.some(check => check);
}

/**
 * 获取环境配置
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const isDevelopment = detectEnvironment();

  return {
    apiUrl: isDevelopment
      ? 'http://localhost:8787/api'
      : 'https://api.wavely.cc/api',
    isDevelopment,
    requestTimeout: 30_000, // 30 seconds
    supabase: {
      url: isDevelopment
        ? 'https://zwzvaqhmfgcvsfstxurq.supabase.co'
        : 'https://zwzvaqhmfgcvsfstxurq.supabase.co', // 生产环境应该使用不同的 Supabase 项目
      anonKey: isDevelopment
        ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp3enZhcWhtZmdjdnNmc3R4dXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzNDM0NjYsImV4cCI6MjA2NTkxOTQ2Nn0.Ge-uw6L7gnvDusvzCb8QvACwJLpBW0h_YHElolzId8I'
        : 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp3enZhcWhtZmdjdnNmc3R4dXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzNDM0NjYsImV4cCI6MjA2NTkxOTQ2Nn0.Ge-uw6L7gnvDusvzCb8QvACwJLpBW0h_YHElolzId8I', // 生产环境应该使用不同的密钥
    },
    defaultQuota: {
      total: 5,
      used: 0,
    },
  };
}

/**
 * 环境常量
 */
export const ENV = getEnvironmentConfig();
