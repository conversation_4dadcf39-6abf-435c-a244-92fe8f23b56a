import React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { InfoCard } from '../components/InfoCard';

console.log('Content script loaded');

// CSS隐藏规则现在由instagram-early-script.ts在document_start时处理

chrome.runtime.sendMessage({ type: 'CONTENT_SCRIPT_LOADED' });

chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.type === 'GET_PAGE_INFO') {
    const pageInfo = {
      title: document.title,
      url: window.location.href,
      description: document.querySelector('meta[name="description"]')?.getAttribute('content') || ''
    };
    sendResponse(pageInfo);
  }
  return true;
});

// 检查是否在Instagram页面
function isInstagramPage(): boolean {
  return window.location.hostname === 'www.instagram.com';
}

// 等待DOM元素出现的工具函数
function waitFor(selector: string, timeout = 10000): Promise<HTMLElement> {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector) as HTMLElement;
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

// Instagram容器选择器 - 完全按照efluns的方式
const INS_CONTAINER_SELECTOR = '[role="main"]';
const ANCHOR_ID = 'my-app-region-anchor';

// 网络拦截现在由instagram-early-script.ts在document_start时处理

// 获取挂载点 - 完全按照efluns的方式
async function getAnchor(): Promise<HTMLElement> {
  const container = await waitFor(INS_CONTAINER_SELECTOR);
  let anchor = Array.from(container.children).find((child) => child.id === ANCHOR_ID) as HTMLElement | undefined;

  if (!anchor) {
    anchor = document.createElement('div');
    anchor.id = ANCHOR_ID;
    // 设置为相对定位，不影响页面布局
    anchor.style.cssText = `
      position: relative;
      pointer-events: none;
      z-index: 999999;
    `;
    container.appendChild(anchor);
  }

  return anchor;
}

let root: Root | null = null;

// 初始化InfoCard组件 - 完全按照efluns的方式
async function initInfoCard() {
  if (!isInstagramPage()) return;

  console.log('初始化InfoCard组件');

  try {
    const anchor = await getAnchor();

    // 创建Shadow DOM
    if (!anchor.shadowRoot) {
      anchor.attachShadow({ mode: 'open' });
    }

    // 创建应用容器
    let appContainer = anchor.shadowRoot!.querySelector('#region-app-container') as HTMLElement;
    if (!appContainer) {
      appContainer = document.createElement('div');
      appContainer.id = 'region-app-container';
      appContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        pointer-events: auto;
        z-index: 999999;
      `;
      anchor.shadowRoot!.appendChild(appContainer);
    }

    // 渲染React组件
    if (root) {
      root.unmount();
    }
    root = createRoot(appContainer);
    root.render(React.createElement(InfoCard));
  } catch (error) {
    console.error('初始化InfoCard失败:', error);
  }
}

// 页面加载完成后初始化
function initialize() {
  if (!isInstagramPage()) return;

  console.log('开始初始化Instagram地区信息功能');

  setTimeout(() => {
    initInfoCard();
  }, 1000);
}

// 等待页面加载
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// 使用Navigation API处理页面导航 - 完全按照efluns的方式
if ((window as any).navigation) {
  (window as any).navigation.addEventListener('navigate', () => {
    if (root) {
      root.unmount();
      root = null;
    }
  });

  (window as any).navigation.addEventListener('navigatesuccess', () => {
    setTimeout(() => {
      initialize();
    }, 1000);
  });
} else {
  // 降级到MutationObserver（用于不支持Navigation API的浏览器）
  const observer = new MutationObserver((mutations) => {
    console.log('Page mutations detected:', mutations.length);

    // 检查URL是否变化
    const currentUrl = window.location.href;
    if (currentUrl !== (window as any).lastUrl) {
      (window as any).lastUrl = currentUrl;
      console.log('URL变化，重新初始化:', currentUrl);

      // 延迟重新初始化，确保页面内容加载完成
      setTimeout(initialize, 500);
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}