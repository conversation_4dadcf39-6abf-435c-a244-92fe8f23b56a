// Instagram早期脚本 - 完全对齐efluns的架构
// runAt: document_start, world: MAIN

import './instagram-early.css';
import './instagram-stats.css';
import { observeInsCards } from '../utils/instagram-stats-display';

// 检查是否在Instagram页面
function isInstagramPage(): boolean {
  return window.location.hostname === 'www.instagram.com';
}

// 发送事件的辅助函数
function sendEvent(name: string, data: any) {
  window.dispatchEvent(new CustomEvent(name, { detail: data }));
}

// Instagram数据类型定义
interface InsCardData {
  code: string;
  like_count: number;
  comment_count: number;
  play_count?: number;
  created_time: number;

  isPinned?: boolean;
  is_paid_partnership?: boolean;
  is_shop?: boolean;
  username?: string;
}

interface InstagramChannelData {
  posts: InsCardData[];
  isFirst: boolean;
  median_number?: number;
}

// 数据存储和通信
const STORAGE_KEY_PREFIX = 'instagram_data_';

function sendInstagramPostData(data: InstagramChannelData) {
  const url = window.location.href;
  const storageKey = STORAGE_KEY_PREFIX + url;
  const oldData = JSON.parse(sessionStorage.getItem(storageKey) || '{}') as InstagramChannelData;

  if (data.isFirst) {
    data.median_number = getMedian(data.posts.map(item => item.like_count));
    sessionStorage.setItem(storageKey, JSON.stringify(data));
    sendToContentScript(data);

    // 清理旧数据
    const keys = Object.keys(sessionStorage);
    const dataKeys = keys.filter(key => key.startsWith(STORAGE_KEY_PREFIX));
    if (dataKeys.length > 10) {
      sessionStorage.removeItem(dataKeys[0]);
    }
  } else {
    data.posts = [...oldData.posts, ...data.posts];
    data.median_number = getMedian(data.posts.map(item => item.like_count));
    sessionStorage.setItem(storageKey, JSON.stringify(data));
    sendToContentScript(data);
  }

  // 调用统计计算 - 完全按照efluns的方式
  setTimeout(() => {
    const event = new CustomEvent('INSTAGRAM_STATS_CALCULATE', {
      detail: { posts: data.posts }
    });
    window.dispatchEvent(event);
  }, 100);
}

function sendToContentScript(data: InstagramChannelData) {
  window.postMessage({
    type: 'FROM_INSTAGRAM_EARLY_SCRIPT',
    url: window.location.href,
    data: data
  }, window.location.origin);
}

function getMedian(numbers: number[]): number {
  const sorted = numbers.slice().sort((a, b) => a - b);
  const middle = Math.floor(sorted.length / 2);

  if (sorted.length % 2 === 0) {
    return (sorted[middle - 1] + sorted[middle]) / 2;
  } else {
    return sorted[middle];
  }
}



// 主函数
function main() {
  if (!isInstagramPage()) return;

  console.log('Instagram Early Script is loading');

  // 拦截XMLHttpRequest - 完全按照efluns的方式
  const originalXHR = window.XMLHttpRequest;
  (window as any).XMLHttpRequest = function() {
    const xhr = new originalXHR();
    const originalOpen = xhr.open;

    xhr.open = function(...args: any[]) {
      if (args[0] === 'POST' && args[1]?.includes('/graphql/query')) {
        const originalSend = xhr.send;
        xhr.send = function(body) {
          let isFirst = true;
          if (typeof body === 'string') {
            try {
              const params = new URLSearchParams(body);
              const variablesStr = params.get('variables');
              if (variablesStr) {
                const variables = JSON.parse(decodeURIComponent(variablesStr));
                isFirst = !variables.after;
              }
            } catch (e) {
              console.error('解析请求体失败:', e);
            }
          }

          xhr.addEventListener('load', function() {
            try {
              const response = JSON.parse(xhr.responseText);

              // 拦截用户时间线数据 - 普通帖子
              if (response?.data?.xdt_api__v1__feed__user_timeline_graphql_connection && !response?.data?.user) {
                const edges = response.data.xdt_api__v1__feed__user_timeline_graphql_connection.edges;
                const datas = edges.map((e: any) => ({
                  code: e.node.code,
                  like_count: e.node.like_count,
                  comment_count: e.node.comment_count,
                  created_time: e.node.taken_at,
                  isPinned: e.node.timeline_pinned_user_ids?.length,
                  is_paid_partnership: e.node.is_paid_partnership,
                  is_shop: e.node.is_shop
                }));

                const instagramData: InstagramChannelData = {
                  posts: datas,
                  isFirst,
                };

                sendInstagramPostData(instagramData);
                console.log('Instagram posts data:', instagramData);
              }

              // 拦截Reels数据
              if (response?.data?.xdt_api__v1__clips__user__connection_v2) {
                const reels = response.data.xdt_api__v1__clips__user__connection_v2.edges;
                const reelsData = reels.map((e: any) => ({
                  code: e?.node?.media?.code,
                  like_count: e?.node?.media?.like_count,
                  comment_count: e?.node?.media?.comment_count,
                  play_count: e?.node?.media?.play_count,
                  is_paid_partnership: e?.node?.media?.is_paid_partnership,
                  is_shop: e?.node?.media?.is_shop,
                  isPinned: !!e?.node?.media?.clips_tab_pinned_user_ids?.length
                }));

                const instagramReelsData: InstagramChannelData = {
                  posts: reelsData,
                  isFirst
                };

                sendInstagramPostData(instagramReelsData);
                console.log('Instagram reels data:', instagramReelsData);
              }

            } catch (error) {
              console.error('解析Instagram响应时出错:', error);
            }
          });

          return originalSend.apply(this, arguments as any);
        };
      }
      return originalOpen.apply(this, args as any);
    };

    return xhr;
  };

  // 拦截fetch请求 - 保留原有的地区信息拦截
  const originalFetch = window.fetch;
  window.fetch = async function(...args) {
    const [resource] = args;
    const response = await originalFetch.apply(this, args);

    // 专门拦截包含用户详细信息的请求
    if(typeof resource === 'string' && resource.includes('/async/wbloks/fetch/')){
      const clonedResponse = response.clone();

      try {
        const rawResponse = await clonedResponse.text();
        const jsonStr = rawResponse.replace(/^for \(;;\);/, '');
        const data = JSON.parse(jsonStr);
        const bloksPayload = data?.payload?.layout?.bloks_payload;

        if (bloksPayload?.data?.[0]?.data?.initial) {
          console.log('Instagram地区信息获取成功:', bloksPayload.data[0].data.initial);
          sendEvent("INS_AREA", bloksPayload.data[0].data.initial);
        }
      } catch (error) {
        console.error('解析Instagram地区信息时出错:', error);
      }
    }

    return response;
  };

  console.log('Instagram网络拦截器设置完成');

  // 启动DOM观察器 - 完全按照efluns的方式
  const startObserver = () => {
    if (document.body) {
      observeInsCards();
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        observeInsCards();
      }, { once: true });
    }
  };

  startObserver();
}

// 立即执行
main();
