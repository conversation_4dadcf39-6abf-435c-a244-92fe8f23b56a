// Instagram 注入脚本 - 在页面最早期运行，用于拦截网络请求
// 这个脚本会被注入到页面的 MAIN world 中，可以访问页面的原生 fetch 函数

(function() {
    'use strict';
    
    console.log('Instagram 注入脚本开始执行');
    
    // 检查是否在 Instagram 页面
    if (!window.location.hostname.includes('instagram.com')) {
        return;
    }
    
    // 发送事件的辅助函数
    function sendEvent(name, data) {
        window.dispatchEvent(new CustomEvent(name, { detail: data }));
    }
    
    // 拦截fetch请求 - 专门用于获取地区信息
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
        const [resource] = args;
        // 调用原始fetch
        const response = await originalFetch.apply(this, args);
       
        // 专门拦截包含用户详细信息的请求
        if(typeof resource === 'string' && resource.includes('/async/wbloks/fetch/')){
            const clonedResponse = response.clone();

            try {
                // 1. 移除开头的 "for (;;);" 安全前缀
                const rawResponse = await clonedResponse.text();
                const jsonStr = rawResponse.replace(/^for \(;;\);/, '');

                // 2. 解析JSON
                const data = JSON.parse(jsonStr);
                
                // 3. 提取bloks_payload中的地区信息
                const bloksPayload = data?.payload?.layout?.bloks_payload;
                
                if (bloksPayload?.data?.[0]?.data?.initial) {
                    console.log('Instagram地区信息获取成功:', bloksPayload.data[0].data.initial);
                    // 发送地区信息事件
                    sendEvent("INS_AREA", bloksPayload.data[0].data.initial);
                }
            } catch (error) {
                console.error('解析Instagram地区信息时出错:', error);
            }
        }
        
        return response;
    };
    
    console.log('Instagram网络拦截器设置完成');
})();
