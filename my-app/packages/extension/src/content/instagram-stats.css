/* Instagram统计数据显示样式 - 完全按照efluns原始设计 */

#easy-kol-ins-poster-data {
    position: absolute;
    bottom: 4px;
    right: 11px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
    font-weight: 700;
    font-size: 13px;
}

#easy-kol-ins-poster-data .__ins_stat {
    display: flex;
    align-items: center;
}

#easy-kol-ins-poster-data .__ins_stat svg{
    vertical-align: text-top;
    margin-right: 3px;
}

#easy-kol-ins-poster-data .__ins_stat_er{
    position: absolute;
    top: 0;
    left: 0;
    transform: translateY(-105%);
}

#easy-kol-ins-poster-data.__ins_reels{
    gap:8px;
    right: 12px;
    font-size: 12px;
}

#easy-kol-ins-poster-data.__ins_reels svg{
    width: 12px;
    height: 12px;
}

#easy-kol-ins-poster-data.__ins_reels .__ins_stat{
    display: flex;
    align-items: center;
}

header section:has(#easy-kol-collector-social-links-card){
    overflow: unset;
}

.__efluns_created_time{
    position: absolute;
    top: 28px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.__efluns_time_badge{
    background: rgba(0, 0, 0, 0.75);
    color: #fff;
    font-weight: 700;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}



.__efluns_username{
    color: #fff;
    font-weight: 700;
    font-size: 13px;
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 9;
    cursor: pointer;
    max-width: 117px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

header section:first-child{
    z-index: 2;
}

 .__efluns_median_number{
    border-radius: 10px;
    color: #fff;
    padding: 0 5px;
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 13px;
    cursor: help;
}

/* 烂 */
 .__efluns_median_number[data-type="bad"]{
    background-color: #34AEFF;
}

/* 普通，无色 */
 .__efluns_median_number[data-type="normal"]{
    background-color: transparent;
}

/* 好 */
 .__efluns_median_number[data-type="good"]{
    background-color: #FE2C55;
}

/* 付费 */
 .__efluns_median_number[data-type="paid"]{
    background-color: #34C759;
}

/* 添加提示框样式 */
.__efluns_median_number:hover:after {
    content: "Data from Likes ÷ Median Likes, via EasyKOL";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    margin-bottom: 5px;
    z-index: 1000;
}

/* 添加小三角形 */
.__efluns_median_number:hover:before {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
    margin-bottom: -5px;
    z-index: 1000;
}

.__efluns_median_number[hideType="commentCount"]:hover:after{
    content: "Data from Comments ÷ Median Comments, via EasyKOL";
}

.__efluns_median_number[hideType="playCount"]:hover:after{
    content: "Data from Views ÷ Median Views, via EasyKOL";
}

a[aria-selected="false"] .efluns-hover-container{
    display: none !important;
}

.__efluns_post_tags{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    position: absolute;
    bottom: -37px;
    max-width: 100%;
    white-space: break-spaces;
}

div:has(> .__efluns_post_tags){
    margin-bottom: 40px;
}

/* 隐藏控制 */
body[er] .__efluns_er{
    display: none;
}

body[plays] .__efluns_plays{
    display: none;
}

body[likes] .__efluns_er_likes{
    display: none;
}

body[comments] .__efluns_comments{
    display: none;
}

body[publishTime] .__efluns_created_time>span{
    display: none;
}


