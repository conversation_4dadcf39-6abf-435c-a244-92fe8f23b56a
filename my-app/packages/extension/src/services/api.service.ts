import { ENV } from '../config/environment';
import { authService } from './auth.service';

/**
 * 统一响应格式接口 - 与后端保持一致
 */
export interface ApiResponse<T = any> {
  code: number;           // 自定义业务状态码
  message: string;        // 提示信息
  error?: any;            // 错误信息
  data?: T;              // 返回数据
}

/**
 * 业务状态码常量 - 与后端保持一致
 */
export const ResponseCode = {
  SUCCESS: 1000,          // 成功
  PARAM_ERROR: 1400,      // 参数错误
  QUOTA_EXCEEDED: 1403,   // 配额不足
  NOT_FOUND: 1404,        // 资源未找到
  SERVER_ERROR: 1500,     // 服务器错误
} as const;

class ApiService {
  private baseUrl: string;
  private requestTimeout: number;

  constructor() {
    // Use environment configuration
    this.baseUrl = ENV.apiUrl;
    this.requestTimeout = ENV.requestTimeout;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    try {
      // 获取认证令牌
      const token = await authService.getAccessToken();

      // Add timeout to fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...options.headers as Record<string, string>,
      };

      // 添加认证头
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      let data: ApiResponse<T>;
      try {
        data = await response.json();
      } catch (e) {
        // 如果无法解析 JSON，使用 HTTP 状态信息
        if (!response.ok) {
          const statusText = response.statusText || `请求失败，状态码: ${response.status}`;
          throw new Error(statusText);
        }
        throw new Error('解析响应数据失败');
      }

      // 检查业务状态码（包括 HTTP 错误状态码的情况）
      if (data.code !== ResponseCode.SUCCESS) {
        throw new Error(
          data.message || '请求失败'
        );
      }

      return data.data as T;
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.error('API请求超时:', url);
        throw new Error('请求超时，请确保后端服务正在运行');
      } else if (error instanceof TypeError && error.message.includes('NetworkError')) {
        console.error('网络错误:', url);
        throw new Error('网络连接错误，请确保后端服务正在运行');
      } else {
        console.error('API请求失败:', error);
        throw error;
      }
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, body: unknown): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });
  }

  async put<T>(endpoint: string, body: unknown): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(body),
    });
  }

  async delete<T>(endpoint: string, body?: unknown): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
      body: body ? JSON.stringify(body) : undefined
    });
  }
}

export const apiService = new ApiService();