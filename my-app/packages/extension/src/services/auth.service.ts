import { createClient } from '@supabase/supabase-js';
import { ENV } from '../config/environment';
import { apiService } from './api.service';

// 创建 Supabase 客户端（使用环境配置）
const supabase = createClient(ENV.supabase.url, ENV.supabase.anonKey);

export interface User {
  id: string;
  email?: string;
  name?: string;
  isAdmin: boolean;
  totalQuota: number;
  usedQuota: number;
  remainingQuota: number;
}

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  user: User;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupCredentials {
  email: string;
  password: string;
  name?: string;
}

class AuthService {
  private readonly STORAGE_KEY = 'auth_session';

  /**
   * 检查扩展是否正常运行
   */
  private checkExtensionContext(): void {
    if (!chrome || !chrome.storage) {
      throw new Error('Extension context invalidated. Please reload the extension.');
    }
  }

  /**
   * 注册新用户
   */
  async signup(credentials: SignupCredentials): Promise<AuthSession> {
    try {
      // 使用后端 API 进行注册
      const response = await apiService.post<{
        user: any;
        session: any;
        message: string;
      }>('/auth/signup', {
        email: credentials.email,
        password: credentials.password,
        name: credentials.name,
      });

      if (response.session) {
        const session: AuthSession = {
          access_token: response.session.access_token,
          refresh_token: response.session.refresh_token,
          expires_in: response.session.expires_in,
          token_type: response.session.token_type,
          user: {
            id: response.user.id,
            email: response.user.email,
            name: response.user.user_metadata?.name || credentials.name,
            isAdmin: false,
            totalQuota: ENV.defaultQuota.total,
            usedQuota: ENV.defaultQuota.used,
            remainingQuota: ENV.defaultQuota.total,
          },
        };

        await this.saveSession(session);
        return session;
      } else {
        throw new Error(response.message || '注册成功！请检查您的邮箱并点击验证链接，然后重新登录。');
      }
    } catch (error: any) {
      // 处理网络错误
      if (error.message?.includes('Failed to fetch') || error.message?.includes('NetworkError')) {
        throw new Error('网络连接失败，请检查网络后重试');
      }

      // 处理后端返回的错误
      if (error.message) {
        throw new Error(error.message);
      }

      throw new Error('注册失败，请重试');
    }
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<AuthSession> {
    try {
      this.checkExtensionContext();
      // 使用后端 API 进行登录
      const response = await apiService.post<{
        user: any;
        session: any;
      }>('/auth/login', {
        email: credentials.email,
        password: credentials.password,
      });
      if (!response.session) {
        throw new Error('登录失败，请检查邮箱和密码，或确认邮箱已验证');
      }

      const session: AuthSession = {
        access_token: response.session.access_token,
        refresh_token: response.session.refresh_token,
        expires_in: response.session.expires_in,
        token_type: response.session.token_type,
        user: {
          id: response.user.id,
          email: response.user.email,
          name: response.user.user_metadata?.name,
          isAdmin: false,
          totalQuota: ENV.defaultQuota.total,
          usedQuota: ENV.defaultQuota.used,
          remainingQuota: ENV.defaultQuota.total,
        },
      };

      await this.saveSession(session);
      return session;
    } catch (error: any) {
      // 处理扩展相关错误
      if (error.message?.includes('Extension context invalidated')) {
        throw new Error('扩展需要重新加载。请刷新页面或重新启动扩展。');
      }

      // 处理网络错误
      if (error.message?.includes('Failed to fetch') || error.message?.includes('NetworkError')) {
        throw new Error('网络连接失败，请检查网络后重试');
      }

      // 处理后端返回的错误
      if (error.message) {
        throw new Error(error.message);
      }

      throw new Error('登录失败，请重试');
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    await supabase.auth.signOut();
    await this.clearSession();
  }

  /**
   * 获取当前用户信息（从后端 API）
   */
  async getCurrentUser(): Promise<User | null> {
    const session = await this.getSession();
    if (!session) {
      return null;
    }

    try {
      const userData = await apiService.get<{
        id: string;
        email: string;
        name: string;
        isAdmin: boolean;
        totalQuota: number;
        usedQuota: number;
        remainingQuota: number;
      }>('/auth/me');

      return {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        isAdmin: userData.isAdmin,
        totalQuota: userData.totalQuota,
        usedQuota: userData.usedQuota,
        remainingQuota: userData.remainingQuota,
      };
    } catch (error) {
      console.error('Failed to get current user:', error);

      if (error instanceof Error && error.message.includes('401')) {
        await this.clearSession();
        return null;
      }

      throw error;
    }
  }

  /**
   * 检查是否已登录
   */
  async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user !== null;
  }

  /**
   * 获取访问令牌
   */
  async getAccessToken(): Promise<string | null> {
    const session = await this.getSession();
    return session?.access_token || null;
  }

  /**
   * 保存会话到 Chrome 存储
   */
  private async saveSession(session: AuthSession): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.local.set({ [this.STORAGE_KEY]: session }, () => {
        resolve();
      });
    });
  }

  /**
   * 从 Chrome 存储获取会话
   */
  private async getSession(): Promise<AuthSession | null> {
    return new Promise((resolve) => {
      chrome.storage.local.get([this.STORAGE_KEY], (result) => {
        resolve(result[this.STORAGE_KEY] || null);
      });
    });
  }

  /**
   * 清除会话
   */
  private async clearSession(): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.local.remove([this.STORAGE_KEY], () => {
        resolve();
      });
    });
  }
}

export const authService = new AuthService();
