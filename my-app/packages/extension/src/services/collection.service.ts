import { apiService } from './api.service';

export interface Collection {
  id: string;
  userId: string;
  name: string;
  platform: string;
  result: {
    influencers: Array<{
      id: string;
      username: string;
      full_name?: string;
      profile_pic_url?: string;
      is_verified: boolean;
      is_private: boolean;
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CollectionSummary {
  id: string;
  name: string;
  platform: string;
}

class CollectionService {
  // 获取用户的所有集合
  async getUserCollections(): Promise<Collection[]> {
    try {
      const response = await apiService.get<{
        collections: Collection[];
        meta: {
          total: number;
        };
      }>('/collections');

      return response.collections || [];
    } catch (error) {
      console.error('Failed to fetch collections:', error);
      throw error;
    }
  }

  // 获取集合详情
  async getCollection(collectionId: string): Promise<Collection | null> {
    try {
      const response = await apiService.get<{
        collection: Collection;
      }>(`/collections/${collectionId}`);

      return response.collection || null;
    } catch (error) {
      console.error('Failed to fetch collection:', error);
      throw error;
    }
  }

  // 获取集合中未查看的博主
  async getUnviewedInfluencers(collectionId: string) {
    try {
      const response = await apiService.get<{
        influencers: Array<{
          id: string;
          username: string;
          full_name?: string;
          profile_pic_url?: string;
          is_verified: boolean;
          is_private: boolean;
        }>;
        meta: {
          total: number;
          collectionId: string;
        };
      }>(`/collections/${collectionId}/unviewed`);

      return response;
    } catch (error) {
      console.error('Failed to fetch unviewed influencers:', error);
      throw error;
    }
  }

  // 记录用户行为（查看博主）
  async recordUserAction(
    collectionId: string,
    influencerUsername: string,
    platformUserId: string,
    actionType: 'viewed' | 'skipped',
    platform: string = 'instagram'
  ): Promise<void> {
    try {
      await apiService.post('/collections/actions', {
        collectionId,
        influencerUsername,
        platformUserId,
        actionType,
        platform
      });
    } catch (error) {
      console.error('Failed to record user action:', error);
      throw error;
    }
  }

  // 添加到收藏
  async addToFavorites(
    collectionId: string,
    influencerUsername: string,
    platformUserId: string,
    platform: string = 'instagram'
  ): Promise<void> {
    try {
      await apiService.post('/collections/favorites', {
        collectionId,
        influencerUsername,
        platformUserId,
        platform
      });
    } catch (error) {
      console.error('Failed to add to favorites:', error);
      throw error;
    }
  }

  // 移除收藏
  async removeFromFavorites(
    collectionId: string,
    influencerUsername: string
  ): Promise<void> {
    try {
      await apiService.delete('/collections/favorites', {
        collectionId,
        influencerUsername
      });
    } catch (error) {
      console.error('Failed to remove from favorites:', error);
      throw error;
    }
  }

  // 获取收藏列表
  async getFavorites(collectionId: string): Promise<string[]> {
    try {
      const response = await apiService.get<{
        favorites: string[];
        meta: {
          total: number;
          collectionId: string;
        };
      }>(`/collections/${collectionId}/favorites`);

      return response.favorites || [];
    } catch (error) {
      console.error('Failed to fetch favorites:', error);
      throw error;
    }
  }

  // 导出全部数据为Excel（包含全部数据和收藏列表两个sheet）
  async exportAllDataToExcel(collectionId: string): Promise<{
    sheets: {
      '全部数据': Array<{ fullname: string, url: string }>,
      '收藏列表': Array<{ fullname: string, url: string }>
    },
    meta: {
      totalAll: number,
      totalFavorites: number,
      collectionId: string
    }
  }> {
    try {
      const response = await apiService.get<{
        sheets: {
          '全部数据': Array<{ fullname: string, url: string }>;
          '收藏列表': Array<{ fullname: string, url: string }>;
        };
        meta: {
          totalAll: number;
          totalFavorites: number;
          collectionId: string;
        };
      }>(`/collections/${collectionId}/export-excel`);

      return response;
    } catch (error) {
      console.error('Failed to export data:', error);
      throw error;
    }
  }
}

export const collectionService = new CollectionService();
