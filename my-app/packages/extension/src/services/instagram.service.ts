import { apiService } from './api.service';

export interface Influencer {
  id: string;
  username: string;
  full_name?: string;
  profile_pic_url?: string;
  is_verified: boolean;
  is_private: boolean;
}

export interface InstagramApiResponse {
  influencers: Influencer[];
  collection: {
    id: string;
    name: string;
    platform: string;
  };
  meta: {
    total: number;
    totalInCollection: number;
    viewedCount: number;
    username: string;
    collectionId: string;
  };
  quota?: {
    totalQuota: number;
    usedQuota: number;
    remainingQuota: number;
  };
}

export class QuotaExceededError extends Error {
  public quotaInfo: {
    totalQuota: number;
    usedQuota: number;
    remainingQuota: number;
  };

  constructor(message: string, quotaInfo: any) {
    super(message);
    this.name = 'QuotaExceededError';
    this.quotaInfo = quotaInfo;
  }
}

class InstagramService {
  async getRelatedInfluencers(username: string, collectionId?: string): Promise<InstagramApiResponse> {
    try {
      const url = `/instagram/influencers/${encodeURIComponent(username)}${
        collectionId ? `?collectionId=${collectionId}` : ''
      }`;

      const response = await apiService.get<InstagramApiResponse>(url);

      return response;
    } catch (error: any) {
      console.error('Failed to fetch influencers:', error);

      // 检查是否是配额不足错误
      if (error.response?.status === 403) {
        const errorData = error.response.data;
        console.log('403 错误详情:', errorData); // 调试日志

        // 检查业务码格式 (code: 1403)
        if (errorData && errorData.code === 1403) {
          const quotaInfo = errorData.error || {
            totalQuota: 5,
            usedQuota: 5,
            remainingQuota: 0
          };

          console.log('抛出 QuotaExceededError:', quotaInfo); // 调试日志

          throw new QuotaExceededError(
            errorData.message || '配额已用完，请联系管理员升级套餐',
            quotaInfo
          );
        }
      }

      throw error;
    }
  }
}

export const instagramService = new InstagramService();