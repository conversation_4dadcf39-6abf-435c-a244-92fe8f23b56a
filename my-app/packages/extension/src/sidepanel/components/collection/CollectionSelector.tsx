import React, { useState, useEffect } from 'react';
import { ChevronDown, Folder, Plus } from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { collectionService, type Collection } from '../../../services/collection.service';

interface CollectionSelectorProps {
  currentCollectionId?: string;
  onCollectionChange: (collectionId: string) => void;
  onStartNewCollection?: () => void;
}

export const CollectionSelector: React.FC<CollectionSelectorProps> = ({
  currentCollectionId,
  onCollectionChange,
  onStartNewCollection
}) => {
  const [collections, setCollections] = useState<Collection[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const currentCollection = collections.find(c => c.id === currentCollectionId);

  useEffect(() => {
    loadCollections();
  }, []);

  // 监听 currentCollectionId 变化，当有新集合时重新加载列表
  useEffect(() => {
    if (currentCollectionId && !currentCollection) {
      loadCollections();
    }
  }, [currentCollectionId, currentCollection]);

  const loadCollections = async () => {
    try {
      setLoading(true);
      const userCollections = await collectionService.getUserCollections();
      setCollections(userCollections);
    } catch (error) {
      console.error('Failed to load collections:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCollectionSelect = (collectionId: string) => {
    onCollectionChange(collectionId);
    setIsOpen(false);
  };

  const handleStartNewCollection = () => {
    if (onStartNewCollection) {
      onStartNewCollection();
    }
    setIsOpen(false);
  };

  // 处理下拉框打开，每次打开时刷新集合列表
  const handleToggleOpen = async () => {
    if (!isOpen) {
      // 打开时刷新集合列表
      await loadCollections();
    }
    setIsOpen(!isOpen);
  };

  // 总是显示选择器，即使没有集合也要显示"开启新集合"选项
  const shouldShowSelector = collections.length > 0 || onStartNewCollection;

  if (!shouldShowSelector) {
    return null;
  }

  return (
    <div className="relative w-full">
      <Button
        onClick={handleToggleOpen}
        variant="outline"
        size="sm"
        className="w-full flex items-center justify-between gap-2 bg-white/90 backdrop-blur-sm border border-gray-200/50 text-gray-700 hover:bg-white hover:border-blue-300 hover:shadow-xl transition-all duration-300 ease-out rounded-xl py-3 shadow-md transform hover:scale-[1.02] active:scale-[0.98]"
        disabled={loading}
      >
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <div className="w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
            <Folder className="w-3 h-3 text-white" />
          </div>
          <span className="text-sm font-semibold truncate min-w-0">
            {currentCollection ? `@${currentCollection.name}` : '选择集合'}
          </span>
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform duration-300 ease-out ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* 下拉菜单 */}
          <div className="absolute top-full left-0 mt-2 w-full min-w-[280px] bg-white/95 backdrop-blur-md rounded-xl shadow-2xl border border-gray-100/50 z-20 overflow-hidden animate-in slide-in-from-top-2 fade-in duration-300 ease-out">
            <div className="py-3">
              <div className="px-5 py-3 text-xs font-bold text-gray-400 uppercase tracking-widest border-b border-gray-100/50">
                博主集合
              </div>

              <div className="max-h-64 overflow-y-auto py-2">
                {/* 开启新集合选项 - 放在最上方 */}
                {onStartNewCollection && (
                  <button
                    onClick={handleStartNewCollection}
                    className="w-full mx-2 mb-2 px-4 py-4 text-left rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg group bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center transition-all duration-300">
                        <Plus className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-bold text-sm">开启新集合</div>
                        <div className="text-xs text-white/80 mt-1">
                          开始查找新的博主集合
                        </div>
                      </div>
                    </div>
                  </button>
                )}

                {/* 分隔线 - 只有当既有新集合按钮又有现有集合时才显示 */}
                {onStartNewCollection && collections.length > 0 && (
                  <div className="border-t border-gray-100/50 mx-2 my-3"></div>
                )}

                {/* 现有集合列表 */}
                {collections.length > 0 && collections.map((collection) => (
                  <button
                    key={collection.id}
                    onClick={() => handleCollectionSelect(collection.id)}
                    className={`w-full mx-2 mb-2 px-4 py-4 text-left rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg group ${
                      currentCollectionId === collection.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                        : 'bg-white/80 hover:bg-white text-gray-700 border border-gray-100/50 hover:border-blue-200'
                    }`}
                  >
                    <div className="flex items-center gap-4">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ${
                        currentCollectionId === collection.id
                          ? 'bg-white/20 backdrop-blur-sm'
                          : 'bg-gradient-to-r from-blue-500 to-purple-600'
                      }`}>
                        <div className={`w-3 h-3 rounded-full ${
                          currentCollectionId === collection.id
                            ? 'bg-white'
                            : 'bg-white'
                        }`} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className={`font-bold text-sm truncate transition-all duration-300 ${
                          currentCollectionId === collection.id
                            ? 'text-white'
                            : 'text-gray-800 group-hover:text-blue-700'
                        }`}>
                          @{collection.name}
                        </div>
                        <div className={`text-xs mt-1 transition-all duration-300 ${
                          currentCollectionId === collection.id
                            ? 'text-white/80'
                            : 'text-gray-500 group-hover:text-blue-500'
                        }`}>
                          {collection.result.influencers.length} 个博主
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
