import React, { useState } from 'react';
import { Download, Loader2 } from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { collectionService } from '../../../services/collection.service';
import * as XLSX from 'xlsx';

interface ExportButtonProps {
  collectionId: string;
  collectionName: string;
}



export const ExportButton: React.FC<ExportButtonProps> = ({
  collectionId,
  collectionName
}) => {
  const [loading, setLoading] = useState(false);

  const handleExport = async () => {
    try {
      setLoading(true);

      // 调用后端获取数据
      const response = await collectionService.exportAllDataToExcel(collectionId);

      // 创建Excel工作簿
      const workbook = XLSX.utils.book_new();

      // 创建第一个工作表：全部数据
      const allDataWorksheetData = [
        ['fullname', 'url'], // 表头
        ...response.sheets['全部数据'].map(item => [item.fullname, item.url]) // 数据行
      ];

      const allDataWorksheet = XLSX.utils.aoa_to_sheet(allDataWorksheetData);

      // 设置列宽
      allDataWorksheet['!cols'] = [{ width: 30 }, { width: 50 }];

      // 设置超链接格式
      response.sheets['全部数据'].forEach((item, index) => {
        const cellRef = XLSX.utils.encode_cell({ r: index + 1, c: 1 });
        if (allDataWorksheet[cellRef]) {
          allDataWorksheet[cellRef].l = { Target: item.url, Tooltip: `访问 ${item.url}` };
        }
      });

      // 创建第二个工作表：收藏列表
      const favoritesWorksheetData = [
        ['fullname', 'url'], 
        ...response.sheets['收藏列表'].map(item => [item.fullname, item.url]) // 数据行
      ];

      const favoritesWorksheet = XLSX.utils.aoa_to_sheet(favoritesWorksheetData);

      // 设置列宽
      favoritesWorksheet['!cols'] = [{ width: 30 }, { width: 50 }];

      // 设置超链接格式
      response.sheets['收藏列表'].forEach((item, index) => {
        const cellRef = XLSX.utils.encode_cell({ r: index + 1, c: 1 });
        if (favoritesWorksheet[cellRef]) {
          favoritesWorksheet[cellRef].l = { Target: item.url, Tooltip: `访问 ${item.url}` };
        }
      });

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, allDataWorksheet, '全部数据');
      XLSX.utils.book_append_sheet(workbook, favoritesWorksheet, '收藏列表');

      // 生成Excel文件并下载
      XLSX.writeFile(workbook, `collection_export_${collectionName}.xlsx`);

    } catch (error) {
      console.error('导出Excel失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={handleExport}
      variant="ghost"
      size="sm"
      className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-md hover:shadow-xl transition-all duration-300 ease-out transform hover:-translate-y-1 hover:scale-105 rounded-xl px-4 py-2.5 active:scale-95 active:translate-y-0"
      disabled={loading}
    >
      <div className={`transition-transform duration-300 ${loading ? 'animate-spin' : 'group-hover:rotate-12'}`}>
        {loading ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Download className="w-4 h-4" />
        )}
      </div>
      <span className="text-sm font-semibold whitespace-nowrap">
        导出全部
      </span>
    </Button>
  );
};
