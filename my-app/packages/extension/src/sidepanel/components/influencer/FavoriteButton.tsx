import React, { useState } from 'react';
import { Heart, Loader2 } from 'lucide-react';
import { Button } from '../../../components/ui/button';

interface FavoriteButtonProps {
  isFavorited: boolean;
  onToggleFavorite: () => Promise<void>;
  disabled?: boolean;
}

export const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  isFavorited,
  onToggleFavorite,
  disabled = false
}) => {
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    if (loading || disabled) return;
    
    try {
      setLoading(true);
      await onToggleFavorite();
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={handleClick}
      disabled={loading || disabled}
      className={`flex-1 flex items-center justify-center gap-2 py-3.5 rounded-xl transition-all duration-300 ease-out font-semibold shadow-md transform hover:-translate-y-1 hover:scale-105 active:scale-95 active:translate-y-0 ${
        isFavorited
          ? 'bg-gradient-to-r from-pink-500 to-red-500 text-white hover:from-pink-600 hover:to-red-600 shadow-lg hover:shadow-xl'
          : 'bg-white/90 backdrop-blur-sm border border-gray-200/50 text-gray-700 hover:bg-white hover:border-pink-300 hover:shadow-xl'
      }`}
    >
      {loading ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="transition-opacity duration-300">处理中...</span>
        </>
      ) : (
        <>
          <Heart className={`h-4 w-4 transition-all duration-300 ${isFavorited ? 'fill-current scale-110' : 'hover:scale-110'}`} />
          <span className="transition-all duration-300">{isFavorited ? '已收藏' : '收藏'}</span>
        </>
      )}
    </Button>
  );
};
