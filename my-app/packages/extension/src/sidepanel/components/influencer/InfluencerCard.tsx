import React from 'react';
import { CheckCircle2, ExternalLink } from 'lucide-react';
import { Card, CardContent } from '../../../components/ui/card';
import { Badge } from '../../../components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '../../../components/ui/avatar';
import { Influencer } from '../../../services/instagram.service';
import { getProxiedImageUrl } from '../../../utils/image-proxy';

interface InfluencerCardProps {
  influencer: Influencer | null;
  imageLoadErrors: Set<string>;
  onImageError: (username: string) => void;
  onClick: (username: string) => void;
  loadingMore?: boolean;
}

export const InfluencerCard: React.FC<InfluencerCardProps> = ({
  influencer,
  imageLoadErrors,
  onImageError,
  onClick,
  loadingMore = false
}) => {
  // 如果正在加载更多，显示加载状态
  if (loadingMore) {
    return (
      <Card className="border-0 shadow-2xl overflow-hidden bg-white/90 backdrop-blur-md">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-purple-400/10 to-pink-400/10 opacity-50"></div>
        <CardContent className="pt-6 pb-6 relative">
          <div className="flex flex-col items-center space-y-5">
            <div className="relative">
              <div className="h-28 w-28 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse"></div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20 blur-xl"></div>
            </div>
            <div className="space-y-3 flex flex-col items-center">
              <div className="h-5 w-36 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
              <div className="h-4 w-28 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 如果没有影响者数据，返回空状态
  if (!influencer) {
    return null;
  }

  return (
    <Card
      className="border-0 shadow-2xl overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-[0_20px_70px_-15px_rgba(0,0,0,0.3)] hover:scale-[1.02] bg-white/90 backdrop-blur-md"
      onClick={() => onClick(influencer.username)}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-purple-400/10 to-pink-400/10 opacity-50"></div>
      <CardContent className="pt-6 pb-6 relative">
        <div className="absolute top-3 right-3 bg-white/80 backdrop-blur-sm rounded-full p-2 shadow-sm">
          <ExternalLink className="h-4 w-4 text-gray-600" />
        </div>

        <div className="flex flex-col items-center space-y-5">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-600 rounded-full blur-xl opacity-25"></div>
            <Avatar className="h-28 w-28 ring-4 ring-white shadow-xl relative overflow-hidden">
              {!imageLoadErrors.has(influencer.username) && influencer.profile_pic_url && (
                <AvatarImage
                  src={getProxiedImageUrl(influencer.profile_pic_url)}
                  alt={influencer.username}
                  onError={() => onImageError(influencer.username)}
                  crossOrigin="anonymous"
                />
              )}
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                <div className="flex flex-col items-center justify-center h-full">
                  <span className="text-4xl font-bold">
                    {influencer.username[0]?.toUpperCase()}
                  </span>
                  {influencer.is_verified && (
                    <CheckCircle2 className="h-4 w-4 mt-1" />
                  )}
                </div>
              </AvatarFallback>
            </Avatar>
            {influencer.is_verified && (
              <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-0.5 shadow-lg">
                <CheckCircle2 className="h-6 w-6 text-blue-500" />
              </div>
            )}
          </div>

          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2">
              <h2 className="font-bold text-xl text-gray-900">@{influencer.username}</h2>
              {influencer.is_verified && (
                <CheckCircle2 className="h-5 w-5 text-blue-500" />
              )}
            </div>
            <p className="text-sm text-gray-600 font-medium">{influencer.full_name || 'Instagram User'}</p>

            {influencer.is_private && (
              <Badge className="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 border-0 font-medium">私密账号</Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
