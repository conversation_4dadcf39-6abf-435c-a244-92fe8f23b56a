import React from 'react';
import { ChevronRight, Loader2, RefreshCw } from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { FavoriteButton } from './FavoriteButton';

interface InfluencerControlsProps {
  currentIndex: number;
  totalCount: number;
  loadingMore: boolean;
  isFavorited: boolean;
  onNext: () => void;
  onLoadMore: () => void;
  onToggleFavorite: () => Promise<void>;
}

export const InfluencerControls: React.FC<InfluencerControlsProps> = ({
  currentIndex,
  totalCount,
  loadingMore,
  isFavorited,
  onNext,
  onLoadMore,
  onToggleFavorite
}) => {
  return (
    <div className="flex flex-col gap-3">
      {/* 第一行：下一个和收藏 */}
      <div className="flex gap-3">
        <Button
          onClick={onNext}
          disabled={currentIndex >= totalCount - 1}
          className="flex-1 group flex items-center justify-center gap-2 bg-white/90 backdrop-blur-sm border border-gray-200/50 text-gray-700 hover:bg-white hover:border-blue-300 hover:shadow-lg py-3.5 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all font-semibold shadow-md"
        >
          下一个
          <ChevronRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform" />
        </Button>

        <FavoriteButton
          isFavorited={isFavorited}
          onToggleFavorite={onToggleFavorite}
        />
      </div>

      {/* 第二行：更多推荐 */}
      <Button
        onClick={onLoadMore}
        disabled={loadingMore}
        className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3.5 rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all font-semibold shadow-lg hover:shadow-xl disabled:opacity-70 transform hover:-translate-y-0.5"
      >
        {loadingMore ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            扩展中...
          </>
        ) : (
          <>
            更多推荐
            <RefreshCw className="h-4 w-4" />
          </>
        )}
      </Button>
    </div>
  );
};
