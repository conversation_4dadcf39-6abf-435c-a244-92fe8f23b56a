import React from 'react';
import { Loader2, Sparkles } from 'lucide-react';
import { Badge } from '../../../components/ui/badge';

interface InfluencerStatsProps {
  currentIndex: number;
  totalCount: number;
  sourceUsernames: string[];
  loadingMore: boolean;
}

export const InfluencerStats: React.FC<InfluencerStatsProps> = ({
  currentIndex,
  totalCount,
  sourceUsernames,
  loadingMore
}) => {
  return (
    <div className="text-center space-y-3">
      <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">发现相似账号</h1>
      <div className="flex items-center justify-center gap-3">
        <Badge className="bg-white/90 backdrop-blur-sm text-gray-800 border-0 shadow-lg px-6 py-3 text-xl font-bold">
          <span className="text-2xl font-extrabold text-blue-600">{currentIndex + 1}</span>
          <span className="text-gray-400 mx-2 text-lg">/</span>
          <span className="text-xl font-bold text-purple-600">{totalCount}</span>
        </Badge>
        {loadingMore && (
          <Badge className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0 shadow-sm">
            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            扩展中
          </Badge>
        )}
      </div>
      
      {sourceUsernames.length > 0 && (
        <div className={`py-3 px-4 bg-white/80 backdrop-blur-sm rounded-xl shadow-md transition-opacity duration-500 ${loadingMore ? 'animate-pulse' : ''}`}>
          <div className="flex items-center justify-center gap-2">
            <Sparkles className="h-4 w-4 text-purple-500" />
            <div className="text-sm font-medium text-gray-700">
              <span>基于 </span>
              {sourceUsernames.map((username, index) => (
                <span key={username}>
                  <span className="text-purple-600 font-semibold">@{username}</span>
                  {index < sourceUsernames.length - 1 && <span className="text-gray-500"> / </span>}
                </span>
              ))} 
              <span> 的推荐</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
