import React from 'react';
import { CheckCircle2, RefreshCw, Search } from 'lucide-react';
import { Button } from '../../../components/ui/button';

interface CollectionEmptyStateProps {
  collectionName?: string;
  onRetry: () => void;
  onSearchNew: () => void;
}

export const CollectionEmptyState: React.FC<CollectionEmptyStateProps> = ({
  collectionName,
  onRetry,
  onSearchNew
}) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] px-6 text-center">
      <div className="relative mb-8">
        <div className="w-24 h-24 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center">
          <CheckCircle2 className="w-12 h-12 text-green-500" />
        </div>
        <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
          <span className="text-white text-xs font-bold">✓</span>
        </div>
      </div>

      <div className="space-y-4 max-w-sm">
        <h2 className="text-2xl font-bold text-gray-900">
          太棒了！
        </h2>
        
        <div className="space-y-2">
          <p className="text-gray-600">
            {collectionName ? (
              <>集合 <span className="font-semibold text-blue-600">@{collectionName}</span> 中的所有博主都已查看完毕！</>
            ) : (
              '当前集合中的所有博主都已查看完毕！'
            )}
          </p>
          <p className="text-sm text-gray-500">
            你可以刷新查看是否有新的博主，或者搜索新的相似账号。
          </p>
        </div>

        <div className="flex flex-col gap-3 pt-4">
          <Button
            onClick={onRetry}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            刷新集合
          </Button>
          
          <Button
            onClick={onSearchNew}
            className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
          >
            <Search className="w-4 h-4" />
            查找相似账号
          </Button>
        </div>
      </div>
    </div>
  );
};
