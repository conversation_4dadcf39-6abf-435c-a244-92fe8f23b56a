import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { Card, CardContent } from '../../../components/ui/card';

interface EmptyStateProps {
  onRetry: () => void;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ onRetry }) => {
  return (
    <div className="flex items-center justify-center p-6 pt-12">
      <Card className="w-full max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-md">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-400/5 via-purple-400/5 to-pink-400/5 rounded-2xl"></div>
        <CardContent className="pt-10 pb-10 relative">
          <div className="flex flex-col items-center space-y-8 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-400 rounded-3xl blur-2xl opacity-20"></div>
              <div className="relative w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center shadow-xl">
                <AlertCircle className="h-12 w-12 text-gray-500" />
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="font-bold text-xl text-gray-700">没有找到相似账号</h3>
              <p className="text-gray-600">请尝试其他账号</p>
            </div>
            <Button 
              onClick={onRetry}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 py-4 text-base font-semibold shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-0.5 rounded-xl"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重新尝试
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
