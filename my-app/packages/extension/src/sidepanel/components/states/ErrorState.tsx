import React from 'react';
import { AlertCircle } from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { Card, CardContent } from '../../../components/ui/card';

interface ErrorStateProps {
  error: string;
  retryCount: number;
  onRetry: () => void;
  onCheckBackend: () => void;
}

export const ErrorState: React.FC<ErrorStateProps> = ({ 
  error, 
  retryCount, 
  onRetry, 
  onCheckBackend 
}) => {
  return (
    <div className="flex flex-col items-center justify-center p-6 pt-12">
      <Card className="w-full max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-md">
        <div className="absolute inset-0 bg-gradient-to-br from-red-400/5 via-orange-400/5 to-pink-400/5 rounded-2xl"></div>
        <CardContent className="pt-10 pb-10 relative">
          <div className="flex flex-col items-center space-y-8 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-red-400 to-orange-500 rounded-3xl blur-2xl opacity-30"></div>
              <div className="relative w-24 h-24 bg-gradient-to-br from-red-100 to-orange-100 rounded-3xl flex items-center justify-center shadow-xl">
                <AlertCircle className="h-12 w-12 text-red-500" />
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="font-bold text-2xl text-gray-900">连接错误</h3>
              <p className="text-base text-gray-600">{error}</p>
            </div>
            <Button 
              onClick={retryCount > 2 ? onCheckBackend : onRetry}
              className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white border-0 py-4 text-base font-semibold shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-0.5 rounded-xl"
            >
              {retryCount > 2 ? '检查后端服务' : '重试'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
