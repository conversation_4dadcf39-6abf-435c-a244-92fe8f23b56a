import React from 'react';
import { Search } from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { Card, CardContent } from '../../../components/ui/card';

interface InitialStateProps {
  currentUsername: string | null;
  onSearchClick: () => void;
  hasCollections?: boolean;
}

export const InitialState: React.FC<InitialStateProps> = ({ currentUsername, onSearchClick, hasCollections = false }) => {
  return (
    <div className="flex flex-col items-center justify-center p-6 pt-12">
      <Card className="w-full max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-md">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400/5 via-purple-400/5 to-pink-400/5 rounded-2xl"></div>
        <CardContent className="pt-6 pb-10 relative">
          <div className="flex flex-col items-center space-y-8 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-600 rounded-3xl blur-2xl opacity-30"></div>
              <div className="relative w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-xl transform hover:scale-105 transition-transform">
                <Search className="h-12 w-12 text-white" />
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="font-bold text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Instagram Influencer Finder</h3>
              <p className="text-base text-gray-600 font-medium">
                {hasCollections ? (
                  currentUsername
                    ? <span>选择上方集合查看博主，或为 <span className="text-blue-600 font-semibold">@{currentUsername}</span> 查找相似账号</span>
                    : '选择上方集合查看博主，或点击按钮获取当前页面信息'
                ) : (
                  currentUsername
                    ? <span>为 <span className="text-blue-600 font-semibold">@{currentUsername}</span> 查找相似账号</span>
                    : '点击按钮获取当前页面信息'
                )}
              </p>
            </div>
            <Button 
              onClick={onSearchClick}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 py-4 text-base font-semibold shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-0.5 rounded-xl"
            >
              查找相似账号
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
