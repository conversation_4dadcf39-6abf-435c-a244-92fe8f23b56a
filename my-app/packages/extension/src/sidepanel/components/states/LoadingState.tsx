import React from 'react';
import { Card, CardContent } from '../../../components/ui/card';

export const LoadingState: React.FC = () => {
  return (
    <div className="p-6 pt-12">
      <div className="max-w-md mx-auto space-y-6">
        <div className="text-center space-y-3">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">发现相似账号</h1>
          <div className="flex items-center justify-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse delay-75"></div>
            <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse delay-150"></div>
          </div>
        </div>
        
        <Card className="border-0 shadow-2xl overflow-hidden bg-white/90 backdrop-blur-md">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-purple-400/10 to-pink-400/10 opacity-50"></div>
          <CardContent className="pt-6 pb-6 relative">
            <div className="flex flex-col items-center space-y-5">
              <div className="relative">
                <div className="h-28 w-28 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse"></div>
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20 blur-xl"></div>
              </div>
              <div className="space-y-3 flex flex-col items-center">
                <div className="h-5 w-36 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
                <div className="h-4 w-28 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="flex flex-col gap-3">
          <div className="grid grid-cols-2 gap-3">
            <div className="h-12 bg-gradient-to-r from-gray-200 to-gray-300 rounded-xl animate-pulse"></div>
            <div className="h-12 bg-gradient-to-r from-gray-200 to-gray-300 rounded-xl animate-pulse"></div>
          </div>
          <div className="h-14 bg-gradient-to-r from-blue-200 to-purple-200 rounded-xl animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};
