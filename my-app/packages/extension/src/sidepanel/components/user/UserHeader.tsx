import React from 'react';
import { User as AuthUser } from '../../../services/auth.service';
import { CollectionSelector } from '../collection/CollectionSelector';
import { ExportButton } from '../collection/ExportButton';
import { UserMenu } from './UserMenu';

interface UserHeaderProps {
  user: AuthUser;
  currentCollectionId?: string;
  currentCollectionName?: string;
  onLogout: () => void;
  onCollectionChange?: (collectionId: string) => void;
  onStartNewCollection?: () => void;
}

export const UserHeader: React.FC<UserHeaderProps> = ({
  user,
  currentCollectionId,
  currentCollectionName,
  onLogout,
  onCollectionChange,
  onStartNewCollection
}) => {
  return (
    <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-100 shadow-sm">
      <div className="max-w-md mx-auto px-4 py-3">
        {/* 第一行：品牌 logo 和用户菜单 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <img
              src="/icon-32.png"
              alt="WaveInflu"
              className="w-6 h-6 object-contain"
            />
            <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              WaveInflu
            </span>
          </div>
          <UserMenu
            user={user}
            onLogout={onLogout}
          />
        </div>

        {/* 第二行：集合选择器和导出按钮 */}
        {onCollectionChange && (
          <div className="flex items-center gap-3">
            <div className="flex-1 min-w-0">
              <CollectionSelector
                currentCollectionId={currentCollectionId}
                onCollectionChange={onCollectionChange}
                onStartNewCollection={onStartNewCollection}
              />
            </div>
            {currentCollectionId && currentCollectionName && (
              <div className="flex-shrink-0">
                <ExportButton
                  collectionId={currentCollectionId}
                  collectionName={currentCollectionName}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
