@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 防止水平滚动 */
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
  }

  /* 防止所有元素产生水平滚动 */
  html, body, #root {
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
  }

  /* 禁用触摸滑动手势 */
  * {
    touch-action: pan-y pinch-zoom;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 允许文本选择 */
  p, span, h1, h2, h3, h4, h5, h6, input, textarea {
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

/* 性能优化和流畅度提升 */
@layer utilities {
  /* 硬件加速 */
  .transform, .transition-transform, .animate-spin {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* 自定义流畅动画 */
  @keyframes slideInFromTop {
    from {
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .animate-in {
    animation: slideInFromTop 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .slide-in-from-top-2 {
    animation: slideInFromTop 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .fade-in {
    animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 优化滚动性能 */
  .overflow-y-auto {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* 按钮交互优化 */
  button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* 减少重绘 */
  .bg-gradient-to-r {
    background-attachment: fixed;
  }
}