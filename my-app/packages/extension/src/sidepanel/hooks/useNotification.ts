import { useState, useCallback } from 'react';
import { NotificationType } from '../components/ui/NotificationBar';

interface Notification {
  id: string;
  message: string;
  type: NotificationType;
}

export const useNotification = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const showNotification = useCallback((message: string, type: NotificationType = 'info') => {
    const id = Date.now().toString();
    const notification: Notification = { id, message, type };
    
    setNotifications(prev => [...prev, notification]);
    
    return id;
  }, []);

  const hideNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const showError = useCallback((message: string) => {
    return showNotification(message, 'error');
  }, [showNotification]);

  const showSuccess = useCallback((message: string) => {
    return showNotification(message, 'success');
  }, [showNotification]);

  const showInfo = useCallback((message: string) => {
    return showNotification(message, 'info');
  }, [showNotification]);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  return {
    notifications,
    showNotification,
    hideNotification,
    showError,
    showSuccess,
    showInfo,
    clearAll
  };
};
