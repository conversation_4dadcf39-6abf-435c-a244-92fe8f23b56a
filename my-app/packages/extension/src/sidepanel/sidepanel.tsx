import React, { useState, useEffect, useCallback } from 'react';
import ReactDOM from 'react-dom/client';
import { Loader2 } from 'lucide-react';
import { instagramService, type Influencer, QuotaExceededError } from '../services/instagram.service';
import { collectionService } from '../services/collection.service';
import { ENV } from '../config/environment';
import { authService, type User as AuthUser } from '../services/auth.service';
import { LoginForm } from '../components/auth/LoginForm';
import { UserHeader } from './components/user/UserHeader';
import { InitialState } from './components/states/InitialState';

import { LoadingState } from './components/states/LoadingState';
import { CollectionEmptyState } from './components/states/CollectionEmptyState';
import { EmptyState } from './components/states/EmptyState';
import { InfluencerStats } from './components/influencer/InfluencerStats';
import { InfluencerCard } from './components/influencer/InfluencerCard';
import { InfluencerControls } from './components/influencer/InfluencerControls';
import { NotificationBar } from './components/ui/NotificationBar';
import { useNotification } from './hooks/useNotification';
import './globals.css';

const SidePanel: React.FC = () => {
  // ==================== 状态管理 ====================
  // 影响者相关状态
  const [influencers, setInfluencers] = useState<Influencer[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [imageLoadErrors, setImageLoadErrors] = useState<Set<string>>(new Set());

  // 加载状态
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [authLoading, setAuthLoading] = useState(true);


  // 用户相关状态
  const [user, setUser] = useState<AuthUser | null>(null);
  const [currentUsername, setCurrentUsername] = useState<string | null>(null);

  // 应用状态
  const [hasSearched, setHasSearched] = useState(false);
  const [lastOpenedTabId, setLastOpenedTabId] = useState<number | null>(null);
  const [preloadedTabId, setPreloadedTabId] = useState<number | null>(null);
  const [preloadedUsername, setPreloadedUsername] = useState<string | null>(null);
  const [sourceUsernames, setSourceUsernames] = useState<string[]>([]);

  // 集合相关状态
  const [currentCollectionId, setCurrentCollectionId] = useState<string | null>(null);
  const [currentCollectionName, setCurrentCollectionName] = useState<string>('');
  const [favorites, setFavorites] = useState<string[]>([]);
  const [hasCollections, setHasCollections] = useState<boolean>(false);

  // 通知功能
  const { notifications, showError, hideNotification } = useNotification();

  // ==================== 工具函数 ====================
  const checkCurrentTab = useCallback(async (showErrorMessages = true) => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab.url && tab.url.includes('instagram.com')) {
        const username = extractUsername(tab.url);
        if (username) {
          setCurrentUsername(username);
          return username;
        } else {
          if (showErrorMessages) {
            showError('请前往Instagram个人主页使用此功能');
          }
          return null;
        }
      } else {
        if (showErrorMessages) {
          showError('请前往Instagram个人主页使用此功能');
        }
        return null;
      }
    } catch (err) {
      if (showErrorMessages) {
        showError('检查当前标签页失败');
      }
      return null;
    }
  }, []);

  // 刷新用户信息的函数
  const refreshUserInfo = useCallback(async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('刷新用户信息失败:', error);
    }
  }, []);

  // ==================== 副作用处理 ====================
  // 检查认证状态
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('认证检查失败:', error);
        setUser(null);
      } finally {
        setAuthLoading(false);
      }
    };

    checkAuth();
  }, []);

  useEffect(() => {
    // 从本地存储加载上次打开的标签页ID
    chrome.storage.local.get(['lastOpenedTabId'], (result) => {
      setLastOpenedTabId(result.lastOpenedTabId || null);
    });
  }, []);

  // 只有在用户登录后才检查当前标签页
  useEffect(() => {
    if (user && !authLoading) {
      // 只检查当前标签页，获取用户名，但不显示错误提示
      checkCurrentTab(false);
    }
  }, [user, authLoading, checkCurrentTab]);

  // 当用户开始查看当前博主时，预加载下一个博主
  useEffect(() => {
    if (influencers.length > 0 && currentIndex >= 0) {
      const nextIndex = currentIndex + 1;
      if (nextIndex < influencers.length) {
        const nextInfluencer = influencers[nextIndex];
        if (nextInfluencer) {
          // 延迟一点预加载，确保当前页面已经打开
          setTimeout(() => {
            preloadNextProfile(nextInfluencer.username);
          }, 1000);
        }
      }
    }
  }, [currentIndex, influencers]);

  const extractUsername = (url: string): string | null => {
    const match = url.match(/instagram\.com\/([^/?]+)/);
    return match && match[1] !== '' && match[1] !== 'explore' && match[1] !== 'direct' ? match[1] : null;
  };

  // ==================== API 调用函数 ====================
  const fetchInfluencers = async (username?: string) => {
    // 总是先检查当前标签页获取最新的用户名
    const detectedUsername = await checkCurrentTab(true); // 显示错误提示
    const targetUsername = username || detectedUsername || currentUsername;

    if (!targetUsername) {
      showError('请前往Instagram个人主页使用此功能');
      return;
    }

    setLoading(true);
    setHasSearched(true);

    try {
      const response = await instagramService.getRelatedInfluencers(targetUsername);

      // 设置集合信息
      setCurrentCollectionId(response.collection.id);
      setCurrentCollectionName(response.collection.name);

      // 设置博主列表
      setInfluencers(response.influencers);
      setCurrentIndex(0);

      // 加载收藏列表
      if (response.collection.id) {
        try {
          const favoritesList = await collectionService.getFavorites(response.collection.id);
          setFavorites(favoritesList);
        } catch (error) {
          console.error('Failed to load favorites:', error);
          setFavorites([]);
        }
      }

      // 添加到来源队列（如果不存在）
      setSourceUsernames(prev => {
        if (!prev.includes(targetUsername)) {
          return [...prev, targetUsername];
        }
        return prev;
      });

      // 刷新用户配额信息（API 调用成功后）
      await refreshUserInfo();

      // 加载完成后不自动打开第一个账号
    } catch (err) {
      console.error('获取相似账号失败:', err);
      console.log('错误类型检查:', err instanceof QuotaExceededError);
      console.log('错误构造函数:', err?.constructor?.name);

      // 检查是否是配额不足错误
      if (err instanceof QuotaExceededError) {
        console.log('捕获到配额不足错误 (fetchInfluencers):', err.quotaInfo);
        showError(err.message);
      } else {
        console.log('其他类型错误 (fetchInfluencers):', err);
        // 统一使用通知条显示错误，优先使用 API 服务已经处理的错误信息
        const errorMessage = (err as any)?.message || `获取相似账号失败。请确保后端服务在 ${ENV.apiUrl.replace('/api', '')} 运行`;
        showError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  // 添加获取更多账号的函数，不替换现有列表，而是添加到末尾（去重）
  const fetchMoreInfluencers = async () => {
    if (!currentInfluencer || loadingMore || !currentCollectionId) return;

    setLoadingMore(true);

    try {
      // 确保最小加载时间，让用户能看到加载动画
      const [response] = await Promise.all([
        instagramService.getRelatedInfluencers(
          currentInfluencer.username,
          currentCollectionId
        ),
        new Promise(resolve => setTimeout(resolve, 800)) // 最小800ms加载时间
      ]);

      // 直接使用后端返回的未查看博主列表（后端已处理去重和过滤）
      setInfluencers(response.influencers);

      // 重新加载收藏列表
      if (currentCollectionId) {
        try {
          const favoritesList = await collectionService.getFavorites(currentCollectionId);
          setFavorites(favoritesList);
        } catch (error) {
          console.error('Failed to reload favorites:', error);
        }
      }

      // 添加到来源队列（如果不存在）
      setSourceUsernames(prev => {
        if (!prev.includes(currentInfluencer.username)) {
          return [...prev, currentInfluencer.username];
        }
        return prev;
      });

      // 刷新用户配额信息（API 调用成功后）
      await refreshUserInfo();

    } catch (err) {
      console.error('获取更多相似账号失败:', err);

      // 检查是否是配额不足错误
      if (err instanceof QuotaExceededError) {
        console.log('捕获到配额不足错误:', err.quotaInfo); // 调试日志
        showError(err.message);
      } else {
        console.log('其他类型错误:', err); // 调试日志
        const errorMessage = (err as any)?.message || '获取更多推荐失败，请稍后重试';
        showError(errorMessage);
      }
    } finally {
      setLoadingMore(false);
    }
  };

  // ==================== 事件处理函数 ====================
  const handleNext = async () => {
    if (currentIndex < influencers.length - 1) {
      // 记录当前博主为已查看
      if (currentInfluencer && currentCollectionId) {
        try {
          await collectionService.recordUserAction(
            currentCollectionId,
            currentInfluencer.username,
            currentInfluencer.id,
            'viewed'
          );
        } catch (error) {
          console.error('Failed to record user action:', error);
        }
      }

      // 关闭之前打开的标签页，避免标签页累积
      if (lastOpenedTabId !== null) {
        try {
          await chrome.tabs.remove(lastOpenedTabId);
        } catch (e) {
          // 标签页可能已经关闭，忽略错误
          console.log('上一个标签页已关闭或不存在');
        }
      }

      const nextIndex = currentIndex + 1;
      const nextInfluencer = influencers[nextIndex];

      if (nextInfluencer) {
        if (preloadedTabId !== null && preloadedUsername === nextInfluencer.username) {
          try {
            await chrome.tabs.update(preloadedTabId, { active: true });
            setLastOpenedTabId(preloadedTabId);
            setPreloadedTabId(null);
            setPreloadedUsername(null);
            chrome.storage.local.set({ lastOpenedTabId: preloadedTabId });
            setCurrentIndex(nextIndex);
          } catch (e) {
            setCurrentIndex(nextIndex);
            await openInstagramProfile(nextInfluencer.username);
          }
        } else {
          setCurrentIndex(nextIndex);
          await openInstagramProfile(nextInfluencer.username);
        }
      }
    }
  };


  const preloadNextProfile = async (username: string) => {
    try {
      if (preloadedTabId !== null) {
        try {
          await chrome.tabs.remove(preloadedTabId);
        } catch (e) {
        }
      }

      const newTab = await chrome.tabs.create({
        url: `https://www.instagram.com/${username}/`,
        active: false 
      });

      if (newTab.id) {
        setPreloadedTabId(newTab.id);
        setPreloadedUsername(username);
      }
    } catch (error) {
      console.error('预加载Instagram页面时出错:', error);
    }
  };

  const openInstagramProfile = async (username: string) => {
    try {
      // 关闭之前打开的标签页
      if (lastOpenedTabId !== null) {
        try {
          await chrome.tabs.remove(lastOpenedTabId);
        } catch (e) {
          // 标签页可能已经关闭，忽略错误
          console.log('上一个标签页已关闭或不存在');
        }
      }

      if (preloadedTabId !== null) {
        try {
          await chrome.tabs.update(preloadedTabId, { active: true });
          setLastOpenedTabId(preloadedTabId);
          setPreloadedTabId(null);
          chrome.storage.local.set({ lastOpenedTabId: preloadedTabId });
          return;
        } catch (e) {
          console.log('预加载标签页不可用，创建新标签页');
        }
      }

      const newTab = await chrome.tabs.create({ url: `https://www.instagram.com/${username}/` });

      // 存储新的标签页ID
      if (newTab.id) {
        setLastOpenedTabId(newTab.id);
        chrome.storage.local.set({ lastOpenedTabId: newTab.id });
      }
    } catch (error) {
      console.error('打开Instagram个人资料页面时出错:', error);
      showError('无法打开新的标签页');
    }
  };

  // 认证处理函数
  const handleLoginSuccess = async (data: { user: any; hasCollections: boolean }) => {
    setUser(data.user);
    setHasCollections(data.hasCollections);
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  // 处理收藏功能 - 简单的收藏/取消收藏
  const handleToggleFavorite = async () => {
    if (!currentInfluencer || !currentCollectionId) return;

    const isFavorited = favorites.includes(currentInfluencer.username);

    try {
      if (isFavorited) {
        // 如果已收藏，移除收藏
        await collectionService.removeFromFavorites(currentCollectionId, currentInfluencer.username);
        setFavorites(prev => prev.filter(username => username !== currentInfluencer.username));
      } else {
        // 如果未收藏，添加到收藏
        await collectionService.addToFavorites(
          currentCollectionId,
          currentInfluencer.username,
          currentInfluencer.id
        );
        setFavorites(prev => [...prev, currentInfluencer.username]);
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      const errorMessage = (error as any)?.message || '收藏操作失败，请稍后重试';
      showError(errorMessage);
      throw error;
    }
  };



  // 处理集合切换
  const handleCollectionChange = async (collectionId: string) => {
    try {
      setLoading(true);

      const response = await collectionService.getUnviewedInfluencers(collectionId);

      setCurrentCollectionId(collectionId);
      setInfluencers(response.influencers);
      setCurrentIndex(0);
      setHasSearched(true); // 设置为已搜索状态

      // 获取收藏列表
      const collectionFavorites = await collectionService.getFavorites(collectionId);
      setFavorites(collectionFavorites);

      // 获取集合信息设置名称
      const collection = await collectionService.getCollection(collectionId);
      if (collection) {
        setCurrentCollectionName(collection.name);
      }

    } catch (error) {
      console.error('Failed to switch collection:', error);
      showError('切换集合失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理开启新集合
  const handleStartNewCollection = () => {
    // 清除所有状态，回到初始状态
    setCurrentCollectionId(null);
    setCurrentCollectionName('');
    setInfluencers([]);
    setCurrentIndex(0);
    setHasSearched(false);
    setFavorites([]);
    setImageLoadErrors(new Set());
    setSourceUsernames([]);

    // 重新检查当前标签页
    checkCurrentTab(false); // 不显示错误提示，因为这是内部操作
  };

  const currentInfluencer = influencers[currentIndex];

  // 认证加载中
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* 通知条 */}
        {notifications.map((notification) => (
          <NotificationBar
            key={notification.id}
            message={notification.message}
            type={notification.type}
            onClose={() => hideNotification(notification.id)}
          />
        ))}
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">检查登录状态...</p>
        </div>
      </div>
    );
  }

  // 未登录状态
  if (!user) {
    return (
      <div>
        {/* 通知条 */}
        {notifications.map((notification) => (
          <NotificationBar
            key={notification.id}
            message={notification.message}
            type={notification.type}
            onClose={() => hideNotification(notification.id)}
          />
        ))}
        <LoginForm onLoginSuccess={handleLoginSuccess} />
      </div>
    );
  }



  // 初始状态，未搜索
  if (!hasSearched && !loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
        {/* 通知条 */}
        {notifications.map((notification) => (
          <NotificationBar
            key={notification.id}
            message={notification.message}
            type={notification.type}
            onClose={() => hideNotification(notification.id)}
          />
        ))}
        <UserHeader
          user={user}
          currentCollectionId={currentCollectionId || undefined}
          currentCollectionName={currentCollectionName}
          onLogout={handleLogout}
          onCollectionChange={handleCollectionChange}
          onStartNewCollection={handleStartNewCollection}
        />
        <InitialState
          currentUsername={currentUsername}
          onSearchClick={() => fetchInfluencers()}
          hasCollections={hasCollections}
        />
      </div>
    );
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
        {/* 通知条 */}
        {notifications.map((notification) => (
          <NotificationBar
            key={notification.id}
            message={notification.message}
            type={notification.type}
            onClose={() => hideNotification(notification.id)}
          />
        ))}
        <UserHeader
          user={user}
          currentCollectionId={currentCollectionId || undefined}
          currentCollectionName={currentCollectionName}
          onLogout={handleLogout}
          onCollectionChange={handleCollectionChange}
          onStartNewCollection={handleStartNewCollection}
        />
        <LoadingState />
      </div>
    );
  }

  if (!currentInfluencer && hasSearched) {
    // 如果是从集合切换过来的，显示集合空状态
    if (currentCollectionId) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
          {/* 通知条 */}
          {notifications.map((notification) => (
            <NotificationBar
              key={notification.id}
              message={notification.message}
              type={notification.type}
              onClose={() => hideNotification(notification.id)}
            />
          ))}
          <UserHeader
            user={user}
            currentCollectionId={currentCollectionId || undefined}
            currentCollectionName={currentCollectionName}
            onLogout={handleLogout}
            onCollectionChange={handleCollectionChange}
            onStartNewCollection={handleStartNewCollection}
          />
          <CollectionEmptyState
            collectionName={currentCollectionName}
            onRetry={() => {
              if (currentCollectionId) {
                handleCollectionChange(currentCollectionId);
              }
            }}
            onSearchNew={() => {
              setHasSearched(false);
              setCurrentCollectionId(null);
              setCurrentCollectionName('');
              checkCurrentTab(false); // 不显示错误提示，因为这是内部操作
            }}
          />
        </div>
      );
    }

    // 否则显示普通的空状态
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
        {/* 通知条 */}
        {notifications.map((notification) => (
          <NotificationBar
            key={notification.id}
            message={notification.message}
            type={notification.type}
            onClose={() => hideNotification(notification.id)}
          />
        ))}
        <UserHeader
          user={user}
          currentCollectionId={currentCollectionId || undefined}
          currentCollectionName={currentCollectionName}
          onLogout={handleLogout}
          onCollectionChange={handleCollectionChange}
          onStartNewCollection={handleStartNewCollection}
        />
        <EmptyState
          onRetry={() => {
            setHasSearched(false);
            checkCurrentTab(false); // 不显示错误提示，因为这是内部操作
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      {/* 通知条 */}
      {notifications.map((notification) => (
        <NotificationBar
          key={notification.id}
          message={notification.message}
          type={notification.type}
          onClose={() => hideNotification(notification.id)}
        />
      ))}

      <UserHeader
        user={user}
        currentCollectionId={currentCollectionId || undefined}
        currentCollectionName={currentCollectionName}
        onLogout={handleLogout}
        onCollectionChange={handleCollectionChange}
        onStartNewCollection={handleStartNewCollection}
      />
      <div className="p-6 pt-12">
        <div className="max-w-md mx-auto space-y-6">
          <InfluencerStats
            currentIndex={currentIndex}
            totalCount={influencers.length}
            sourceUsernames={sourceUsernames}
            loadingMore={loadingMore}
          />

          <InfluencerCard
            influencer={currentInfluencer}
            imageLoadErrors={imageLoadErrors}
            onImageError={(username) => {
              setImageLoadErrors(prev => new Set([...prev, username]));
            }}
            onClick={openInstagramProfile}
            loadingMore={loadingMore}
          />

          <InfluencerControls
            currentIndex={currentIndex}
            totalCount={influencers.length}
            loadingMore={loadingMore}
            isFavorited={currentInfluencer ? favorites.includes(currentInfluencer.username) : false}
            onNext={handleNext}
            onLoadMore={fetchMoreInfluencers}
            onToggleFavorite={handleToggleFavorite}
          />
        </div>
      </div>
    </div>
  );
};

const root = ReactDOM.createRoot(document.getElementById('root')!);
root.render(<SidePanel />);