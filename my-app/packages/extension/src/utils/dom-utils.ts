// DOM 工具函数

// 等待元素出现 - 参考 efluns 插件实现
export function waitFor<T extends HTMLElement>(
  selector: string,
  options?: {
    timeout?: number;
    interval?: number;
    container?: Node;
    suppressLog?: boolean;
  }
): Promise<T> {
  const { container, timeout, interval, suppressLog } = {
    container: window.document,
    timeout: 30000,
    interval: 200,
    suppressLog: false,
    ...options
  };

  return new Promise<T>((resolve, reject) => {
    const checkExist = () => {
      // 确保 container 是 Document 或 Element 类型
      const searchContainer = container instanceof Document || container instanceof Element
        ? container
        : window.document;

      const element = searchContainer.querySelector(selector) as T | null;
      if (element) {
        !suppressLog && console.log(`Element with selector "${selector}" found`, element);
        resolve(element);
      } else {
        if (Date.now() - startTime > timeout) {
          reject(new Error(`Timeout: Element with selector "${selector}" not found within ${timeout}ms`));
        } else {
          setTimeout(checkExist, interval);
        }
      }
    };

    const startTime = Date.now();
    checkExist();
  });
}

// 检查是否在Instagram KOL页面 - 参考 efluns 插件实现
export function isInInsKOLPage(url?: string): boolean {
  const targetUrl = url || window.location.href;
  const urlObj = new URL(targetUrl);

  if (!['www.instagram.com', 'instagram.com'].includes(urlObj.hostname)) {
    return false;
  }

  const pathname = urlObj.pathname;
  const nonKOLPrefixPaths = ['/explore/', '/reels/', '/direct/', '/p/', '/stories/', '/accounts/'];
  const isNonKOLPath = pathname === '/' ||
    nonKOLPrefixPaths.some((prefix) => pathname.startsWith(prefix)) ||
    pathname.includes('/reel/');

  return !isNonKOLPath;
}

// 提取Instagram用户名 - 参考 efluns 插件实现
export function extractInsKOLHandler(url: string): string | null {
  const isKOLPage = isInInsKOLPage(url);

  if (isKOLPage) {
    const pathname = new URL(url).pathname;
    const [kolHandler = null] = pathname.split('/').filter((item) => item !== '');
    return kolHandler;
  } else {
    return null;
  }
}
