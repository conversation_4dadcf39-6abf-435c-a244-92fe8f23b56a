// 事件系统 - 跨世界通信

export type EventName = 'INS_AREA' | 'INSTAGRAM_POSTS' | 'INSTAGRAM_REELS' | 'INSTAGRAM_TAGGED';
export type EventData = any;

// 发送事件 (在 MAIN world)
export function sendEvent(name: EventName, data: EventData): void {
  console.log(`Sending event: ${name}`, data);
  window.dispatchEvent(new CustomEvent(name, { detail: data }));
}

// 监听事件 (在 ISOLATED world)
export function listenEvent(name: EventName, callback: (data: EventData) => void): () => void {
  const eventListener = ((event: CustomEvent) => {
    console.log(`Received event: ${name}`, event.detail);
    callback(event.detail);
  }) as EventListener;

  window.addEventListener(name, eventListener);
  
  // 返回取消监听的函数
  return () => {
    window.removeEventListener(name, eventListener);
  };
}
