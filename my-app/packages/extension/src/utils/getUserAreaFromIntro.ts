// 获取用户地区信息 - 核心功能

import { waitFor } from "./dom-utils";

// 缓存机制
const cache = new Map<string, string>();

// 获取缓存的地区信息
export function getUserAreaFromIntroCache(key: string): string | undefined {
    return cache.get(key);
}

// 设置缓存的地区信息
export function setUserAreaFromIntroCache(key: string, value: string): void {
    // LRU缓存策略，最多保存10个
    if (cache.size > 10) {
        const firstKey = cache.keys().next().value;
        if (firstKey) {
            cache.delete(firstKey);
        }
    }
    cache.set(key, value);
}

// 完全按照efluns的方式实现CSS隐藏
function hideIntroCss(): void {
    document.body.setAttribute("hideIntro", ""); // 空字符串，完全按照efluns
}

// 恢复简介弹窗的CSS
function resetIntroCss(): void {
    document.body.removeAttribute("hideIntro");
}

// 模拟点击打开简介
async function openIntro(): Promise<void> {
    const OPEN_INTRO_BTN_SELECTOR = "main section:nth-child(2)>div>div>div>div>a";
    const btn = await waitFor(OPEN_INTRO_BTN_SELECTOR);
    
    if (btn && btn instanceof HTMLElement) {
        btn.click();
        console.log('简介按钮已点击');
    } else {
        throw new Error('找不到简介按钮');
    }
}

// 模拟点击关闭简介
async function closeIntro(): Promise<void> {
    const CLOSE_INTRO_SELECTOR = '[role="dialog"]:has([data-bloks-name="bk.components.screen.Wrapper"]) button';
    const btn = await waitFor(CLOSE_INTRO_SELECTOR);
    
    if (btn && btn instanceof HTMLElement) {
        btn.click();
        console.log('简介弹窗已关闭');
    } else {
        console.warn('找不到关闭按钮，可能弹窗已经关闭');
    }
}

// 完全按照efluns的方式实现 - 无感知激活简介卡
export async function getUserAreaFromIntro(): Promise<void> {
    console.log('开始获取用户地区信息');

    hideIntroCss(); // 立即隐藏

    try {
        await openIntro();
        await closeIntro();
        await new Promise(resolve => setTimeout(resolve, 200)); // 只保留这一个等待
        window.scrollTo(0, 0); // 添加efluns的滚动重置
    } catch (error) {
        console.error('获取地区信息失败:', error);
    } finally {
        resetIntroCss(); // 立即恢复，不延迟
    }

    console.log('地区信息获取流程完成');
}
