// 图片代理工具，解决Instagram图片跨域问题

export function getProxiedImageUrl(originalUrl: string): string {
  if (!originalUrl) return '';
  
  // 方案1: 使用公共图片代理服务（生产环境建议使用自己的代理服务器）
  // 这里使用 wsrv.nl 作为示例，它是一个免费的图片代理和优化服务
  const proxiedUrl = `https://wsrv.nl/?url=${encodeURIComponent(originalUrl)}&w=300&h=300&fit=cover&a=attention`;
  
  return proxiedUrl;
}

// 备选方案：使用其他代理服务
export function getAlternativeProxyUrl(originalUrl: string): string {
  // 备选方案1: images.weserv.nl
  return `https://images.weserv.nl/?url=${encodeURIComponent(originalUrl)}&w=300&h=300&fit=cover`;
  
  // 备选方案2: 如果有自己的后端服务，可以这样：
  // return `http://localhost:8787/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;
}