/**
 * Instagram统计数据显示功能
 * 完全复制efluns的changeInsPosterData.ts功能
 */

// Instagram数据类型定义
interface InsCardData {
  code: string;
  like_count: number;
  comment_count: number;
  play_count?: number;
  created_time: number;

  isPinned?: boolean;
  is_paid_partnership?: boolean;
  is_shop?: boolean;
  username?: string;
}

interface InstagramChannelData {
  posts: InsCardData[];
  isFirst: boolean;
  median_number?: number;
}

// 图标定义 - 经典简洁图标设计，使用现代配色方案
const ICONS = {
    play:`<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.5446 11.085L5.92771 1.25462C5.56918 0.972722 5.08087 0.920508 4.67071 1.11995C4.26036 1.31936 4 1.73551 4 2.19163V21.8523C4 22.3085 4.26036 22.7246 4.67071 22.924C4.83606 23.0045 5.01427 23.0439 5.19141 23.0439C5.45352 23.0439 5.71373 22.9575 5.92771 22.7893L20.5446 12.959C20.8321 12.733 21 12.3876 21 12.022C21 11.6563 20.8322 11.311 20.5446 11.085ZM6.38315 19.4007V4.64328L17.8797 12.022L6.38315 19.4007Z" fill="white"/>
</svg>`,
    like:`<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" fill="white"/>
</svg>`,
    comment:`<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" fill="white"/>
</svg>`,
    er:`<svg width="16" height="10" viewBox="0 0 24 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13 2L3 14h9l-1 5 10-12h-9l1-5z" fill="white"/>
</svg>`
};

const hideIcon = `<svg t="1737287489092" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1899" width="16" height="16"><path d="M253.6 679.2l109.6-109.6C356 552 352 532.8 352 512c0-88 72-160 160-160 20.8 0 40 4 57.6 11.2l82.4-82.4C607.2 264.8 560 256 512 256c-168 0-329.6 106.4-384 256 24 65.6 68.8 123.2 125.6 167.2z" p-id="1900" fill="#ffffff"></path><path d="M416 512v4.8L516.8 416H512c-52.8 0-96 43.2-96 96zM770.4 344.8l163.2-163.2L888 136l-753.6 753.6 45.6 45.6 192.8-192.8A390.4 390.4 0 0 0 512 768c167.2 0 330.4-106.4 384.8-256-24-65.6-69.6-123.2-126.4-167.2zM512 672c-20 0-40-4-57.6-11.2l53.6-53.6h4.8c52.8 0 96-43.2 96-96v-4.8l53.6-53.6C668 472 672 492 672 512c0 88-72 160-160 160z" p-id="1901" fill="#ffffff"></path></svg>`;

// 数据存储相关
const STORAGE_KEY_PREFIX = 'instagram_data_';

export function getStoredInstagramData(url: string): InstagramChannelData | null {
  const storageKey = STORAGE_KEY_PREFIX + url;
  const data = sessionStorage.getItem(storageKey);
  return data ? JSON.parse(data) : null;
}

// 中位数计算函数 - 完全按照efluns的方式
function getMedian(arr: number[]): number {
  if (arr.length === 0) return 0;
  const sorted = arr.slice().sort((a, b) => a - b);
  const mid = Math.floor(sorted.length / 2);
  return sorted.length % 2 === 0
    ? (sorted[mid - 1] + sorted[mid]) / 2
    : sorted[mid];
}

// 数字格式化 - 完全按照efluns的方式
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// 时间格式化
function formatTimeAgo(timestamp: number): string {
  const now = Date.now() / 1000;
  const diff = now - timestamp;
  
  if (diff < 3600) {
    return Math.floor(diff / 60) + 'm';
  } else if (diff < 86400) {
    return Math.floor(diff / 3600) + 'h';
  } else if (diff < 604800) {
    return Math.floor(diff / 86400) + 'd';
  } else {
    return Math.floor(diff / 604800) + 'w';
  }
}

// 计算互动率
function computeEr(insCardData: InsCardData): string {
  if (!insCardData.play_count) return '';
  const er = (insCardData.like_count + insCardData.comment_count) / insCardData.play_count * 100;
  return er.toFixed(1) + '%';
}

// 创建显示元素 - 完全按照efluns的方式
function createElementStr(insCardData: InsCardData, className?: string): string {
  return `
    <div id="easy-kol-ins-poster-data" class="${className || ''}">
        ${insCardData.play_count ? `<div class="__ins_stat __ins_stat_er __efluns_er">${ICONS.er}${insCardData.like_count === 3 ?
            hideIcon : computeEr(insCardData)}</div>` : ''}
        ${insCardData.play_count ? `<span class="__ins_stat __efluns_plays">${ICONS.play}${formatNumber(insCardData.play_count)}</span>` : ''}
        <span class="__ins_stat __efluns_er_likes">${ICONS.like}${insCardData.like_count === 3 ?
            hideIcon : formatNumber(insCardData.like_count)}</span>
        <span class="__ins_stat __efluns_comments">${ICONS.comment}${formatNumber(insCardData.comment_count)}</span>
    </div>
    `;
}

function createCreatedTime(insCardData: InsCardData): string {
  return `<div class="__efluns_created_time">
    <span class="__efluns_time_badge">${formatTimeAgo(insCardData.created_time)}</span>
  </div>`;
}



function createUsername(insCardData: InsCardData): string {
  return insCardData.username ? `<span data-username="${insCardData.username}" class="__efluns_username">@${insCardData.username}</span>` : '';
}

// 监听Instagram卡片变化 - 完全按照efluns的方式
export function observeInsCards(): () => void {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node instanceof HTMLElement) {
          const isReelsPage = window.location.pathname.includes('/reels');
          
          if (isReelsPage) {
            // Reels 页面的选择器
            const cards = node.querySelectorAll(`main a[href*='/reel/']`);
            cards.forEach(card => {
              const href = (card as HTMLElement).getAttribute('href');
              if (!href) return;
              
              const code = href.split('/reel/')[1]?.split('/')[0];
              if (!code) return;
              
              const data = getStoredInstagramData(window.location.href)?.posts
                .find((post: InsCardData) => post.code === code);
              
              if (data) {
                const container = [...card.querySelectorAll("span.html-span")].at(-1)?.parentElement?.parentElement?.parentElement;
                if (container) {
                  container.innerHTML = createElementStr(data, "__ins_reels");
                }
              }
            });
          } else {
            // 普通帖子页面的选择器
            const cards = node.querySelectorAll("main>div>div:last-child a[href][role=link]");
            cards.forEach(card => {
              const href = (card as HTMLElement).getAttribute('href');
              if (!href) return;
              
              const code = href.split('/').slice(-2)[0];
              if (!code) return;
              
              const data = getStoredInstagramData(window.location.href)?.posts
                .find((post: InsCardData) => post.code === code);
              
              if (data) {
                card.insertAdjacentHTML('afterend', createElementStr(data));
                card.insertAdjacentHTML('beforeend', createCreatedTime(data));
                card.insertAdjacentHTML('afterend', createUsername(data));
              }
            });
          }
        }
      });
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  return () => observer.disconnect();
}



// Instagram统计计算函数 - 完全按照efluns的方式
export function getInstagramStatistics(posts: InsCardData[]): void {
  const datas = posts; // 简化版本，不做时间过滤
  const avgViewCount = getMedian(datas.map(item => item.play_count || 0));
  const argLikes = getMedian(datas.map(item => item.like_count || 0));
  const argComments = getMedian(datas.map(item => item.comment_count || 0));

  const isHideLikeKol = datas.filter(item => item.like_count === 3).length / datas.length > 0.3;

  changeInsMedianNumber({
    argLikeNumber: argLikes,
    argCommentNumber: argComments,
    argPlayCount: avgViewCount,
    isHideLikeKol
  });
}

// 中位数显示功能 - 完全按照efluns的方式（去除付费标记）
export function changeInsMedianNumber({argLikeNumber, argCommentNumber, argPlayCount, isHideLikeKol}: {
  argLikeNumber: number;
  argCommentNumber: number;
  argPlayCount: number;
  isHideLikeKol: boolean;
}) {
  const datas = getStoredInstagramData(window.location.href)?.posts;
  const isReelsPage = window.location.href.includes("/reels/");

  if (datas) {
    datas.forEach(item => {
      let card;
      const a = document.querySelector(`main a[href*='/${item.code}/']`);
      if (!a) return;

      if (isReelsPage) {
        card = a.parentElement?.parentElement;
      } else {
        card = a.parentElement;
      }
      if (!card) return;

      let medianElement = card.querySelector(".__efluns_median_number") as HTMLElement;
      if (!medianElement) {
        medianElement = document.createElement("div");
        medianElement.className = "__efluns_median_number";
        card.appendChild(medianElement);
      }

      const percent =
        isReelsPage ?
        item.play_count! / argPlayCount :
        !isHideLikeKol ?
        item.like_count / argLikeNumber :
        item.comment_count / argCommentNumber;

      // 完全按照efluns的付费标记逻辑
      if (item.is_paid_partnership) {
        medianElement.textContent = item.is_shop ? `${percent.toFixed(1)}X-Shop` : `${percent.toFixed(1)}X-Paid`;
        medianElement.setAttribute('data-type', item.is_shop ? "shop" : "paid");
      } else {
        const type = percent <= 0.4 ? 'bad' : percent >= 3.0 ? 'good' : 'normal';
        medianElement.textContent = `${percent.toFixed(1)}X`;
        medianElement.setAttribute('data-type', type);
      }

      if (isReelsPage) {
        medianElement.setAttribute('hideType', "playCount");
      } else {
        isHideLikeKol && medianElement.setAttribute('hideType', 'commentCount');
      }
    });
  }
}

// 监听统计计算事件 - 完全按照efluns的方式
function listenForStatsCalculation(): () => void {
  const handler = (event: CustomEvent) => {
    if (event.detail?.posts) {
      getInstagramStatistics(event.detail.posts);
    }
  };

  window.addEventListener('INSTAGRAM_STATS_CALCULATE', handler as EventListener);

  return () => {
    window.removeEventListener('INSTAGRAM_STATS_CALCULATE', handler as EventListener);
  };
}

// 监听来自early script的数据
export function listenFromInstagramEarlyScript(callback: (data: InstagramChannelData) => void): () => void {
  const handler = (event: MessageEvent) => {
    if (event.origin !== window.location.origin) return;

    if (event.data?.type === 'FROM_INSTAGRAM_EARLY_SCRIPT') {
      callback(event.data.data);
    }
  };

  window.addEventListener('message', handler);

  // 启动统计计算监听
  const cleanupStats = listenForStatsCalculation();

  // 返回清理函数
  return () => {
    window.removeEventListener('message', handler);
    cleanupStats();
  };
}
