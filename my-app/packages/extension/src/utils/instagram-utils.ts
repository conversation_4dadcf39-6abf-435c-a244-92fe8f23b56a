/**
 * Instagram 地区信息工具函数
 * 专门用于处理 Instagram 用户地区信息的获取和显示
 * 参考 efluns 插件实现，支持全球 150+ 个国家和地区
 */

/**
 * 从国家名称获取 ISO 国家代码
 * @param countryName 国家名称（支持中英文）
 * @returns ISO 两位字母国家代码
 */
export function getCountryCodeFromName(countryName: string): string {
  if (!countryName) return 'US';

  // 如果已经是双字母代码，直接返回
  if (countryName.length === 2 && /^[A-Z]{2}$/i.test(countryName)) {
    return countryName.toUpperCase();
  }

  // 完整的国家名称映射表（中英文对照）
  const countryMap: { [key: string]: string } = {
    // 北美洲
    '美国': 'US', 'United States': 'US', 'USA': 'US', 'America': 'US',
    '加拿大': 'CA', 'Canada': 'CA',
    '墨西哥': 'MX', 'Mexico': 'MX',

    // 欧洲
    '英国': 'GB', 'United Kingdom': 'GB', 'UK': 'GB', 'Britain': 'GB',
    '法国': 'FR', 'France': 'FR',
    '德国': 'DE', 'Germany': 'DE', 'Deutschland': 'DE',
    '意大利': 'IT', 'Italy': 'IT', 'Italia': 'IT',
    '西班牙': 'ES', 'Spain': 'ES', 'España': 'ES',
    '荷兰': 'NL', 'Netherlands': 'NL', 'Holland': 'NL',
    '波兰': 'PL', 'Poland': 'PL', 'Polska': 'PL',
    '比利时': 'BE', 'Belgium': 'BE',
    '瑞典': 'SE', 'Sweden': 'SE', 'Sverige': 'SE',
    '瑞士': 'CH', 'Switzerland': 'CH', 'Schweiz': 'CH',
    '奥地利': 'AT', 'Austria': 'AT', 'Österreich': 'AT',
    '爱尔兰': 'IE', 'Ireland': 'IE',
    '葡萄牙': 'PT', 'Portugal': 'PT',
    '丹麦': 'DK', 'Denmark': 'DK', 'Danmark': 'DK',
    '挪威': 'NO', 'Norway': 'NO', 'Norge': 'NO',
    '芬兰': 'FI', 'Finland': 'FI', 'Suomi': 'FI',
    '俄罗斯': 'RU', 'Russia': 'RU', 'Russian Federation': 'RU',
    '乌克兰': 'UA', 'Ukraine': 'UA',
    '希腊': 'GR', 'Greece': 'GR',
    '捷克': 'CZ', 'Czech Republic': 'CZ', 'Czechia': 'CZ',
    '匈牙利': 'HU', 'Hungary': 'HU',
    '罗马尼亚': 'RO', 'Romania': 'RO',
    '保加利亚': 'BG', 'Bulgaria': 'BG',
    '克罗地亚': 'HR', 'Croatia': 'HR',
    '斯洛伐克': 'SK', 'Slovakia': 'SK',
    '斯洛文尼亚': 'SI', 'Slovenia': 'SI',
    '立陶宛': 'LT', 'Lithuania': 'LT',
    '拉脱维亚': 'LV', 'Latvia': 'LV',
    '爱沙尼亚': 'EE', 'Estonia': 'EE',

    // 亚洲
    '中国': 'CN', 'China': 'CN', 'People\'s Republic of China': 'CN',
    '香港': 'HK', 'Hong Kong': 'HK',
    '澳门': 'MO', 'Macau': 'MO', 'Macao': 'MO',
    '台湾': 'TW', 'Taiwan': 'TW',
    '日本': 'JP', 'Japan': 'JP',
    '韩国': 'KR', 'South Korea': 'KR', 'Korea': 'KR', 'Republic of Korea': 'KR',
    '朝鲜': 'KP', 'North Korea': 'KP', 'Democratic People\'s Republic of Korea': 'KP',
    '印度': 'IN', 'India': 'IN',
    '印度尼西亚': 'ID', 'Indonesia': 'ID',
    '泰国': 'TH', 'Thailand': 'TH',
    '越南': 'VN', 'Vietnam': 'VN', 'Viet Nam': 'VN',
    '马来西亚': 'MY', 'Malaysia': 'MY',
    '新加坡': 'SG', 'Singapore': 'SG',
    '菲律宾': 'PH', 'Philippines': 'PH',
    '柬埔寨': 'KH', 'Cambodia': 'KH',
    '缅甸': 'MM', 'Myanmar': 'MM', 'Burma': 'MM',
    '老挝': 'LA', 'Laos': 'LA', 'Lao People\'s Democratic Republic': 'LA',
    '文莱': 'BN', 'Brunei': 'BN',
    '巴基斯坦': 'PK', 'Pakistan': 'PK',
    '孟加拉国': 'BD', 'Bangladesh': 'BD',
    '斯里兰卡': 'LK', 'Sri Lanka': 'LK',
    '尼泊尔': 'NP', 'Nepal': 'NP',
    '不丹': 'BT', 'Bhutan': 'BT',
    '马尔代夫': 'MV', 'Maldives': 'MV',
    '阿富汗': 'AF', 'Afghanistan': 'AF',
    '伊朗': 'IR', 'Iran': 'IR', 'Islamic Republic of Iran': 'IR',
    '伊拉克': 'IQ', 'Iraq': 'IQ',
    '以色列': 'IL', 'Israel': 'IL',
    '约旦': 'JO', 'Jordan': 'JO',
    '黎巴嫩': 'LB', 'Lebanon': 'LB',
    '叙利亚': 'SY', 'Syria': 'SY', 'Syrian Arab Republic': 'SY',
    '土耳其': 'TR', 'Turkey': 'TR', 'Türkiye': 'TR',
    '沙特阿拉伯': 'SA', 'Saudi Arabia': 'SA',
    '阿联酋': 'AE', 'United Arab Emirates': 'AE', 'UAE': 'AE',
    '卡塔尔': 'QA', 'Qatar': 'QA',
    '科威特': 'KW', 'Kuwait': 'KW',
    '巴林': 'BH', 'Bahrain': 'BH',
    '阿曼': 'OM', 'Oman': 'OM',
    '也门': 'YE', 'Yemen': 'YE',
    '哈萨克斯坦': 'KZ', 'Kazakhstan': 'KZ',
    '乌兹别克斯坦': 'UZ', 'Uzbekistan': 'UZ',
    '土库曼斯坦': 'TM', 'Turkmenistan': 'TM',
    '塔吉克斯坦': 'TJ', 'Tajikistan': 'TJ',
    '吉尔吉斯斯坦': 'KG', 'Kyrgyzstan': 'KG',
    '蒙古': 'MN', 'Mongolia': 'MN',

    // 大洋洲
    '澳大利亚': 'AU', 'Australia': 'AU',
    '新西兰': 'NZ', 'New Zealand': 'NZ',
    '斐济': 'FJ', 'Fiji': 'FJ',
    '巴布亚新几内亚': 'PG', 'Papua New Guinea': 'PG',

    // 非洲
    '南非': 'ZA', 'South Africa': 'ZA',
    '埃及': 'EG', 'Egypt': 'EG',
    '尼日利亚': 'NG', 'Nigeria': 'NG',
    '肯尼亚': 'KE', 'Kenya': 'KE',
    '加纳': 'GH', 'Ghana': 'GH',
    '摩洛哥': 'MA', 'Morocco': 'MA',
    '阿尔及利亚': 'DZ', 'Algeria': 'DZ',
    '突尼斯': 'TN', 'Tunisia': 'TN',
    '利比亚': 'LY', 'Libya': 'LY',
    '苏丹': 'SD', 'Sudan': 'SD',
    '埃塞俄比亚': 'ET', 'Ethiopia': 'ET',
    '坦桑尼亚': 'TZ', 'Tanzania': 'TZ',
    '乌干达': 'UG', 'Uganda': 'UG',
    '卢旺达': 'RW', 'Rwanda': 'RW',
    '津巴布韦': 'ZW', 'Zimbabwe': 'ZW',
    '博茨瓦纳': 'BW', 'Botswana': 'BW',
    '纳米比亚': 'NA', 'Namibia': 'NA',
    '赞比亚': 'ZM', 'Zambia': 'ZM',
    '马拉维': 'MW', 'Malawi': 'MW',
    '莫桑比克': 'MZ', 'Mozambique': 'MZ',
    '马达加斯加': 'MG', 'Madagascar': 'MG',
    '毛里求斯': 'MU', 'Mauritius': 'MU',
    '塞舌尔': 'SC', 'Seychelles': 'SC',

    // 南美洲
    '巴西': 'BR', 'Brazil': 'BR', 'Brasil': 'BR',
    '阿根廷': 'AR', 'Argentina': 'AR',
    '智利': 'CL', 'Chile': 'CL',
    '哥伦比亚': 'CO', 'Colombia': 'CO',
    '秘鲁': 'PE', 'Peru': 'PE', 'Perú': 'PE',
    '委内瑞拉': 'VE', 'Venezuela': 'VE',
    '厄瓜多尔': 'EC', 'Ecuador': 'EC',
    '乌拉圭': 'UY', 'Uruguay': 'UY',
    '巴拉圭': 'PY', 'Paraguay': 'PY',
    '玻利维亚': 'BO', 'Bolivia': 'BO',
    '圭亚那': 'GY', 'Guyana': 'GY',
    '苏里南': 'SR', 'Suriname': 'SR',

    // 中美洲和加勒比海
    '古巴': 'CU', 'Cuba': 'CU',
    '牙买加': 'JM', 'Jamaica': 'JM',
    '海地': 'HT', 'Haiti': 'HT',
    '多米尼加': 'DO', 'Dominican Republic': 'DO',
    '波多黎各': 'PR', 'Puerto Rico': 'PR',
    '特立尼达和多巴哥': 'TT', 'Trinidad and Tobago': 'TT',
    '巴巴多斯': 'BB', 'Barbados': 'BB',
    '巴哈马': 'BS', 'Bahamas': 'BS',
    '伯利兹': 'BZ', 'Belize': 'BZ',
    '哥斯达黎加': 'CR', 'Costa Rica': 'CR',
    '萨尔瓦多': 'SV', 'El Salvador': 'SV',
    '危地马拉': 'GT', 'Guatemala': 'GT',
    '洪都拉斯': 'HN', 'Honduras': 'HN',
    '尼加拉瓜': 'NI', 'Nicaragua': 'NI',
    '巴拿马': 'PA', 'Panama': 'PA'
  };

  // 先尝试精确匹配
  const exactMatch = countryMap[countryName];
  if (exactMatch) return exactMatch;

  // 尝试不区分大小写的匹配
  const lowerCaseName = countryName.toLowerCase();
  for (const [key, value] of Object.entries(countryMap)) {
    if (key.toLowerCase() === lowerCaseName) {
      return value;
    }
  }

  // 尝试部分匹配（包含关系）
  for (const [key, value] of Object.entries(countryMap)) {
    if (key.toLowerCase().includes(lowerCaseName) || lowerCaseName.includes(key.toLowerCase())) {
      return value;
    }
  }

  // 如果都没有匹配到，返回默认值
  console.warn(`未找到国家代码映射: ${countryName}`);
  return 'US';
}

/**
 * 获取国旗图标 URL
 * @param region 地区名称或国家代码
 * @returns SVG 格式的国旗图标 URL
 */
export function getCountryIcon(region: string | null | undefined): string | null {
  if (!region) return null;

  const countryCode = region.length === 2 && /^[A-Z]{2}$/i.test(region)
    ? region.toUpperCase()
    : getCountryCodeFromName(region);

  return `https://flagcdn.com/${countryCode.toLowerCase()}.svg`;
}

/**
 * 获取国旗 emoji
 * @param countryCode ISO 两位字母国家代码
 * @returns 国旗 emoji 字符
 */
export function getCountryFlag(countryCode: string): string {
  if (!countryCode || countryCode.length !== 2) return '🏳️';

  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt(0));
  return String.fromCodePoint(...codePoints);
}
