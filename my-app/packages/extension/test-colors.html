<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Stats Color Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .color-demo {
            display: inline-block;
            margin: 10px;
            position: relative;
        }
        /* 复制我们的样式 */
        .__efluns_median_number{
            border-radius: 10px;
            color: #fff;
            padding: 0 5px;
            font-size: 13px;
            cursor: help;
            display: inline-block;
            margin: 5px;
        }

        /* 烂 */
        .__efluns_median_number[data-type="bad"]{
            background-color: #34AEFF;
        }

        /* 普通，无色 */
        .__efluns_median_number[data-type="normal"]{
            background-color: transparent;
            color: #333;
            border: 1px solid #ccc;
        }

        /* 好 */
        .__efluns_median_number[data-type="good"]{
            background-color: #FE2C55;
        }

        /* 付费 */
        .__efluns_median_number[data-type="paid"]{
            background-color: #34C759;
        }

        /* 店铺 */
        .__efluns_median_number[data-type="shop"]{
            background-color: #FF9500;
        }
    </style>
</head>
<body>
    <h1>Instagram Stats Color Test - 完全对齐efluns</h1>
    
    <div class="test-container">
        <h2>颜色测试 - 与efluns完全一致</h2>
        <p>以下是各种类型的颜色显示：</p>
        
        <div class="color-demo">
            <span class="__efluns_median_number" data-type="bad">0.3X</span>
            <span>烂 (Bad) - 蓝色 #34AEFF</span>
        </div>
        
        <div class="color-demo">
            <span class="__efluns_median_number" data-type="normal">1.5X</span>
            <span>普通 (Normal) - 透明</span>
        </div>
        
        <div class="color-demo">
            <span class="__efluns_median_number" data-type="good">4.2X</span>
            <span>好 (Good) - 红色 #FE2C55</span>
        </div>
        
        <div class="color-demo">
            <span class="__efluns_median_number" data-type="paid">2.1X-Paid</span>
            <span>付费 (Paid) - 绿色 #34C759</span>
        </div>
        
        <div class="color-demo">
            <span class="__efluns_median_number" data-type="shop">1.8X-Shop</span>
            <span>店铺 (Shop) - 橙色 #FF9500</span>
        </div>
    </div>

    <div class="test-container">
        <h2>修复说明</h2>
        <ul>
            <li>✅ 添加了shop类型的橙色配置 (#FF9500)</li>
            <li>✅ 恢复了付费标记逻辑 (is_paid_partnership)</li>
            <li>✅ 添加了is_shop字段支持</li>
            <li>✅ 逻辑完全对齐efluns的处理方式</li>
            <li>✅ 只显示点赞、播放和评论数据</li>
            <li>✅ 颜色配置与efluns完全一致</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>逻辑说明</h2>
        <p><strong>付费合作判断：</strong></p>
        <pre>
if (item.is_paid_partnership) {
    medianElement.textContent = item.is_shop ? `${percent.toFixed(1)}X-Shop` : `${percent.toFixed(1)}X-Paid`;
    medianElement.setAttribute('data-type', item.is_shop ? "shop" : "paid");
} else {
    const type = percent <= 0.4 ? 'bad' : percent >= 3.0 ? 'good' : 'normal';
    medianElement.textContent = `${percent.toFixed(1)}X`;
    medianElement.setAttribute('data-type', type);
}
        </pre>
    </div>
</body>
</html>
