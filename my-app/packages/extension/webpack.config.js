const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = {
  entry: {
    sidepanel: './src/sidepanel/sidepanel.tsx',
    content: './src/content/content.ts',
    'instagram-early-script': './src/content/instagram-early-script.ts',
    background: './src/background/background.ts',
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].js',
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader'
        ]
      }
    ]
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@my-app/shared': path.resolve(__dirname, '../shared/src')
    }
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './src/sidepanel/sidepanel.html',
      filename: 'sidepanel.html',
      chunks: ['sidepanel']
    }),
    new MiniCssExtractPlugin({
      filename: '[name].css'
    }),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: 'public/manifest.json',
          to: 'manifest.json'
        },
        {
          from: 'public/*.png',
          to: '[name][ext]',
          noErrorOnMissing: true
        },
        {
          from: 'src/content/instagram-injected-script.js',
          to: 'instagram-injected-script.js'
        },
        {
          from: 'src/content/instagram-early.css',
          to: 'instagram-early.css'
        }
      ]
    })
  ],
  optimization: {
    splitChunks: false
  }
};