# @my-app/shared

## 概述

这个包包含了项目中各个包之间共享的 TypeScript 类型定义，确保跨包的类型一致性。

## 主要作用

1. **类型统一** - 提供统一的类型定义，避免在不同包中重复定义相同的类型
2. **接口规范** - 确保前端、后端、扩展等不同包使用相同的数据结构
3. **开发效率** - 减少类型定义的重复工作，提高开发效率

## 当前包含的类型

### API 类型 (`types/api.ts`)
- `ApiRequest` - API 请求接口

### 用户类型 (`types/user.ts`)
- `User` - 用户基本信息接口
- `UserPreferences` - 用户偏好设置接口

## 使用方式

```typescript
import { User, ApiRequest } from '@my-app/shared';

// 使用共享的类型定义
const user: User = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>'
};
```

## 注意事项

- 由于构建工具的限制，某些包（如 Cloudflare Workers）可能无法直接导入此包
- 在这种情况下，建议在对应包中复制类型定义以保持一致性
- 修改类型定义时，请确保同步更新所有使用该类型的包

## 构建

```bash
pnpm build
```

## 开发

```bash
pnpm dev  # 监听模式构建
```
