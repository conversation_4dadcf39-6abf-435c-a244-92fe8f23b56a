{"name": "@my-app/shared", "version": "1.0.0", "description": "Shared types for cross-package consistency", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "eslint": "^8.56.0", "typescript": "^5.3.3"}}