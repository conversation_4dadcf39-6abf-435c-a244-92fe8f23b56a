{"name": "@my-app/website", "version": "1.0.0", "description": "WaveInflu official website", "private": true, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "deploy": "wrangler pages deploy build --project-name=waveinflu", "preview": "npx serve build -p 8080"}, "keywords": ["waveinflu", "instagram", "influencers", "chrome-extension", "website"], "author": "WaveInflu Team", "license": "MIT", "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.309.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "tailwind-merge": "^2.2.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@craco/craco": "^7.1.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}