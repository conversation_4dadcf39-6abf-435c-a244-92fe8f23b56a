import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';

const Features: React.FC = () => {
  const features = [
    {
      title: "Smart Discovery",
      description: "Find relevant influencers based on engagement, followers, and content quality with our advanced algorithms.",
      icon: "🔍"
    },
    {
      title: "Collection Management",
      description: "Organize influencers into custom collections for different campaigns and projects.",
      icon: "📁"
    },
    {
      title: "Data Export",
      description: "Export influencer data in multiple formats (CSV, JSON) for further analysis and outreach.",
      icon: "📊"
    },
    {
      title: "Real-time Analytics",
      description: "Get up-to-date engagement metrics and follower counts for better decision making.",
      icon: "📈"
    },
    {
      title: "Chrome Integration",
      description: "Seamlessly integrated with Instagram through our Chrome extension for instant access.",
      icon: "🌐"
    },
    {
      title: "Privacy First",
      description: "We respect privacy and only collect publicly available data with transparent practices.",
      icon: "🔒"
    }
  ];

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Powerful Features
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to discover, analyze, and connect with Instagram influencers
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 bg-white border-0 shadow-lg"
            >
              <CardHeader className="text-center">
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <CardTitle className="text-xl font-semibold text-gray-900">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600 text-center leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
