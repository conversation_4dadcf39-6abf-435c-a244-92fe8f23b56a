{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "sourceMap": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@my-app/shared": ["packages/shared/src"], "@my-app/shared/*": ["packages/shared/src/*"]}}, "exclude": ["node_modules", "dist", "build"]}